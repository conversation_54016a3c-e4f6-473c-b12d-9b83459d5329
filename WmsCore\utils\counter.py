import datetime

from WmsCore.model.counter import CounterTable
from common.logger import logger as log

#short_name_mgr = None;
class Counter:
    @staticmethod
    def SysDayCounter(name:str, prefetch:bool=False) -> str:
        short_name = short_name_mgr.Name2Short(name)
        return Counter.DayCounter(short_name, prefetch)

    @staticmethod
    def SysMonthCounter(name:str, prefetch:bool=False) -> str:
        short_name = short_name_mgr.Name2Short(name)
        return Counter.MonthCounter(short_name, prefetch)

    @staticmethod
    def SysNextCounter(name:str, mid_key:str, prefetch:bool=False) -> str:
        short_name = short_name_mgr.Name2Short(name)
        return Counter.NextCounter(short_name, mid_key, prefetch)

    @staticmethod
    def SysCounterInt(name:str, prefetch:bool=False) -> int:
        short_name = short_name_mgr.Name2Short(name)
        return Counter.CounterInt(short_name, prefetch)

    @staticmethod
    def DayCounter(prefix:str, prefetch:bool=False) -> str:
        """
        return {prefix}_{YYMMDD}_{counter_value}
        """
        date = datetime.datetime.now().strftime("%y%m%d")
        return Counter.NextCounter(prefix, date, prefetch)

    @staticmethod
    def MonthCounter(prefix:str, prefetch:bool=False) -> str:
        """
        return {prefix}_{YYMM}_{counter_value}
        """

    @staticmethod
    def NextCounter(prefix:str, mid_key:str, prefetch:bool=False) -> str:
        """
        return {prefix}_{counter_value}
        """
        counter, created = CounterTable.objects.get_or_create(prefix=prefix)
        if mid_key:
            if not counter.mid_key or counter.mid_key != mid_key:
                log.debug(f"mid_key: {mid_key} counter: {counter} reset to 0")
                counter.mid_key = mid_key
                counter.counter_value = 0
        else:
            mid_key = counter.mid_key;
        if mid_key:
            mid_key = mid_key+"_";
        else:
            mid_key = ""
        counter_value = counter.counter_value + 1;
        if not prefetch:
            counter.counter_value = counter_value
            counter.save()

        return f"{prefix}_{mid_key}{str(counter_value).zfill(3)}"


    @staticmethod
    def CounterInt(prefix:str, prefetch:bool=False) -> int:
        """
        return {prefix}_{counter_value}
        """
        # 前缀加 __ ，避免和数据库中的行冲突
        prefix = "__" + prefix ;
        counter, created = CounterTable.objects.get_or_create(prefix=prefix)
        counter_value = counter.counter_value + 1;
        if not prefetch:
            counter.counter_value = counter_value
            counter.save()
        return counter_value


CounterShortName = {
    "PurchaseIn": "PI", #采购入库
    "PurchaseOut": "PO", #采购出库
    "AdjustmentIn": "ADI", #调拨入库
    "AdjustmentOut": "ADO", #调拨出库
    "AssemblyIn": "AII", #组装入库
    "AssemblyOut": "AIO", #组装出库
    "RepairIn": "RPI", #维修入库
    "RepairOut": "RPO", #维修出库
    "ReturnIn": "RIN", #退货入库
    "ReturnOut": "ROT", #退货出库
    "StockReturn": "SRT", #库存退货
    "CostAllocation": "CAL", #费用分摊
    "SalesOrder": "SO", #销售订单
    "SalesInvoice": "SI", #销货单
    "RetailOrder": "RO", #零售订单
    "SalesOut": "SOUT", #销售出库
    "SalesReturn": "SRET", #销售退货
    "Receipt": "REC", #收款单
    "ReceiptRecord": "RECR", #收款记录
    "InitialIn": "INI",
    "AccountsPayable": "ACP", #应付账单
    "AccountsReceivable": "ACR", #应收账单
}

class ShortName:
    name2short = None;
    short2name = None;
    instance = None;
    # 单例模式
    def __new__(cls):
        if cls.instance is None:
            cls.instance = super(ShortName, cls).__new__(cls)
            cls.instance.InitNames()
        return cls.instance
    def __init__(self):
        pass

    def InitNames(self):
        self.name2short = {}
        self.short2name = {}
        for name, short in CounterShortName.items():
            lower_name = name.lower()
            upper_short = short.upper()
            self.name2short[lower_name] = upper_short
            self.short2name[upper_short] = lower_name
        #log.debug(f"ShortName: {self.name2short}")

    def Name2Short(self, name:str) -> str:
        name = name.lower()
        if name not in self.name2short:
            log.error(f"ShortName: {name} not found")
            raise Exception(f"ShortName: {name} not found")
            #return "Exception_" + name
        return self.name2short[name]
    def Short2Name(self, short:str) -> str:
        short = short.upper()
        if short not in self.short2name:
            log.error(f"ShortName: {short} not found")
            raise Exception(f"ShortName: {short} not found")
            #return "Exception_" + short
        return self.short2name[short]

short_name_mgr = ShortName();
