# Controller 字段移除总结

## 问题描述
用户决定不使用 `controller` 字段，但代码中仍然在尝试使用它，导致数据库错误：
```
django.db.utils.ProgrammingError: column "controller_id" of relation "WmsCore_accountsreceivable" does not exist
```

## 修复方案

### 1. 模型层修改
在 `WmsCore/models.py` 中，用户已经注释掉了 `controller` 字段：
```python
# controller = TenantForeignKey('ZTUser', on_delete=models.PROTECT, verbose_name='操作者', related_name='accounts_receivable_controller')
```

### 2. 序列化器修改
在 `WmsCore/control/Accounts.py` 中：

#### 移除 controller_name 字段声明
```python
# 移除前
controller_name = serializers.CharField(source='controller.name', read_only=True)

# 移除后
# 不再声明此字段
```

#### 更新 fields 列表
```python
# 移除前
fields = [
    'id', 'receivable_no', 'source_order_no', 'source_type', 'source_type_display',
    'customer', 'customer_name', 'receivable_amount', 'order_amount', 'remaining_amount',
    'payment_status', 'payment_status_display', 'due_date', 'create_date', 'update_date',
    'sales_order', 'sales_out', 'sales_return',
    'handler', 'handler_name', 'controller_name', 'remark',  # 包含 controller_name
    'is_overdue', 'overdue_days'
]

# 移除后
fields = [
    'id', 'receivable_no', 'source_order_no', 'source_type', 'source_type_display',
    'customer', 'customer_name', 'receivable_amount', 'order_amount', 'remaining_amount',
    'payment_status', 'payment_status_display', 'due_date', 'create_date', 'update_date',
    'sales_order', 'sales_out', 'sales_return',
    'handler', 'handler_name', 'remark',  # 移除 controller_name
    'is_overdue', 'overdue_days'
]
```

### 3. 业务逻辑修改

#### AccountsAdmin.create_receivable 方法
在 `WmsCore/admin/Accounts.py` 中：

```python
# 修改前
def create_receivable(customer, order_amount, receivable_amount, remaining_amount, handler, source_type, source_order_id, remark, controller=None, **foreign_order):
    # 复杂的controller字段检查逻辑
    
# 修改后
def create_receivable(customer, order_amount, receivable_amount, remaining_amount, handler, source_type, source_order_id, remark, **foreign_order):
    # 简化的创建逻辑，不包含controller字段
```

#### 调用方修改
移除所有调用 `create_receivable` 时的 `controller` 参数：

**SalesOrder.py** (2处调用)：
```python
# 修改前
accounts_receivable = AccountsAdmin.create_receivable(
    customer=sales_order.customer,
    # ... 其他参数
    handler=request.zt_user,
    controller=request.zt_user,  # 移除此行
    # ... 其他参数
)

# 修改后
accounts_receivable = AccountsAdmin.create_receivable(
    customer=sales_order.customer,
    # ... 其他参数
    handler=request.zt_user,
    # ... 其他参数
)
```

**SalesOut.py** (3处调用)：
- 零售订单创建
- 批发订单草稿创建
- 批发订单正式创建

所有调用都移除了 `controller=request.zt_user` 参数。

## 修复结果

### 数据结构简化
现在 `AccountsReceivable` 模型只有：
- `handler`: 经办人（保留）
- ~~`controller`: 操作者（移除）~~

### API 返回数据调整
序列化器返回的数据不再包含 `controller_name` 字段，只包含：
- `handler`: 经办人ID
- `handler_name`: 经办人姓名

### 业务逻辑简化
- 创建应收账本记录时只需要指定 `handler`
- 不再需要区分经办人和操作者
- 简化了参数传递和数据库操作

## 测试建议

1. **功能测试**：
   - 测试销售订单的创建和更新
   - 测试销售出库的创建（零售和批发）
   - 确认应收账本记录正确创建

2. **数据完整性测试**：
   - 确认序列化器返回数据不包含 `controller_name`
   - 确认数据库操作不再尝试访问 `controller_id` 字段

3. **前端兼容性测试**：
   - 确认前端不依赖 `controller_name` 字段
   - 如果前端有使用，需要相应调整

## 注意事项

- 此修改简化了数据模型，去除了经办人和操作者的区分
- 如果将来需要重新引入 `controller` 字段，需要：
  1. 取消注释模型中的字段定义
  2. 创建数据库迁移
  3. 恢复序列化器中的字段声明
  4. 恢复业务逻辑中的参数传递
