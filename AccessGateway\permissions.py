
from rest_framework import permissions
from common.logger import logger as log;

class IsPlatformAccount(permissions.BasePermission):
    """
    自定义权限类，检查用户是否是平台账号
    """
    def has_permission(self, request, view):
        # 我的 enterprise_id 跟 请求的 enterprise_id 要一致

        # 只要通过了认证，用户存在即可
        #log.debug(f"request.user: {request.user is None} request.user_id: {request.user_id}")
        log.debug(f"检查用户是否是平台账号: {bool(request.user and hasattr(request.user, 'id'))}")
        return bool(request.user and hasattr(request.user, 'id'))
    