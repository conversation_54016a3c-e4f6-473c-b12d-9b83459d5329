import time
import jwt
from conf.config import cnf

class JWTServiceAuth():
    """JWT认证类"""
    @staticmethod 
    def Encode(data:dict):
        token = jwt.encode(
            data,
            cnf.SECRET_KEY,
            algorithm=cnf.ALGORITHM
        )
        return token;

    @staticmethod
    def Decode(token:str):
        return jwt.decode(token, cnf.SECRET_KEY, algorithms=[cnf.ALGORITHM])
    
    @staticmethod
    def NewToken(check_key:str):
        # 生成新 token 加上当前时间戳，生成的 token 校验的时候，同时校验时间戳不能超过 5 分钟
        return JWTServiceAuth.Encode({
            'check_key': check_key,
            'timestamp': int(time.time())
        })

    @staticmethod
    def CheckToken(token:str, check_key:str, check_key2:str):
        info = JWTServiceAuth.Decode(token)
        # 设置2个校验key轮转，用来支持动态更新校验 key
        if info.get('check_key') != check_key and info.get('check_key') != check_key2:
            raise Exception('无效的认证令牌')
        if int(time.time()) - info.get('timestamp') > 300:
            raise Exception('认证令牌已过期')
        return True;

