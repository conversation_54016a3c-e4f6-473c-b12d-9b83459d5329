from decimal import Decimal, ROUND_HALF_UP
from math import e

from django.db import models, transaction
from django.utils import timezone
from rest_framework import status, serializers
from rest_framework.decorators import action
from django.utils.translation import gettext_lazy as _
from AccessGateway.control.auth import make_response
from WmsCore.admin.warehouse import WarehouseAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import SalesReturn, SalesReturnItem, SalesOut, SalesOutItem, Unit, Item, Warehouse, \
    PaymentMethod, ZTUser, Customer, Stock, AccountsPayable, Payment
from WmsCore.admin.Accounts import AccountsAdmin
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.tools import CommonTools

from WmsCore.utils.submission import prevent_duplicate_submission
from common.exception import WmsException, ErrorCode
from common.logger import logger as log
from common.translation import _T


@SerializerCollector(alias="销售退货物品")
class SalesReturnItemSerializer(serializers.ModelSerializer):
    """销售退货物品序列化器"""
    item = serializers.PrimaryKeyRelatedField(queryset=Item.objects.all(), required=True, write_only=True,label="物品id")
    item_name = serializers.CharField(source='item.name', read_only=True,label="物品名称")
    unit = serializers.PrimaryKeyRelatedField(queryset=Unit.objects.all(), required=True,label="单位id")
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True,label="单位名称")
    price = serializers.DecimalField(max_digits=30, decimal_places=8,label="价格")
    quantity = serializers.DecimalField(max_digits=30, decimal_places=8, required=True,label="数量")

    class Meta:
        model = SalesReturnItem
        fields = ['id', 'item', 'item_name', 'unit', 'unit_name', 'quantity', 'price']

    def validate(self, attrs):
        item = attrs.get('item')
        unit = attrs.get('unit')
        if unit.item_id != item.id:
            raise serializers.ValidationError({"unit": f"单位【{unit}】不属于物品【{item}】，请重新选择单位"})
        return attrs

@SerializerCollector(alias="销售退货")
class SalesReturnSerializer(serializers.ModelSerializer):
    """销售退货序列化器"""
    items = SalesReturnItemSerializer(many=True, required=True, allow_empty=False, write_only=True)
    is_draft = serializers.BooleanField(required=True, write_only=True,label="是否草稿")
    discount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False,label="折扣")
    sales_out = serializers.PrimaryKeyRelatedField(queryset=SalesOut.objects.all(), required=True,label="销售出库单id")
    warehouse = serializers.PrimaryKeyRelatedField(queryset=Warehouse.objects.all(), required=False,label="仓库id")
    payment_method = serializers.PrimaryKeyRelatedField(queryset=PaymentMethod.objects.all(), required=False,label="付款方式")
    total_amount = serializers.DecimalField(max_digits=30, decimal_places=8, required=True,label="总价,优惠后")
    handler = serializers.PrimaryKeyRelatedField(queryset=ZTUser.objects.all(), required=False,allow_null=True,label="经办人id")
    customer = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), required=True,label="客户id")
    short_desc = serializers.CharField(read_only=True,label="简要描述")
    order_no = serializers.CharField(read_only=True,label="退货单编号")
    order_type = serializers.ChoiceField(choices=SalesReturn.ORDER_TYPE_CHOICES, required=False, default='wholesale',label="订单类型")
    class Meta:
        model = SalesReturn
        fields = ['is_draft', 'id', 'sales_out', 'items', 'discount', 'handler', 'customer',
                  'warehouse', 'payment_method', 'total_amount', 'short_desc', 'order_no', 'order_type', ]
        read_only_fields = ('id', 'created_at', 'updated_at', 'total_cost', 'final_cost', 'short_desc', 'order_no')
        write_only_fields = ['items']

    def validate(self, attrs):
        if not attrs.get('is_draft'):
           if not attrs.get('payment_method'):
               raise serializers.ValidationError({
                   "payment_method": ["非暂存状态，请选择付款方式"]
               })
        return attrs

@SerializerCollector(alias="销售退货列表")
class SalesReturnListSerializer(serializers.ModelSerializer):
    """销售退货列表序列化器"""
    customer_name = serializers.CharField(source='customer.name', read_only=True, label="客户名称")
    sales_out_id = serializers.CharField(source='sales_out.id', read_only=True, label="销售出库单ID")
    sales_out_order_id = serializers.CharField(source='sales_out.order_id', read_only=True, label="销售出库单编号")
    handler_name = serializers.CharField(source='handler.name', read_only=True, label="经办人姓名")
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True, label="仓库名称")
    controller_name = serializers.CharField(source='controller.name', read_only=True, label="操作者姓名")
    short_desc = serializers.CharField(read_only=True, label="简要描述")
    order_no = serializers.CharField(read_only=True, label="退货单编号")
    order_type_display = serializers.CharField(source='get_order_type_display', read_only=True, label="订单类型")
    order_status_display = serializers.CharField(source='get_order_status_display', read_only=True, label="订单状态")
    # 退款金额
    refund_amount = serializers.DecimalField(max_digits=30, decimal_places=8, read_only=True, label="退款金额")
    # 订单合计
    total_amount = serializers.DecimalField(max_digits=30, decimal_places=8, read_only=True, label="订单合计")

    # 付款优惠
    payment_discount = serializers.DecimalField(max_digits=30, decimal_places=8, read_only=True, label="付款优惠")
    # 添加应付账本相关字段
    payable_status = serializers.SerializerMethodField(label="付款状态")
    payable_amount = serializers.SerializerMethodField(label="应付金额")
    create_at = serializers.DateTimeField(source='create_time', read_only=True, format="%Y-%m-%d %H:%M:%S", label="创建时间")
    update_at = serializers.DateTimeField(source='update_time', read_only=True, format="%Y-%m-%d %H:%M:%S", label="更新时间")
    # 添加物品种类数量字段
    item_count = serializers.IntegerField(read_only=True, label="物品种类数量")

    def get_payable_status(self, obj):
        """获取对应应付账本的付款状态"""
        try:
            accounts_payable = AccountsPayable.objects.filter(
                source_order_no=str(obj.id),
                source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
                sales_return=obj
            ).first()
            return accounts_payable.get_payment_status_display() if accounts_payable else "未付款"
        except:
            return ""

    def get_payable_amount(self, obj):
        """获取对应应付账本的应付金额"""
        try:
            accounts_payable = AccountsPayable.objects.filter(
                source_order_no=str(obj.id),
                source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
                sales_return=obj
            ).first()
            return accounts_payable.payable_amount if accounts_payable else 0
        except:
            return 0

    def get_refund_amount(self, obj):
        """获取退款金额"""
        try:
            # 查找关联的应付账本记录
            accounts_payable = AccountsPayable.objects.filter(
                source_order_no=str(obj.id),
                source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
                sales_return=obj
            ).first()
            
            if not accounts_payable:
                return Decimal('0')
            
            # 已付款的金额作为退款金额
            return accounts_payable.paid_amount if hasattr(accounts_payable, 'paid_amount') else Decimal('0')
        except Exception as e:
            log.error(f"获取退款金额时出错: {str(e)}")
            return Decimal('0')
    
    def get_payment_discount(self, obj):
        """获取付款优惠"""
        try:
            return obj.payment_discount or Decimal('0')
        except Exception as e:
            log.error(f"获取付款优惠时出错: {str(e)}")
            return Decimal('0')

    class Meta:
        model = SalesReturn
        fields = ['id', 'sales_out_id', 'sales_out_order_id', 'customer_name', 'handler_name','create_at','update_at',
                 'warehouse_name', 'controller_name', 'short_desc', 'payable_status', 'payable_amount',
                 'order_no', 'order_type_display', 'order_status_display', 'refund_amount', 'total_amount', 'discount', 'payment_discount', 'item_count']

@SerializerCollector(alias="销售退货应付账本信息")
class AccountsPayableInfoSerializer(serializers.ModelSerializer):
    """销售退货应付账本信息序列化器"""
    class Meta:
        model = AccountsPayable
        fields = ['id', 'order_amount', 'payable_amount', 'remaining_amount', 'paid_amount',
                  'payment_status', 'source_type', 'source_order_no', 'remark']

@SerializerCollector(alias="销售退货付款信息")
class PaymentInfoSerializer(serializers.ModelSerializer):
    """销售退货付款信息序列化器"""
    payment_date = serializers.DateField(format="%Y-%m-%d")
    
    class Meta:
        model = Payment
        fields = ['id', 'amount', 'actual_amount', 'discount', 'payment_date']

@SerializerCollector(alias="销售退货详情")
class SalesReturnDetailSerializer(serializers.ModelSerializer):
    """销售退货详情序列化器"""
    warehouse_info = serializers.SerializerMethodField()
    customer_info = serializers.SerializerMethodField()
    handler_info = serializers.SerializerMethodField()
    sales_out_info = serializers.SerializerMethodField()
    payment_method_info = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(source='create_time', format="%Y-%m-%d %H:%M:%S")
    updated_at = serializers.DateTimeField(source='update_time', format="%Y-%m-%d %H:%M:%S")
    items = serializers.SerializerMethodField()
    accounts_payable = serializers.SerializerMethodField()
    payment = serializers.SerializerMethodField()

    class Meta:
        model = SalesReturn
        fields = ['id', 'sales_out', 'discount', 'handler', 'customer',
                  'warehouse', 'payment_method', 'total_amount', 'short_desc', 'order_no', 
                  'order_type', 'payment_discount', 'is_draft', 'warehouse_info', 'customer_info', 
                  'handler_info', 'sales_out_info', 'payment_method_info', 'created_at', 'updated_at',
                  'items', 'accounts_payable', 'payment']

    def get_items(self, obj):
        """获取销售退货明细项"""
        try:
            items = SalesReturnItem.objects.filter(sales_return=obj).select_related(
                'item', 'unit', 'unit__unit_type'
            )
            return [
                {
                    "id": item.id,
                    "item": item.item.id if item.item else None,
                    "item_name": item.item.name if item.item else "未知物品",
                    "unit": item.unit.id if item.unit else None,
                    "unit_name": item.unit.unit_type.name if item.unit and item.unit.unit_type else "未知单位",
                    "quantity": item.quantity,
                    "price": item.price,
                }
                for item in items
            ]
        except Exception as e:
            log.error(f"获取销售退货明细项时出错: {str(e)}")
            return []
    
    def get_accounts_payable(self, obj):
        """获取关联的应付账本数据"""
        try:
            accounts_payable = AccountsPayable.objects.filter(
                source_order_no=str(obj.id),
                source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
                sales_return=obj
            ).first()
            
            if accounts_payable:
                return AccountsPayableInfoSerializer(accounts_payable).data
            return None
        except Exception as e:
            log.error(f"获取应付账本数据时出错: {str(e)}")
            return None
    
    def get_payment(self, obj):
        """获取关联的付款记录数据"""
        try:
            payment = Payment.objects.filter(
                order_type=Payment.OrderTypeChoices.SalesReturn,
                order_id=str(obj.id)
            ).first()
            
            if payment:
                return PaymentInfoSerializer(payment).data
            return None
        except Exception as e:
            log.error(f"获取付款记录数据时出错: {str(e)}")
            return None

    def get_warehouse_info(self, obj):
        try:
            if not obj.warehouse:
                return None
            return {
                "id": obj.warehouse.id,
                "name": obj.warehouse.name
            }
        except Exception as e:
            log.error(f"获取仓库信息时出错: {str(e)}")
            return None

    def get_customer_info(self, obj):
        try:
            if not obj.customer:
                return None
            return {
                "id": obj.customer.id,
                "name": obj.customer.name,
                "phone": getattr(obj.customer, 'phone', '')
            }
        except Exception as e:
            log.error(f"获取客户信息时出错: {str(e)}")
            return None

    def get_handler_info(self, obj):
        try:
            if not obj.handler:
                return None
            return {
                "id": obj.handler.id,
                "name": obj.handler.name
            }
        except Exception as e:
            log.error(f"获取经办人信息时出错: {str(e)}")
            return None

    def get_sales_out_info(self, obj):
        try:
            if not obj.sales_out:
                return None
            return {
                "id": obj.sales_out.id,
                "order_no": obj.sales_out.order_no,
                "document_type": obj.sales_out.document_type,
                "document_type_display": obj.sales_out.get_document_type_display()
            }
        except Exception as e:
            log.error(f"获取销售出库单信息时出错: {str(e)}")
            return None

    def get_payment_method_info(self, obj):
        try:
            if not obj.payment_method:
                return None
            return {
                "id": obj.payment_method.id,
                "name": obj.payment_method.name
            }
        except Exception as e:
            log.error(f"获取付款方式信息时出错: {str(e)}")
            return None


class SalesReturnViewSet(SafeModelViewSet):
    """
    销售退货的增删改查接口
    
    支持两种类型的退货：
    1. 零售退货（retail）：通常来自零售销售出库单的退货
    2. 批发退货（wholesale）：通常来自批发销售出库单的退货
    
    退货单根据类型会有不同的编号前缀：
    - 零售退货单：SRR (Sales Return Retail)
    - 批发退货单：SRW (Sales Return Wholesale)
    
    可以通过设置order_type参数来指定退货类型，如果未指定，
    则会尝试从关联的销售出库单获取类型，默认为批发退货。
    """
    queryset = SalesReturn.objects.all()
    serializer_class = SalesReturnSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """
        重写 get_queryset 方法，添加过滤条件
        默认不返回已取消的订单，除非请求中指定 include_cancelled=true
        """
        queryset = super().get_queryset()
        
        # 获取是否包含已取消订单的参数
        include_cancelled = self.request.query_params.get('include_cancelled', 'false').lower()
        include_cancelled = include_cancelled in ['true', '1', 'yes']
        
        # 默认不返回已取消的订单
        if not include_cancelled:
            queryset = queryset.exclude(order_status=SalesReturn.OrderStatusChoices.CANCELLED)
            
        return queryset

    @staticmethod
    def validate_return_quantities(sales_out, items_data):
        """
        校验退货数量：所有针对当前销售出库订单的退货订单中，每个退货物品的数量加起来不能超过销售出库的物品
        """

        # 这个是没有退货的，直接允许
        if not sales_out:
            return True, None

        # 获取销售出库单的所有物品
        sales_out_items = SalesOutItem.objects.filter(sales_out=sales_out)
        sales_out_item_quantities = {}

        # 统计销售出库单中每个物品的数量 (主单位）
        for so_item in sales_out_items:
            item_id = so_item.item.id
            if item_id not in sales_out_item_quantities:
                sales_out_item_quantities[item_id] = Decimal('0')
            sales_out_item_quantities[item_id] += so_item.quantity * so_item.unit.conversion_rate

        # 获取该销售出库单的所有退货单（不包括草稿状态）
        existing_returns = SalesReturn.objects.filter(
            sales_out=sales_out,
            is_draft=False
        )

        # 统计已退货的数量
        returned_quantities = {}
        for return_order in existing_returns:
            return_items = SalesReturnItem.objects.filter(sales_return=return_order)
            for return_item in return_items:
                item_id = return_item.item.id
                if item_id not in returned_quantities:
                    returned_quantities[item_id] = Decimal('0')
                returned_quantities[item_id] += return_item.quantity * return_item.unit.conversion_rate

        # 校验当前退货单的数量
        for item_data in items_data:
            item_id = item_data['item'].id
            current_return_quantity = Decimal(item_data['quantity'] * item_data['unit'].conversion_rate)

            # 计算该物品的总退货数量（包括已退货和当前退货） 都换为主单位了
            total_returned = returned_quantities.get(item_id, Decimal('0')) + current_return_quantity

            # 检查是否超过销售出库数量
            if item_id in sales_out_item_quantities:
                sales_out_quantity = sales_out_item_quantities[item_id]
                if total_returned > sales_out_quantity:
                    return False, (f"物品【{item_data['item'].name}】的退货数量超过销售出库数量。"
                                   f"销售出库：{sales_out_quantity}{item_data['item'].unit.unit_type.name}，"
                                   f"已退货：{returned_quantities.get(item_id, Decimal('0'))}{item_data['item'].unit.unit_type.name}，"
                                   f"当前退货：{item_data['quantity']}{item_data['unit'].unit_type.name}，"
                                   f"即退货：{current_return_quantity}{item_data['unit'].unit_type.name}，"
                                   f"总计：{total_returned}{item_data['item'].unit.unit_type.name}")
            else:
                return False, f"物品【{item_data['item'].name}】在销售出库单中不存在"

        return True, None

    @staticmethod
    def count_unique_items(items_data):
        """
        统计不重复的物品种类数量，不同单位的同一物品算一种
        
        :param items_data: 物品数据列表
        :return: 不重复物品种类数量
        """
        # 使用集合去重
        unique_item_ids = set()
        for item_data in items_data:
            item_id = item_data['item'].id if isinstance(item_data.get('item'), models.Model) else item_data.get('item')
            if item_id:
                unique_item_ids.add(item_id)
        return len(unique_item_ids)

    @staticmethod
    def create_stock_for_return(sales_return, return_item, warehouse):
        """
        为退货创建库存记录
        重新入库，原批次号不变，入库单号就是退货ID
        """
        from WmsCore.models import Stock
        # 查找原销售出库物品对应的库存记录
        original_stock = None
        if sales_return.sales_out:
            sales_out_items = SalesOutItem.objects.filter(
                sales_out=sales_return.sales_out,
                item=return_item.item
            )
            if sales_out_items.exists():
                # 使用第一个匹配的库存记录作为参考
                original_stock = sales_out_items.first().stock if hasattr(sales_out_items.first(), 'stock') else None
                # 如果库存记录为空，尝试查找该物品在该仓库的库存
                if not original_stock:
                    original_stock = Stock.objects.filter(
                        item=return_item.item,
                        warehouse=warehouse
                    ).first()

        # 创建新的库存记录
        stock_data = {
            'item': return_item.item,
            'warehouse': warehouse,
            'unit': return_item.unit,
            'in_type': Stock.StockInChoices.RETURN,
            'in_order_id': str(sales_return.id),
            'batch_number': original_stock.batch_number if original_stock else Counter.DayCounter("SBN"),
            'track_id': original_stock.track_id if original_stock else 0,
            'in_date': timezone.now().date(),
            'actual_cost': return_item.price,
            'quantity': return_item.quantity,
            'remaining_quantity': return_item.quantity * return_item.unit.conversion_rate,
        }
        # 如果有原库存记录，复制一些字段
        if original_stock:
            stock_data['expiry_days'] = original_stock.expiry_days
            stock_data['allocated_cost'] = original_stock.allocated_cost
        else:
            stock_data['expiry_days'] = 0
            stock_data['allocated_cost'] = None

        # 创建库存记录
        new_stock = Stock.objects.create(**stock_data)
        if new_stock.track_id==0:
            new_stock.track_id=new_stock.id
            new_stock.save()
        # 更新物品的总库存
        item = return_item.item
        item.total_stock += return_item.quantity * return_item.unit.conversion_rate
        item.save()
        # 更新仓库的总库存
        WarehouseAdmin.UpdateItemQuantity(item, warehouse)
        return new_stock

    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        # 对于写操作，不需要预取关系
        if self.action in ['create', 'update', 'partial_update']:
            return super().get_serializer(*args, **kwargs)

        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()

        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            try:
                instance = self.get_queryset().get(id=instance.id)
                kwargs['instance'] = instance
            except SalesReturn.DoesNotExist:
                # 在某些情况下（如删除后），实例可能不存在，这时保持原样
                pass

        return super().get_serializer(*args, **kwargs)

    def get_serializer_class(self):
        if self.action == 'list':
            return SalesReturnListSerializer
        return SalesReturnSerializer
        
    def list(self, request, *args, **kwargs):
        """获取销售退货列表，支持按客户ID和订单类型筛选"""
        customer_id = request.query_params.get('customer_id', None)
        order_type = request.query_params.get('order_type', None)
        
        queryset = self.get_queryset()
        
        # 按订单类型筛选
        if order_type in ['retail', 'wholesale']:
            queryset = queryset.filter(order_type=order_type)
        
        # 按客户ID筛选
        if customer_id:
            try:
                customer_id = int(customer_id)
                queryset = queryset.filter(customer_id=customer_id)
                # 如果没有找到对应客户ID的记录，返回空列表
                if not queryset.exists():
                    return make_response(
                        code=0,
                        msg='获取成功',
                        data={'count': 0, 'next': None, 'previous': None, 'results': []},
                        status=status.HTTP_200_OK
                    )
            except ValueError:
                # 如果customer_id无效（不能转为整数），返回空列表
                return make_response(
                    code=0,
                    msg='获取成功',
                    data={'count': 0, 'next': None, 'previous': None, 'results': []},
                    status=status.HTTP_200_OK
                )
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = self.get_serializer(queryset, many=True)
        return make_response(
            code=0,
            msg='获取成功',
            data=serializer.data,
            status=status.HTTP_200_OK
        )

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新销售退货订单（包含退货物品）"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} create sales return: {serializer.validated_data}")

        # 根据订单类型选择不同的创建方法
        order_type = serializer.validated_data.get('order_type', 'wholesale')
        if order_type == 'retail':
            return self.create_retail_return(serializer, request)
        else:
            return self.create_wholesale_return(serializer, request)

    def create_retail_return(self, serializer, request):
        """创建零售退货订单"""
        # 1. 取出前端传入的仓库和物品明细
        warehouse = serializer.validated_data.get('warehouse', None)
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        sales_out = serializer.validated_data.get('sales_out', None)

        if not items_data:
            raise WmsException(_T("销售退货订单必须包含至少一个物品"), ErrorCode.FIELD_REQUIRED)

        # 2. 校验销售退货必须指定退货仓库
        if not warehouse:
            raise WmsException(_T("必须指定退货仓库"), ErrorCode.FIELD_REQUIRED)

        # 3. 零售退货必须指定付款方式
        if not payment_method:
            raise WmsException(_T("零售退货必须指定付款方式"), ErrorCode.FIELD_REQUIRED)

        # 4. 校验销售出库单不能是暂存状态
        if sales_out and sales_out.is_draft:
            raise WmsException(_T("销售出库单不能是暂存状态"), ErrorCode.VALIDATION_ERROR)

        # 5. 校验退货物品必须在销售出库单中存在
        self._validate_items_in_sales_out(sales_out, items_data)

        # 6. 校验退货数量合理性
        self._validate_item_quantity_positive(items_data)
        self._validate_return_quantity_not_exceed(sales_out, items_data)

        # 7. 累计总金额
        total_amount = self._calc_total_amount(items_data)

        # 8. 校验前端传入的 total_amount 是否与服务器计算的总金额一致
        self._validate_total_amount(request, total_amount, total_discount)

        # 9. 零售退货直接创建正式单据（不支持草稿）
        is_draft = serializer.validated_data.get('is_draft', False)
        if is_draft:
            raise WmsException(_T("零售退货不支持草稿状态"), ErrorCode.VALIDATION_ERROR)

        return SalesReturnViewSet.retail_return_create(serializer, request, total_amount)

    def create_wholesale_return(self, serializer, request):
        """创建批发退货订单"""
        # 1. 取出前端传入的仓库和物品明细
        warehouse = serializer.validated_data.get('warehouse', None)
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        sales_out = serializer.validated_data.get('sales_out', None)

        if not items_data:
            raise WmsException(_T("销售退货订单必须包含至少一个物品"), ErrorCode.FIELD_REQUIRED)

        # 2. 校验销售退货必须指定退货仓库
        if not warehouse:
            raise WmsException(_T("必须指定退货仓库"), ErrorCode.FIELD_REQUIRED)

        # 3. 校验销售出库单不能是暂存状态
        if sales_out and sales_out.is_draft:
            raise WmsException(_T("销售出库单不能是暂存状态"), ErrorCode.VALIDATION_ERROR)

        # 4. 校验退货物品必须在销售出库单中存在
        self._validate_items_in_sales_out(sales_out, items_data)

        # 5. 校验退货数量合理性
        self._validate_item_quantity_positive(items_data)
        self._validate_return_quantity_not_exceed(sales_out, items_data)

        # 6. 累计总金额
        total_amount = self._calc_total_amount(items_data)

        # 7. 校验前端传入的 total_amount 是否与服务器计算的总金额一致
        self._validate_total_amount(request, total_amount, total_discount)

        # 8. 根据是否草稿选择不同的处理方式
        is_draft = serializer.validated_data.get('is_draft', False)
        if is_draft:
            return SalesReturnViewSet.wholesale_return_create_draft(serializer, request, total_amount)
        else:
            return SalesReturnViewSet.wholesale_return_create_formal(serializer, request, total_amount)

    @staticmethod
    def retail_return_create(serializer, request, total_amount):
        """创建零售退货单（直接正式）"""
        warehouse = serializer.validated_data.pop('warehouse', None)
        items_data = serializer.validated_data.pop('items', [])
        sales_out = serializer.validated_data.pop('sales_out', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        payment_method = serializer.validated_data.pop('payment_method', None)
        customer = serializer.validated_data.pop('customer', None)
        handler = serializer.validated_data.pop('handler', None)
        payment_discount = serializer.validated_data.pop('payment_discount', Decimal('0'))

        # 生成简要描述
        short_desc = CommonTools.generate_short_desc(items_data)
        
        # 计算不重复的物品种类数量
        item_count = SalesReturnViewSet.count_unique_items(items_data)
        
        # 生成订单编号
        order_no = SalesReturnViewSet.generate_order_no('retail')  # 零售退货单编号

        # 创建销售退货单（零售，直接正式）
        sales_return = SalesReturn.objects.create(
            sales_out=sales_out,
            customer=customer if customer else sales_out.customer,
            handler=handler if handler else request.zt_user,
            return_date=timezone.now().date(),
            warehouse=warehouse,
            total_cost=0,
            total_amount=total_amount - total_discount,
            discount=total_discount,
            controller=request.zt_user,
            is_draft=False,
            order_type='retail',
            short_desc=short_desc,
            item_count=item_count,  # 添加物品种类数量
            order_no=order_no,  # 设置订单编号
            payment_method=payment_method,  # 设置付款方式
            order_status=SalesReturn.OrderStatusChoices.COMPLETED,  # 零售退货直接设为完成状态
            payment_status=SalesReturn.PaymentStatusChoices.PAID  # 零售退货直接设为已付款状态
        )

        # 创建应付账本记录（零售退货立即付款）
        payable_amount = total_amount - total_discount - payment_discount
        accounts_payable = AccountsAdmin.create_payable(
            supplier=None,
            order_amount=total_amount,
            payable_amount=payable_amount,
            remaining_amount=Decimal('0'),  # 零售退货立即付款
            handler=handler if handler else request.zt_user,
            source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
            source_order_id=str(sales_return.id),
            remark=f"零售退货 {order_no}",
            sales_return=sales_return,
            customer=customer if customer else sales_out.customer,
        )

        # 设置为已付款状态
        accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PAID
        accounts_payable.save()

        # 创建付款单记录
        payment = Payment.objects.create(
            supplier=None,
            customer=customer if customer else sales_out.customer,
            order_type=Payment.OrderTypeChoices.SalesReturn,
            order_id=str(sales_return.id),
            amount=payable_amount,
            actual_amount=payable_amount,
            discount=payment_discount,
            handler=handler if handler else request.zt_user,
            payment_date=timezone.now().date(),
            remark=f"零售退货付款 {order_no}",
            payment_method=payment_method,
            controller=request.zt_user
        )

        # 创建销售退货物品表并处理库存
        sales_return_items = []
        for return_item in items_data:
            return_item_obj = SalesReturnItem.objects.create(
                sales_return=sales_return,
                item=return_item['item'],
                quantity=return_item['quantity'],
                price=return_item['price'],
                final_cost=return_item['price'],
                unit=return_item['unit'],
                controller=request.zt_user,
                handler=handler if handler else request.zt_user,
            )
            sales_return_items.append(return_item_obj)

            # 创建库存记录（重新入库）
            new_stock = SalesReturnViewSet.create_stock_for_return(sales_return, return_item_obj, warehouse)
            return_item_obj.stock = new_stock
            return_item_obj.save()

        # 准备返回数据
        from WmsCore.control.Accounts import AccountsPayableSerializer
        accounts_payable_data = AccountsPayableSerializer(accounts_payable).data
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(sales_return_items, many=True).data

        return make_response(
            code=0,
            msg='零售退货创建成功',
            data={
                "sales_return": sales_return_data,
                "sales_return_items": sales_return_items_data,
                "accounts_payable": accounts_payable_data,
                "payment": {
                    "id": payment.id,
                    "amount": payment.amount,
                    "payment_date": payment.payment_date.strftime("%Y-%m-%d")
                }
            },
            status=status.HTTP_201_CREATED
        )

    @staticmethod
    def wholesale_return_create_draft(serializer, request, total_amount):
        """创建批发退货草稿单"""
        warehouse = serializer.validated_data.pop('warehouse', None)
        items_data = serializer.validated_data.pop('items', [])
        sales_out = serializer.validated_data.pop('sales_out', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        customer = serializer.validated_data.pop('customer', None)
        handler = serializer.validated_data.pop('handler', None)
        payment_discount = serializer.validated_data.pop('payment_discount', Decimal('0'))
        payment_method = serializer.validated_data.pop('payment_method', None)

        # 生成简要描述
        short_desc = CommonTools.generate_short_desc(items_data)

        # 计算不重复的物品种类数量
        item_count = SalesReturnViewSet.count_unique_items(items_data)
        
        # 生成订单编号（草稿状态也生成，便于前端展示）
        order_no = SalesReturnViewSet.generate_order_no('wholesale')

        # 创建销售退货单（批发，草稿状态）
        sales_return = SalesReturn.objects.create(
            sales_out=sales_out,
            customer=customer if customer else sales_out.customer,
            handler=handler if handler else request.zt_user,
            return_date=timezone.now().date(),
            warehouse=warehouse,
            total_cost=0,
            total_amount=total_amount - total_discount,
            discount=total_discount,
            controller=request.zt_user,
            is_draft=True,
            order_type='wholesale',
            short_desc=short_desc,
            item_count=item_count,
            order_no=order_no,
            payment_method=payment_method,
            order_status=SalesReturn.OrderStatusChoices.PENDING  # 草稿状态设为待处理
        )

        # 创建销售退货物品表（草稿状态不处理库存）
        sales_return_items = []
        for return_item in items_data:
            return_item_obj = SalesReturnItem.objects.create(
                sales_return=sales_return,
                item=return_item['item'],
                quantity=return_item['quantity'],
                price=return_item['price'],
                final_cost=return_item['price'],
                unit=return_item['unit'],
                controller=request.zt_user,
                handler=handler if handler else request.zt_user,
            )
            sales_return_items.append(return_item_obj)

        # 准备返回数据
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(sales_return_items, many=True).data

        return make_response(
            code=0,
            msg='批发退货草稿创建成功',
            data={
                "sales_return": sales_return_data,
                "sales_return_items": sales_return_items_data
            },
            status=status.HTTP_201_CREATED
        )

    @staticmethod
    def wholesale_return_create_formal(serializer, request, total_amount):
        """创建批发退货正式单"""
        warehouse = serializer.validated_data.pop('warehouse', None)
        items_data = serializer.validated_data.pop('items', [])
        sales_out = serializer.validated_data.pop('sales_out', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        payment_method = serializer.validated_data.pop('payment_method', None)
        customer = serializer.validated_data.pop('customer', None)
        handler = serializer.validated_data.pop('handler', None)
        payment_discount = serializer.validated_data.pop('payment_discount', Decimal('0'))

        if not payment_method:
            raise WmsException(_T("批发退货正式单必须指定付款方式"), ErrorCode.FIELD_REQUIRED)

        # 生成简要描述
        short_desc = CommonTools.generate_short_desc(items_data)

        # 计算不重复的物品种类数量
        item_count = SalesReturnViewSet.count_unique_items(items_data)
        
        # 生成订单编号
        order_no = SalesReturnViewSet.generate_order_no('wholesale')  # 批发退货单编号

        # 创建销售退货单（批发，正式状态）
        sales_return = SalesReturn.objects.create(
            sales_out=sales_out,
            customer=customer if customer else sales_out.customer,
            handler=handler if handler else request.zt_user,
            return_date=timezone.now().date(),
            warehouse=warehouse,
            total_cost=0,
            total_amount=total_amount - total_discount,
            discount=total_discount,
            controller=request.zt_user,
            is_draft=False,
            order_type='wholesale',
            short_desc=short_desc,
            item_count=item_count,
            order_no=order_no,
            payment_method=payment_method,
            order_status=SalesReturn.OrderStatusChoices.COMPLETED  # 批发退货也直接设为完成状态
        )

        # 获取请求中的付款金额（如果有）
        pay_amount = serializer.validated_data.get('pay_amount', Decimal('0'))

        # 创建应付账本记录（批发退货可能有账期）
        payable_amount = total_amount - total_discount - payment_discount
        accounts_payable = AccountsAdmin.create_payable(
            supplier=None,
            order_amount=total_amount,
            payable_amount=payable_amount,
            remaining_amount=payable_amount - pay_amount,  # 批发退货可能有账期，初始为全额欠款减去已付款
            handler=handler if handler else request.zt_user,
            source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
            source_order_id=str(sales_return.id),
            remark=f"批发退货 {order_no}",
            sales_return=sales_return,
            customer=customer if customer else sales_out.customer
        )

        # 设置付款状态
        if pay_amount == 0:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.UNPAID
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.UNPAID
        elif pay_amount < payable_amount:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PARTIAL
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.PARTIAL
        else:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PAID
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.PAID
        
        accounts_payable.save()
        sales_return.save()

        # 如果有付款，创建付款单记录
        payment_data = None
        if pay_amount > 0:
            payment = Payment.objects.create(
                supplier=None,
                customer=customer if customer else sales_out.customer,
                order_type=Payment.OrderTypeChoices.SalesReturn,
                order_id=str(sales_return.id),
                amount=payable_amount,
                actual_amount=pay_amount,
                discount=payment_discount,
                handler=handler if handler else request.zt_user,
                payment_date=timezone.now().date(),
                remark=f"批发退货付款 {order_no}",
                payment_method=payment_method,
                controller=request.zt_user
            )
            payment_data = {
                "id": payment.id,
                "amount": payment.amount,
                "actual_amount": payment.actual_amount,
                "payment_date": payment.payment_date.strftime("%Y-%m-%d")
            }

        # 创建销售退货物品表并处理库存
        sales_return_items = []
        for return_item in items_data:
            return_item_obj = SalesReturnItem.objects.create(
                sales_return=sales_return,
                item=return_item['item'],
                quantity=return_item['quantity'],
                price=return_item['price'],
                final_cost=return_item['price'],
                unit=return_item['unit'],
                controller=request.zt_user,
                handler=handler if handler else request.zt_user,
            )
            sales_return_items.append(return_item_obj)

            # 创建库存记录（重新入库）
            new_stock = SalesReturnViewSet.create_stock_for_return(sales_return, return_item_obj, warehouse)
            return_item_obj.stock = new_stock
            return_item_obj.save()

        # 准备返回数据
        from WmsCore.control.Accounts import AccountsPayableSerializer
        accounts_payable_data = AccountsPayableSerializer(accounts_payable).data
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(sales_return_items, many=True).data

        result_data = {
            "sales_return": sales_return_data,
            "sales_return_items": sales_return_items_data,
            "accounts_payable": accounts_payable_data,
        }
        
        if payment_data:
            result_data["payment"] = payment_data

        return make_response(
            code=0,
            msg='批发退货创建成功',
            data=result_data,
            status=status.HTTP_201_CREATED
        )

    def create_return(self, serializer, request):
        # 1. 取出前端传入的仓库和物品明细
        warehouse = serializer.validated_data.get('warehouse', None)
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        sales_out = serializer.validated_data.get('sales_out', None)
        order_type = serializer.validated_data.get('order_type', None)

        # 如果没有指定订单类型，则从销售出库单获取
        if not order_type and sales_out:
            order_type = sales_out.document_type
        # 默认为批发
        if not order_type:
            order_type = 'wholesale'
            
        # 判断是零售还是批发
        is_retail = self.is_retail(sales_out, order_type)
        
        # 根据是否为零售或批发，可以添加特定的验证逻辑
        if is_retail:
            # 零售退货可能有特殊的验证规则
            log.info(f"创建零售退货单: {serializer.validated_data}")
        else:
            # 批发退货的验证逻辑
            log.info(f"创建批发退货单: {serializer.validated_data}")

        if not items_data:
            raise WmsException(_T("销售退货订单必须包含至少一个物品"), ErrorCode.FIELD_REQUIRED)

        # 2. 校验销售退货必须指定退货仓库
        if not warehouse:
            raise WmsException(_T("必须指定退货仓库"), ErrorCode.FIELD_REQUIRED)

        # 3. 校验销售出库单不能是暂存状态
        if sales_out and sales_out.is_draft:
            raise WmsException(_T("销售出库单不能是暂存状态"), ErrorCode.VALIDATION_ERROR)

        # 4. 校验退货物品必须在销售出库单中存在
        self._validate_items_in_sales_out(sales_out, items_data)

        # 5. 校验退货数量合理性
        self._validate_item_quantity_positive(items_data)
        self._validate_return_quantity_not_exceed(sales_out, items_data)

        # 6. 累计总金额
        total_amount = self._calc_total_amount(items_data)

        # 7. 校验前端传入的 total_amount 是否与服务器计算的总金额一致
        self._validate_total_amount(request, total_amount, total_discount)

        if request.data.get('is_draft', False):
            return SalesReturnViewSet.return_create_or_update(
                serializer=serializer, 
                request=request, 
                is_update=False,
                is_draft=True, 
                total_amount=total_amount,
                order_type=order_type
            )
        else:
            return SalesReturnViewSet.return_create_or_update(
                serializer, 
                request, 
                is_update=False, 
                is_draft=False,
                total_amount=total_amount,
                order_type=order_type
            )

    @staticmethod
    def return_create_or_update(serializer, request, total_amount, is_update: bool = False,
                                is_draft: bool = False, order_type: str = 'wholesale'):
        if is_update:
            if is_draft is False:
                raise WmsException(_T("不能更新非草稿订单"), ErrorCode.VALIDATION_ERROR)
            return SalesReturnViewSet.return_update(serializer, request, total_amount, order_type)
        else:
            return SalesReturnViewSet.return_create(serializer, request, total_amount, is_draft, order_type)

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个销售退货详情"""
        try:
            # 预加载所有相关数据
            queryset = SalesReturn.objects.select_related(
                'warehouse', 'customer', 'handler', 'sales_out', 'payment_method'
            )
            
            # 获取对象
            sales_return = queryset.get(pk=pk)
            
            # 使用详情序列化器
            serializer = SalesReturnDetailSerializer(sales_return)
            sales_return_data = serializer.data
            
            # 提取序列化器返回的数据
            # sales_return_items_data = sales_return_data.pop('items')
            # accounts_payable_data = sales_return_data.pop('accounts_payable')
            # payment_data = sales_return_data.pop('payment')
            
            # 如果有支付信息，添加到返回数据中
            # if payment_data:
            #     sales_return_data["payment"] = payment_data
            
            # # 构建返回数据结构，与创建方法保持一致
            # result_data = {
            #     "sales_return": sales_return_data,
            #     # "sales_return_items": sales_return_items_data,
            #     "accounts_payable": accounts_payable_data
            # }
            return make_response(code=0, msg='获取成功', data=sales_return_data)
        except SalesReturn.DoesNotExist:
            return make_response(code=1, msg='销售退货不存在', )
        except Exception as e:
            log.error(f"获取销售退货详情时出错: {str(e)}")
            return make_response(code=1, msg=f'获取销售退货详情失败: {str(e)}')

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新销售退货信息"""
        # 先获取当前对象，判断是否为草稿
        sales_return = self.get_object()
        if not sales_return.is_draft:
            raise WmsException(_T("只能修改暂存状态的退货单"), ErrorCode.VALIDATION_ERROR)
        
        serializer = self.get_serializer(sales_return, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} update sales return: {serializer.validated_data}")

        # 1. 取出前端传入的仓库和物品明细
        warehouse = serializer.validated_data.get('warehouse', None)
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        sales_out = serializer.validated_data.get('sales_out', None)
        order_type = serializer.validated_data.get('order_type', None)

        # 如果前端没有传入订单类型，则使用当前订单的类型
        if not order_type:
            order_type = sales_return.order_type

        if not items_data:
            raise WmsException(_T("销售退货订单必须包含至少一个物品"), ErrorCode.FIELD_REQUIRED)

        # 2. 校验销售退货必须指定退货仓库
        if not warehouse:
            raise WmsException(_T("必须指定退货仓库"), ErrorCode.FIELD_REQUIRED)

        # 3. 校验销售出库单不能是暂存状态
        if sales_out and sales_out.is_draft:
            raise WmsException(_T("销售出库单不能是暂存状态"), ErrorCode.VALIDATION_ERROR)

        # 4. 校验退货物品必须在销售出库单中存在
        self._validate_items_in_sales_out(sales_out, items_data)

        # 5. 校验退货数量合理性
        self._validate_item_quantity_positive(items_data)
        self._validate_return_quantity_not_exceed(sales_out, items_data)

        # 6. 累计总金额
        total_amount = self._calc_total_amount(items_data)

        # 7. 校验前端传入的 total_amount 是否与服务器计算的总金额一致
        self._validate_total_amount(request, total_amount, total_discount)

        # 根据订单类型选择不同的更新方法
        if order_type == 'retail':
            # 零售退货不支持草稿状态
            raise WmsException(_T("零售退货不支持草稿状态，无法更新"), ErrorCode.VALIDATION_ERROR)
        else:
            # 批发退货的更新逻辑
            is_draft = request.data.get('is_draft', True)
            if not is_draft and sales_return.is_draft:
                # 草稿转正式，需要创建应付账本记录
                return SalesReturnViewSet.convert_wholesale_draft_to_formal(serializer, request, total_amount)
            else:
                # 普通的草稿更新
                return SalesReturnViewSet.wholesale_return_update_draft(serializer, request, total_amount)

    @staticmethod
    def wholesale_return_update_draft(serializer, request, total_amount):
        """更新批发退货草稿"""
        sales_return = serializer.instance
        items_data = serializer.validated_data.pop('items', [])
        handler = serializer.validated_data.pop('handler', None)
        warehouse = serializer.validated_data.pop('warehouse', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        payment_method = serializer.validated_data.pop('payment_method', None)
        payment_discount = serializer.validated_data.pop('payment_discount', Decimal('0'))
        customer = serializer.validated_data.pop('customer', None)

        # 更新主表字段
        for field, value in serializer.validated_data.items():
            setattr(sales_return, field, value)
        if handler:
            sales_return.handler = handler
        if warehouse:
            sales_return.warehouse = warehouse
        if customer:
            sales_return.customer = customer
        if payment_method:
            sales_return.payment_method = payment_method
        
        # 更新物品种类数量
        item_count = SalesReturnViewSet.count_unique_items(items_data)
        sales_return.item_count = item_count
        
        # 更新金额字段
        sales_return.total_amount = total_amount - total_discount
        sales_return.discount = total_discount
        sales_return.payment_discount = payment_discount
        
        # 更新简要描述
        sales_return.short_desc = CommonTools.generate_short_desc(items_data)
        
        sales_return.save()

        # 更新退货物品
        item_ids = []
        for item_data in items_data:
            item_id = item_data.get('id', 0)
            if item_id:
                try:
                    item = SalesReturnItem.objects.get(id=item_id, sales_return=sales_return)
                    for field, value in item_data.items():
                        if field != 'id':
                            setattr(item, field, value)
                    item.save()
                except SalesReturnItem.DoesNotExist:
                    # 如果找不到项目，创建新的
                    item = SalesReturnItem.objects.create(
                        sales_return=sales_return,
                        item=item_data['item'],
                        quantity=item_data['quantity'],
                        price=item_data['price'],
                        final_cost=item_data['price'],
                        unit=item_data['unit'],
                        controller=request.zt_user,
                        handler=handler if handler else request.zt_user,
                    )
            else:
                # 创建新的退货物品
                item = SalesReturnItem.objects.create(
                    sales_return=sales_return,
                    item=item_data['item'],
                    quantity=item_data['quantity'],
                    price=item_data['price'],
                    final_cost=item_data['price'],
                    unit=item_data['unit'],
                    controller=request.zt_user,
                    handler=handler if handler else request.zt_user,
                )
            item_ids.append(item.id)

        # 删除不在当前提交列表中的物品
        SalesReturnItem.objects.filter(sales_return=sales_return).exclude(id__in=item_ids).delete()

        # 准备返回数据
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(
            SalesReturnItem.objects.filter(sales_return=sales_return), many=True
        ).data

        return make_response(
            code=0,
            msg='批发退货草稿更新成功',
            data={
                "sales_return": sales_return_data,
                "sales_return_items": sales_return_items_data
            },
            status=status.HTTP_200_OK
        )

    @staticmethod
    def convert_wholesale_draft_to_formal(serializer, request, total_amount):
        """将批发退货草稿转为正式"""
        sales_return = serializer.instance
        items_data = serializer.validated_data.get('items', [])
        handler = serializer.validated_data.get('handler', None)
        warehouse = serializer.validated_data.get('warehouse', None)
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        payment_discount = serializer.validated_data.get('payment_discount', Decimal('0'))
        pay_amount = serializer.validated_data.get('pay_amount', Decimal('0'))

        if not payment_method:
            raise WmsException(_T("转为正式退货单必须指定付款方式"), ErrorCode.FIELD_REQUIRED)

        # 更新退货单状态和付款方法
        sales_return.is_draft = False
        sales_return.payment_discount = payment_discount
        sales_return.payment_method = payment_method
        
        # 更新物品种类数量
        if items_data:
            item_count = SalesReturnViewSet.count_unique_items(items_data)
            sales_return.item_count = item_count
        
        # 更新订单状态为已完成
        sales_return.order_status = SalesReturn.OrderStatusChoices.COMPLETED
        
        # 根据付款金额设置付款状态
        payable_amount = total_amount - total_discount - payment_discount
        if pay_amount == 0:
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.UNPAID
        elif pay_amount < payable_amount:
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.PARTIAL
        else:
            sales_return.payment_status = SalesReturn.PaymentStatusChoices.PAID
        
        sales_return.save()

        # 查找草稿状态时创建的应付账本记录
        old_accounts_payable = AccountsPayable.objects.filter(
            source_order_no=str(sales_return.id),
            sales_return=sales_return
        ).first()

        # 如果存在旧的应付账本，则删除
        if old_accounts_payable:
            old_accounts_payable.delete()

        # 创建新的应付账本记录
        accounts_payable = AccountsAdmin.create_payable(
            supplier=None,
            order_amount=total_amount,
            payable_amount=payable_amount,
            remaining_amount=payable_amount - pay_amount,  # 减去已付款金额
            handler=handler if handler else request.zt_user,
            source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
            source_order_id=str(sales_return.id),
            remark=f"批发退货 {sales_return.order_no}",
            sales_return=sales_return,
            customer=sales_return.customer
        )

        # 设置应付账本的付款状态
        if pay_amount == 0:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.UNPAID
        elif pay_amount < payable_amount:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PARTIAL
        else:
            accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PAID
        accounts_payable.save()

        # 如果有付款，创建付款单记录
        payment_data = None
        if pay_amount > 0:
            payment = Payment.objects.create(
                supplier=None,
                customer=sales_return.customer,
                order_type=Payment.OrderTypeChoices.SalesReturn,
                order_id=str(sales_return.id),
                amount=payable_amount,
                actual_amount=pay_amount,
                discount=payment_discount,
                handler=handler if handler else request.zt_user,
                payment_date=timezone.now().date(),
                remark=f"批发退货付款 {sales_return.order_no}",
                payment_method=payment_method,
                controller=request.zt_user
            )
            payment_data = {
                "id": payment.id,
                "amount": payment.amount,
                "actual_amount": payment.actual_amount,
                "payment_date": payment.payment_date.strftime("%Y-%m-%d")
            }

        # 处理库存（为退货物品创建库存记录）
        return_items = SalesReturnItem.objects.filter(sales_return=sales_return).select_related('item', 'unit')
        for return_item in return_items:
            # 创建库存记录（重新入库）
            new_stock = SalesReturnViewSet.create_stock_for_return(sales_return, return_item, warehouse or sales_return.warehouse)
            return_item.stock = new_stock
            return_item.save()

        # 准备返回数据
        from WmsCore.control.Accounts import AccountsPayableSerializer
        accounts_payable_data = AccountsPayableSerializer(accounts_payable).data
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(
            SalesReturnItem.objects.filter(sales_return=sales_return), many=True
        ).data

        result_data = {
            "sales_return": sales_return_data,
            "sales_return_items": sales_return_items_data,
            "accounts_payable": accounts_payable_data
        }
        
        if payment_data:
            result_data["payment"] = payment_data

        return make_response(
            code=0,
            msg='批发退货转为正式成功',
            data=result_data,
            status=status.HTTP_200_OK
        )

    @staticmethod
    def convert_draft_to_formal(serializer, request, total_amount, order_type: str = 'wholesale'):
        """将草稿退货单转为正式退货单"""
        sales_return = serializer.instance
        items_data = serializer.validated_data.get('items', [])
        handler = serializer.validated_data.get('handler', None)
        warehouse = serializer.validated_data.get('warehouse', None)
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        payment_discount = serializer.validated_data.get('payment_discount', Decimal('0'))
        
        # 判断是零售还是批发
        is_retail = SalesReturnViewSet.is_retail(None, order_type)
        
        if not payment_method:
            raise WmsException(_T("转为正式退货单必须指定付款方式"), ErrorCode.FIELD_REQUIRED)

        # 更新退货单状态和类型
        sales_return.is_draft = False
        sales_return.order_type = order_type
        sales_return.payment_method = payment_method
        sales_return.payment_discount = payment_discount
        
        # 更新物品种类数量
        if items_data:
            item_count = SalesReturnViewSet.count_unique_items(items_data)
            sales_return.item_count = item_count
        
        # 如果订单编号前缀不匹配当前类型，重新生成
        if (is_retail and not sales_return.order_no.startswith("SRR")) or \
           (not is_retail and not sales_return.order_no.startswith("SRW")):
            sales_return.order_no = SalesReturnViewSet.generate_order_no(order_type)
            
        sales_return.save()

        # 创建应付账本记录
        payable_amount = total_amount - total_discount - payment_discount
        accounts_payable = AccountsAdmin.create_payable(
            supplier=None,  # 销售退货不涉及供应商
            order_amount=total_amount,
            payable_amount=payable_amount,
            remaining_amount=Decimal('0'),  # 退货时立即付款
            handler=handler if handler else request.zt_user,
            source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
            source_order_id=str(sales_return.id),
            remark=f"销售退货 {sales_return.order_no}",  # 更新remark格式
            sales_return=sales_return,
            customer=sales_return.customer
        )

        # 设置为已付款状态
        accounts_payable.payment_status = AccountsPayable.PaymentStatusChoices.PAID
        accounts_payable.save()

        # 处理库存（为退货物品创建库存记录）
        for return_item_data in items_data:
            return_item = SalesReturnItem.objects.filter(
                sales_return=sales_return,
                item=return_item_data['item']
            ).first()

            if return_item:
                # 创建库存记录（重新入库）
                new_stock = SalesReturnViewSet.create_stock_for_return(sales_return, return_item, warehouse)
                # 更新退货物品记录的库存关联
                return_item.stock = new_stock
                return_item.save()

        # 准备返回数据
        from WmsCore.control.Accounts import AccountsPayableSerializer
        accounts_payable_data = AccountsPayableSerializer(accounts_payable).data
        sales_return_data = SalesReturnSerializer(sales_return).data
        sales_return_items_data = SalesReturnItemSerializer(
            SalesReturnItem.objects.filter(sales_return=sales_return), many=True
        ).data

        result_data = {
            "sales_return": sales_return_data,
            "sales_return_items": sales_return_items_data,
            "accounts_payable": accounts_payable_data,
        }

        return make_response(
            code=0,
            msg='转为正式退货单成功',
            data=result_data,
            status=status.HTTP_200_OK
        )

    def destroy(self, request, pk=None, **kwargs):
        """只能删除草稿状态的销售退货单，删除时连同所有子退货物品和应付账本记录一起删除"""
        try:
            sales_return = self.get_object()
            if not sales_return.is_draft:
                raise WmsException(_T('只能删除草稿状态的销售退货单'), ErrorCode.VALIDATION_ERROR)

            # 删除关联的应付账本记录（如果存在）
            AccountsPayable.objects.filter(
                source_order_no=str(sales_return.id),
                source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
                sales_return=sales_return
            ).delete()

            # 先删除所有子退货物品
            SalesReturnItem.objects.filter(sales_return=sales_return).delete()
            sales_return.delete()
            return make_response(code=0, msg=_T('删除成功'), status=status.HTTP_200_OK)
        except SalesReturn.DoesNotExist:
            raise WmsException(_T('销售退货不存在'), ErrorCode.VALIDATION_ERROR)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索销售退货"""
        query = request.query_params.get('query', '').strip()
        order_type = request.query_params.get('order_type', None)
        
        if not query:
            return make_response(code=1, msg='请输入搜索关键词', status=status.HTTP_400_BAD_REQUEST)

        # 基础查询集
        queryset = self.get_queryset()
        
        # 按单据类型筛选
        if order_type in ['retail', 'wholesale']:
            queryset = queryset.filter(order_type=order_type)
            
        # 先找出匹配的客户和经办人
        from django.db.models import Q
        from WmsCore.models import Customer, ZTUser
        
        # 查找匹配的客户ID（根据客户名称进行搜索）
        customer_ids = Customer.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 查找匹配的经办人ID（根据经办人名称进行搜索）
        handler_ids = ZTUser.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 构建查询条件
        search_query = Q(id__icontains=query) | Q(order_no__icontains=query) | Q(short_desc__icontains=query)
        
        # 添加关联字段的查询
        if customer_ids:
            search_query |= Q(customer_id__in=customer_ids)
        if handler_ids:
            search_query |= Q(handler_id__in=handler_ids)
        
        # 执行查询
        sales_returns = queryset.filter(search_query).distinct()
        
        # 序列化结果
        page = self.paginate_queryset(sales_returns)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = self.get_serializer(sales_returns, many=True)
        return make_response(code=0, msg='搜索成功', status=status.HTTP_200_OK, data=serializer.data)

    def _validate_items_in_sales_out(self, sales_out, items_data):
        if sales_out:
            sales_out_item_ids = set(SalesOutItem.objects.filter(sales_out=sales_out).values_list('item_id', flat=True))
            for item_data in items_data:
                if item_data['item'].id not in sales_out_item_ids:
                    raise WmsException(_T("物品【{name}】在销售出库单中不存在", name=item_data['item'].name), ErrorCode.VALIDATION_ERROR)

    def _validate_item_quantity_positive(self, items_data):
        for item_data in items_data:
            quantity = Decimal(item_data['quantity'])
            if quantity <= 0:
                raise WmsException(_T("物品数量必须大于0"), ErrorCode.FIELD_RANGE_ERROR)

    def _validate_return_quantity_not_exceed(self, sales_out, items_data):
        is_valid, error_msg = SalesReturnViewSet.validate_return_quantities(sales_out, items_data)
        if not is_valid:
            raise WmsException(_T(error_msg), ErrorCode.FIELD_RANGE_ERROR)

    def _calc_total_amount(self, items_data):
        total_amount = Decimal('0')
        for item_data in items_data:
            total_amount += Decimal(item_data['quantity']) * Decimal(item_data['price'])
        return total_amount

    @staticmethod
    def _validate_total_amount(request, total_amount, total_discount):
        """验证前端传入的总金额是否与服务器计算的总金额一致（统一精度后精确比较）"""
        # 确保使用Decimal类型并统一精度
        precision = Decimal('0.00000001')  # 8位小数精度
        
        # 获取付款优惠
        payment_discount = Decimal(str(request.data.get('payment_discount', '0')))
        
        # 转换为字符串再转Decimal，避免浮点数转换问题
        frontend_amount = Decimal(str(request.data.get('total_amount'))).quantize(precision, rounding=ROUND_HALF_UP)
        calculated_amount = Decimal(str(total_amount - total_discount)).quantize(precision, rounding=ROUND_HALF_UP)
        
        if frontend_amount != calculated_amount:
            log.warning(f"总金额验证失败: 前端传入={frontend_amount}, 服务器计算={calculated_amount}")
            raise WmsException(_T("前端传入的金额与服务器计算的金额不一致"), ErrorCode.VALIDATION_ERROR)

    @staticmethod
    def _update_main_fields(sales_return, serializer, handler, warehouse, payment_method, total_amount, total_discount):
        for field, value in serializer.validated_data.items():
            setattr(sales_return, field, value)
        if handler:
            sales_return.handler = handler
        if warehouse:
            sales_return.warehouse = warehouse
        if payment_method:
            sales_return.payment_method = payment_method
        
        # 处理payment_discount字段
        payment_discount = serializer.validated_data.get('payment_discount', sales_return.payment_discount or Decimal('0'))
        sales_return.payment_discount = payment_discount
        
        # 如果有物品数据，更新物品种类数量
        items_data = serializer.validated_data.get('items', None)
        if items_data:
            item_count = SalesReturnViewSet.count_unique_items(items_data)
            sales_return.item_count = item_count
        
        sales_return.total_amount = total_amount - total_discount
        sales_return.discount = total_discount
        sales_return.save()

    @staticmethod
    def _update_items(sales_return, items_data, handler):
        item_ids = []
        for item_data in items_data:
            item_id = item_data.get('id', 0)
            if item_id:
                item = SalesReturnItem.objects.get(id=item_id, sales_return=sales_return)
                for field, value in item_data.items():
                    setattr(item, field, value)
                item.save()
            else:
                item = SalesReturnItem.objects.create(**item_data, sales_return=sales_return, handler=handler)
            item_ids.append(item.id)
        SalesReturnItem.objects.filter(sales_return=sales_return).exclude(id__in=item_ids).delete()
        
        # 更新简要描述
        sales_return.short_desc = CommonTools.generate_short_desc(items_data)
        sales_return.save()

    @staticmethod
    def _make_response(sales_return):
        return make_response(
            code=0,
            msg='更新成功',
            data=SalesReturnSerializer(sales_return).data,
            status=status.HTTP_200_OK
        )

    @staticmethod
    def is_retail(sales_out=None, order_type=None):
        """
        判断是否为零售退货单
        优先使用指定的order_type，如果未指定则从销售出库单获取
        """
        if order_type:
            return order_type == 'retail'
        
        if sales_out:
            return sales_out.document_type == 'retail'
        
        return False
        
    @staticmethod
    def is_wholesale(sales_out=None, order_type=None):
        """
        判断是否为批发退货单
        优先使用指定的order_type，如果未指定则从销售出库单获取
        """
        if order_type:
            return order_type == 'wholesale'
        
        if sales_out:
            return sales_out.document_type == 'wholesale'
        
        return True  # 默认为批发

    @staticmethod
    def generate_order_no(order_type=None):
        """
        根据订单类型生成订单编号
        """
        if order_type == 'retail':
            return Counter.DayCounter("SRR")  # Sales Return Retail
        else:
            return Counter.DayCounter("SRW")  # Sales Return Wholesale
