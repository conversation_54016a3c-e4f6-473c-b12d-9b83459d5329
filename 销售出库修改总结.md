# 销售出库修改总结

## 修改目标
将销售出库模块中的收款单（Receipt）改为使用应收账本表（AccountsReceivable），但保留收款记录表（ReceiptRecord）的使用。

## 主要修改内容

### 1. 导入修改
- 添加了 `AccountsReceivable` 和 `AccountsAdmin` 的导入
- 移除了 `Receipt` 的导入
- 移除了 `ReceiptSerializer` 类

### 2. 列表序列化器方法修改

#### get_receipt_discount 方法
- **原来**: 通过 `Receipt` 收款单查找优惠金额
- **现在**: 通过 `AccountsReceivable` 应收账本计算优惠金额
- 优惠金额 = 订单金额 - 应收金额

#### get_receipt_status 方法  
- **原来**: 查询收款单的状态
- **现在**: 查询应收账本的收款状态
- 使用 `get_payment_status_display()` 方法

### 3. 零售订单业务逻辑修改

#### 正式零售订单创建
- **原来**: 创建 `Receipt` 收款单和 `ReceiptRecord` 收款记录
- **现在**: 
  1. 使用 `AccountsAdmin.create_receivable()` 创建应收账本记录
  2. 设置为已收款状态（零售订单全额收款）
  3. 创建 `ReceiptRecord` 收款记录，关联到应收账本

#### 草稿零售订单
- **现在**: 只创建销售出库物品，不创建应收账本记录

### 4. 批发订单业务逻辑修改

#### 草稿批发订单
- **原来**: 创建未收款状态的收款单
- **现在**: 创建未收款状态的应收账本记录

#### 正式批发订单
- **原来**: 复杂的预付款处理逻辑，涉及销售订单的收款单
- **现在**: 
  1. 查找销售订单的应收账本记录
  2. 计算可用预付款
  3. 创建销售出库的应收账本记录
  4. 处理预付款使用情况
  5. 创建收款记录

### 5. 详情查询修改

#### retrieve 方法
- **原来**: 查询关联的收款单数据
- **现在**: 查询关联的应收账本数据
- 返回数据结构调整：`receipt` → `accounts_receivable`

### 6. 数据流变化

#### 零售订单数据流
```
原来: SalesOut → Receipt → ReceiptRecord
现在: SalesOut → AccountsReceivable → ReceiptRecord
```

#### 批发订单数据流
```
原来: SalesOrder → Receipt → SalesOut → Receipt → ReceiptRecord
现在: SalesOrder → AccountsReceivable → SalesOut → AccountsReceivable → ReceiptRecord
```

### 7. 字段映射

| 原收款单字段 | 应收账本字段 | 说明 |
|-------------|-------------|------|
| amount | receivable_amount | 应收金额 |
| received_amount | received_amount | 已收金额 |
| receipt_status | payment_status | 收款状态 |
| discount | order_amount - receivable_amount | 优惠金额（计算得出） |
| order_id | source_order_no | 来源订单号 |

### 8. 特殊处理

#### 预付款处理
- 批发订单需要处理销售订单的预付款
- 查找销售订单的应收账本记录
- 计算可用预付款和本次使用的预付款
- 在应收账本记录的备注中记录预付款使用情况

#### 状态枚举变化
- 收款单状态: `UNPAID/PARTIAL/FULL/CANCELED`
- 应收账本状态: `UNPAID/PARTIAL/PAID`

### 9. 返回数据调整

#### API 返回数据变化
- 创建/更新接口: `receipt` → `accounts_receivable`
- 详情查询接口: `receipt` → `accounts_receivable`
- 保留 `receipt_record` 字段不变

### 10. 注意事项

1. **收款记录表保留**: `ReceiptRecord` 表继续使用，但关联字段从 `receipt` 改为 `accounts_receivable`

2. **业务逻辑保持一致**: 虽然底层数据结构改变，但业务逻辑（创建、查询）保持一致

3. **预付款处理**: 批发订单的预付款处理逻辑需要特别注意，确保与销售订单的应收账本记录正确关联

4. **向后兼容**: 前端可能需要调整以适应新的返回数据结构

## 测试建议

1. 测试零售订单的创建和查询
2. 测试批发订单的创建和预付款处理
3. 测试草稿状态的处理
4. 测试收款记录的正确创建
5. 测试列表页面的收款状态和优惠金额显示
6. 测试详情页面的数据完整性
