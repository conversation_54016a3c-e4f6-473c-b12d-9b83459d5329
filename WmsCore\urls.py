from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .control.Item import ItemViewSet, UnitTypeViewSet
#from .control.Unit import UnitViewSet
from .control.Stock import StockViewSet
from .control.Warehouse import WarehouseViewSet
from .control.Customer import CustomerViewSet
from .control.Supplier import SupplierViewSet
from .control.PurchaseOrder import PurchaseOrderViewSet
from .control.PurchaseIn import PurchaseInViewSet
#from .control.PurchaseOrderItem import PurchaseOrderItemViewSet
# from .control.PurchaseInItem import PurchaseInItemViewSet
from .control.SalesOrder import SalesOrderViewSet
from .control.SalesOrderItem import SalesOrderItemViewSet
from .control.SalesOut import SalesOutViewSet
from .control.SalesOutItem import SalesOutItemViewSet
from .control.SalesOutReturn import SalesReturnViewSet
from .control.Payment import PaymentViewSet
from .control.PurchasePayment import PurchasePaymentViewSet
from .control.StockReturn import StockReturnViewSet
#from .control.StockReturnItem import StockReturnItemViewSet
from .control.Counter import CounterViewSet
from .control.PaymentMethod import PaymentMethodViewSet
from .control.Image import ImageViewSet
from .control.Catalog import CatalogTypeTreeViewSet
from .control.Accounts import AccountsPayableViewSet, AccountsReceivableViewSet

router = DefaultRouter()
router.register(r'items', ItemViewSet)
#router.register(r'itemtypes', ItemTypeViewSet)
#router.register(r'units', UnitViewSet)
router.register(r'unittypes', UnitTypeViewSet)
router.register(r'stocks', StockViewSet, basename='stock')
router.register(r'warehouses', WarehouseViewSet)
router.register(r'customers', CustomerViewSet)
router.register(r'suppliers', SupplierViewSet)
router.register(r'purchaseorders', PurchaseOrderViewSet)
router.register(r'purchaseins', PurchaseInViewSet)
# router.register(r'purchaseorderitems', PurchaseOrderItemViewSet)
# router.register(r'purchaseinitems', PurchaseInItemViewSet)
router.register(r'salesorders', SalesOrderViewSet)
router.register(r'salesorderitems', SalesOrderItemViewSet)
router.register(r'salesouts', SalesOutViewSet)
router.register(r'salesoutitems', SalesOutItemViewSet)
router.register(r'salesreturns', SalesReturnViewSet)
router.register(r'payments', PaymentViewSet)
router.register(r'purchasepayments', PurchasePaymentViewSet)
router.register(r'stockreturns', StockReturnViewSet)
#router.register(r'stockreturnitems', StockReturnItemViewSet)
router.register(r'paymethods', PaymentMethodViewSet)
router.register(r'catalogtypes', CatalogTypeTreeViewSet)
router.register(r'accountspayable', AccountsPayableViewSet)
router.register(r'accountsreceivable', AccountsReceivableViewSet)
# router.register(r'stockreturnpayments', StockReturnPaymentViewSet)

from .control import index
from . import views
urlpatterns = [
    path("", index.index, name="index"),

    #path("test/", views.TenantAPI.as_view(), name="创建租户"),
    path("test/", views.IndexAPI.as_view(), name="test"),
    path("s2x/", views.Model2XlsAPI.as_view(), name="s2xls"),
    path("counter/", CounterViewSet.as_view(), name="counter"),
    path("images/", ImageViewSet.as_view(), name="image"),
    path('', include(router.urls)),
]

