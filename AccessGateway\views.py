import random
import string
from django.http import HttpResponse
#from django_tenants.utils import schema_context
from rest_framework import status
from rest_framework.permissions import AllowAny

# 导入模型
# 导入新的日志工具
from django.db import models
from rest_framework import serializers
from common.logger import logger as log
from .control.auth import make_response
#from .models import TenantLedger
# from .serializers import (
#     PlatformAccountSerializer
# )
from common.APIViewSafe import APIViewSafe
from common.auth.JWT import JWTUtil


# Create your views here.
def index(request, schema_name):
    log.info(f"访问AccessGateway首页，租户: {schema_name}")
    return HttpResponse(f"Hello, world. You're at the AccessGateway index. schema_name: {schema_name}")


# class PlatformAccountInfoAPIView(APIViewSafe):
#     """获取当前平台账号信息"""
#     def get(self, request):
#         log.info(f"获取平台账号信息: user_id={request.user.id}")
#         user = request.user
#         return make_response(code=0, msg='获取平台账号信息成功', data=PlatformAccountSerializer(user).data,
#                              status=status.HTTP_200_OK)


class TokenView(APIViewSafe):
    """
    处理JWT令牌相关API
    """
    no_safe = True
    permission_classes = [AllowAny]

    def post(self, request):
        """
        通过刷新令牌获取新的访问令牌
        """
        log.info("刷新令牌请求")
        # 获取刷新令牌
        refresh_token = request.data.get('refresh_token')

        if not refresh_token:
            log.warning("缺少刷新令牌")
            return make_response(
                code=1,
                msg='缺少刷新令牌',
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 验证刷新令牌
            payload = JWTUtil.verify_refresh_token(refresh_token)
            user_id = payload.get('user_id')
            account_type = payload.get('account_type')

            if not user_id or not account_type:
                log.warning("无效的刷新令牌")
                return make_response(
                    code=1,
                    msg='无效的刷新令牌',
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 使用高性能方法直接刷新访问令牌
            access_token = JWTUtil.refresh_access_token(refresh_token)

            log.info(f"刷新令牌成功: user_id={user_id} account_type={account_type} access_token={access_token}")
            return make_response(
                code=0,
                msg='刷新令牌成功',
                data={
                    'token': access_token,
                    'user_id': user_id,
                    'account_type': account_type
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            log.error(f"刷新令牌失败: {str(e)}")
            return make_response(
                code=1,
                msg='刷新令牌无效或已过期',
                status=status.HTTP_401_UNAUTHORIZED
            )

    def get(self, request):
        """
        验证当前令牌
        这个方法适用于需要简单验证当前请求头中令牌的场景
        """
        log.info("验证令牌请求")
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            log.warning("未提供认证令牌")
            return make_response(
                code=1,
                msg='未提供认证令牌',
                data={'is_valid': False},
                status=status.HTTP_401_UNAUTHORIZED
            )

        try:
            # 提取令牌
            token_type, token = auth_header.split()
            if token_type.lower() != 'bearer':
                log.warning("无效的认证方式")
                return make_response(
                    code=1,
                    msg='无效的认证方式',
                    data={'is_valid': False},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            # 验证令牌
            payload = JWTUtil.verify_token(token)
            user_id = payload.get('user_id')
            account_type = payload.get('account_type')

            log.info(f"令牌有效: user_id={user_id}")
            return make_response(
                code=0,
                msg='令牌有效',
                data={
                    'user_id': user_id,
                    'account_type': account_type,
                    'is_valid': True
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            log.error(f"验证令牌失败: {str(e)}")
            return make_response(
                code=1,
                msg='令牌无效或已过期',
                data={'is_valid': False},
                status=status.HTTP_401_UNAUTHORIZED
            )


class SchemaSwitchView(APIViewSafe):
    """临时切换 schema 的示例视图"""
    no_safe = True
    def post(self, request):
        try:
            # target_schema = request.data.get('schema_name')
            # # 方法1：使用 schema_context 上下文管理器（推荐）
            # with schema_context(target_schema):
            #     # 在这个代码块中的所有数据库操作都会使用指定的 schema
            #     # 例如：查询该 schema 下的数据
            #     data = TenantLedger.objects.filter(id=1).first()
            #     # Company.objects.create(name="test",enterprise=Enterprise.objects.get(id=1))
            #     print(data)
            #     data.name = "test"
            #     data.save()
            return make_response(
                    code=0,
                    msg='切换 schema 成功',
                    # data={'schema': data},

                    status=status.HTTP_200_OK
                )
                
        except Exception as e:
            log.error(f"切换 schema 失败: {str(e)}")
            return make_response(
                code=1,
                msg=f'切换 schema 失败: {str(e)}',
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
    # def post(self, request, target_schema):
    #     try:
    #         # 方法2：直接设置 connection 的 schema
    #         connection.set_schema(target_schema)
    #         # 在这个代码块中的所有数据库操作都会使用指定的 schema
    #         # 注意：这种方式需要手动恢复原来的 schema
    #         try:
    #             # 执行你的数据库操作
    #             data = YourModel.objects.all()
    #             return make_response(
    #                 code=0,
    #                 msg='切换 schema 成功',
    #                 data={'schema': target_schema},
    #                 status=status.HTTP_200_OK
    #             )
    #         finally:
    #             # 恢复原来的 schema
    #             connection.set_schema_to_public()
    #
    #     except Exception as e:
    #         log.error(f"切换 schema 失败: {str(e)}")
    #         return make_response(
    #             code=1,
    #             msg=f'切换 schema 失败: {str(e)}',
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )

