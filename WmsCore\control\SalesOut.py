from decimal import Decimal

from django.db import models, transaction
from django.utils import timezone
from rest_framework import status, serializers
from rest_framework.decorators import action
from django.utils.translation import gettext_lazy as _
from AccessGateway.control.auth import make_response
from WmsCore.admin.sales_out import SalesOutAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.model.ret_code import RetCode
from WmsCore.models import SalesOut, SalesOutItem, Unit, Item, SalesOrder, Warehouse, ReceiptRecord, \
    PaymentMethod, ZTUser, Customer, AccountsReceivable
from WmsCore.admin.Accounts import AccountsAdmin

from WmsCore.admin.sales_order import SalesOrderAdmin as SOA, SalesOrderAdmin
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.exception import WmsException
from common.logger import logger as log
from common.translation import _T
from common.exception import WmsException
from  WmsCore.utils.tools import CommonTools
@SerializerCollector(alias="销售出库项")
class SalesOutItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesOutItem
        fields = '__all__'

@SerializerCollector(alias="收款记录")
class ReceiptRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReceiptRecord
        fields = '__all__'

@SerializerCollector(alias="销售订单物品出库")
class SalesOrderItemSerializer(serializers.ModelSerializer):
    """销售订单物品序列化器"""
    item = serializers.PrimaryKeyRelatedField(queryset=Item.objects.all(), required=True, write_only=True,label="物品")
    item_name = serializers.CharField(source='item.name', read_only=True,label="物品名称")
    unit = serializers.PrimaryKeyRelatedField(queryset=Unit.objects.all(), required=True,label="单位")
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True,label="单位名称")
    price = serializers.DecimalField(max_digits=30, decimal_places=8, source='sale_price',label="单价")
    quantity = serializers.DecimalField(max_digits=30, decimal_places=8, required=True,label="数量")
    discount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True,label="折扣")

    class Meta:
        model = SalesOutItem
        fields = ['id', 'item', 'item_name', 'unit', 'unit_name', 'quantity', 'discount', 'price']

    def validate(self, attrs):
        item = attrs.get('item')
        unit = attrs.get('unit')
        if unit.item_id != item.id:
            raise serializers.ValidationError({"unit": f"单位【{unit}】不属于物品【{item}】，请重新选择单位"})
        return attrs

@SerializerCollector(alias="销售订单出库")
class SalesOutSerializer(serializers.ModelSerializer):
    """销售出库序列化器"""
    items = SalesOrderItemSerializer(many=True, required=True, allow_empty=False, write_only=True,label="销售出库项list")
    is_draft = serializers.BooleanField(required=True, write_only=True,label="是否为草稿or暂存")
    discount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False,label="总减免金额")
    sales_order = serializers.PrimaryKeyRelatedField(queryset=SalesOrder.objects.all(), required=False,label="销售订单")
    document_type = serializers.ChoiceField(choices=SalesOut.DOCUMENT_TYPE_CHOICES, required=True,label="订单类型（零售订单（retail）、销售批发订单（wholesale））")
    warehouse = serializers.PrimaryKeyRelatedField(queryset=Warehouse.objects.all(), required=False,label="仓库")
    payment_method = serializers.PrimaryKeyRelatedField(queryset=PaymentMethod.objects.all(), required=False,label="收款方式id")
    pay_amount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True,label="已收款金额")
    total_sale_price = serializers.DecimalField(max_digits=30, decimal_places=8, required=True,label="总金额=应收款")
    handler = serializers.PrimaryKeyRelatedField(queryset=ZTUser.objects.all(), required=False,label="经手人")
    customer = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), required=True,label="客户")
    remark = serializers.CharField(required =False,allow_blank=True, label="备注")
    
    # 只读字段，用于返回关联对象的详细信息
    customer_name = serializers.CharField(source='customer.name', read_only=True, label="客户名称")
    sales_order_id = serializers.CharField(source='sales_order.order_id', read_only=True, label="销售订单编号")
    handler_name = serializers.CharField(source='handler.name', read_only=True, label="经办人姓名")
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True, label="仓库名称")
    controller_name = serializers.CharField(source='controller.name', read_only=True, label="操作者姓名")
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True, label="单据类型显示")
    payment_method_name = serializers.CharField(source='payment_method.account_name', read_only=True, label="收款方式名称")
    short_desc=serializers.CharField(read_only=True,source='sales_order.short_desc', label="简称")
    order_no=serializers.CharField(read_only=True, label="订单编号")
    class Meta:
        model = SalesOut
        fields = ['is_draft', 'sales_order', 'sales_order_id', 'items', 'document_type', 'document_type_display', 
                  'discount', 'handler', 'handler_name', 'warehouse', 'warehouse_name', 'payment_method', 
                  'payment_method_name', 'total_sale_price', 'customer', 'customer_name', 'id', 'remark',
                  'short_desc','order_no',
                  'pay_amount', 'out_date', 'total_cost', 'controller_name']
        read_only_fields = ('id', 'out_date', 'total_cost', 'controller_name', 'customer_name', 
                           'sales_order_id', 'handler_name', 'warehouse_name', 'document_type_display',
                            'short_desc','order_no'
                           'payment_method_name')
        write_only_fields = ['items']

@SerializerCollector(alias="销售出库单列表")
class SalesOutListSerializer(serializers.ModelSerializer):
    """销售出库列表序列化器"""
    customer_name = serializers.CharField(source='customer.name', read_only=True, label="客户名称")
    sales_order_id = serializers.CharField(source='sales_order.order_id', read_only=True, label="销售订单编号")
    handler_name = serializers.CharField(source='handler.name', read_only=True, label="经办人姓名")
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True, label="仓库名称")
    controller_name = serializers.CharField(source='controller.name', read_only=True, label="操作者姓名")
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True, label="单据类型显示")

    # 新增字段
    # item_names = serializers.SerializerMethodField(label="商品名称前三个")
    item_count = serializers.SerializerMethodField(label="物品总数量")
    total_amount = serializers.DecimalField(source='total_sale_price', max_digits=30, decimal_places=8, read_only=True, label="金额合计")
    receipt_discount = serializers.SerializerMethodField(label="收款优惠")
    receipt_status = serializers.SerializerMethodField(label="收款状态")
    document_date = serializers.DateField(source='out_date', read_only=True, label="单据日期")
    short_desc=serializers.CharField(read_only=True, label="简称")
    # order_no=serializers.CharField(read_only=True,source='order_no', label="销售出库订单编号")
    class Meta:
        model = SalesOut
        fields = [
            'id', 'document_type', 'document_type_display', 'customer', 'customer_name',
            'sales_order', 'sales_order_id', 'handler', 'handler_name', 'out_date', 'document_date',
            'warehouse', 'warehouse_name', 'total_cost', 'total_sale_price', 'total_amount', 'remark',
            'discount', 'is_draft', 'controller', 'controller_name','short_desc','order_no',
            'item_count', 'receipt_discount', 'receipt_status'
        ]



    def get_item_count(self, obj):
        """获取订单物品总数量，按物品ID统计，忽略单位差异"""
        return SalesOutItem.objects.filter(sales_out=obj).values('item').distinct().count()

    def get_receipt_discount(self, obj):
        """获取对应应收账本的收款优惠"""
        try:
            # 查找关联的应收账本记录
            accounts_receivable = AccountsReceivable.objects.filter(
                source_order_no=str(obj.id),
                sales_out=obj
            ).first()

            if not accounts_receivable:
                return Decimal('0')

            # 计算优惠金额 = 订单金额 - 应收金额
            return accounts_receivable.order_amount - accounts_receivable.receivable_amount
        except:
            return Decimal('0')

    def get_receipt_status(self, obj):
        """获取对应应收账本的收款状态"""
        try:
            # 查找关联的应收账本记录
            accounts_receivable = AccountsReceivable.objects.filter(
                source_order_no=str(obj.id),
                sales_out=obj
            ).first()
            return accounts_receivable.get_payment_status_display() if accounts_receivable else "未收款"
        except:
            return "未收款"


class SalesOutViewSet(SafeModelViewSet):
    """
    销售出库的增删改查接口
    """
    queryset = SalesOut.objects.all()
    serializer_class = SalesOutSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """
        重写 get_queryset 方法，支持按客户 ID 筛选
        """
        queryset = super().get_queryset()
        
        # 获取客户 ID 参数
        customer_id = self.request.query_params.get('customer_id')
        
        # 如果提供了客户 ID，则进行筛选
        if customer_id:
            try:
                customer_id = int(customer_id)
                queryset = queryset.filter(customer_id=customer_id)
                log.info(f"按客户 ID {customer_id} 筛选销售出库单，共 {queryset.count()} 条记录")
            except ValueError:
                log.warning(f"无效的客户 ID: {customer_id}")
        
        return queryset
        
    def list(self, request, *args, **kwargs):
        """获取销售出库列表，支持按客户ID筛选"""
        customer_id = request.query_params.get('customer_id', None)
        exclude_draft = request.query_params.get('exclude_draft', None)
        order_type = request.query_params.get('document_type', None)
        
        queryset = self.get_queryset()
         # 按订单类型筛选
        if order_type in ['retail', 'wholesale']:
            queryset = queryset.filter(document_type=order_type)
        # 处理exclude_draft参数，过滤草稿状态的订单
        if exclude_draft and exclude_draft.lower() in ['true', '1', 'yes']:
            queryset = queryset.filter(is_draft=False)
        
        # 处理customer_id参数
        if customer_id:
            try:
                customer_id = int(customer_id)
                queryset = queryset.filter(customer_id=customer_id)
                # 如果没有找到对应客户ID的记录，返回空列表
                if not queryset.exists():
                    return make_response(
                        code=0,
                        msg='获取成功',
                        data={'count': 0, 'next': None, 'previous': None, 'results': []},
                        status=status.HTTP_200_OK
                    )
            except ValueError:
                # 如果customer_id无效（不能转为整数），返回空列表
                return make_response(
                    code=0,
                    msg='获取成功',
                    data={'count': 0, 'next': None, 'previous': None, 'results': []},
                    status=status.HTTP_200_OK
                )
        
        # 使用父类的list方法处理分页等逻辑
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return make_response(
            code=0,
            msg='获取成功',
            data=serializer.data,
            status=status.HTTP_200_OK
        )

    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        # 对于写操作，不需要预取关系
        if self.action in ['create', 'update', 'partial_update']:
            return super().get_serializer(*args, **kwargs)

        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()

        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            try:
                instance = self.get_queryset().get(id=instance.id)
                kwargs['instance'] = instance
            except SalesOrder.DoesNotExist:
                # 在某些情况下（如删除后），实例可能不存在，这时保持原样
                pass

        return super().get_serializer(*args, **kwargs)

    def get_serializer_class(self):
        if self.action == 'list':
            return SalesOutListSerializer
        return SalesOutSerializer

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新销售订单（包含订单物品）"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} create sales order: {serializer.validated_data}")
        if request.data.get('document_type') == 'retail':
            return self.retail(serializer, request)
        else:
            return self.wholesale(serializer, request)
    @staticmethod
    def check_total_sale_price(items_data, total_discount, total_sale_price):
        """检查订单总额是否合法"""
        # 5. 累计总金额
        total_amount = Decimal('0')
        total_sale_price= Decimal(total_sale_price)
        for item_data in items_data:
            item_data.pop('id', None)
            item_data['out_quantity'] = item_data['quantity']
            total_amount += Decimal(item_data['quantity']) * Decimal(item_data['sale_price']) - Decimal(
                item_data.get('discount', '0'))

        # 6. 更新订单总金额
        # 校验前端传入的 total_amount 是否与服务器计算的总金额一致
        if total_sale_price != total_amount - total_discount:
            raise WmsException(_T("前端传入的金额与服务器计算的金额不一致"))
        return total_amount
    def check_prepare(self, serializer, request):
        # 1. 取出前端传入的仓库和物品明细
        warehouse = serializer.validated_data.get('warehouse', None)
        items_data = serializer.validated_data.get('items', [])
        payment_method = serializer.validated_data.get('payment_method', None)
        if not items_data:
            raise WmsException(_T("销售订单必须包含至少一个物品"))
        if request.data.get('is_draft', False)==False:
            if not payment_method:
                raise WmsException(_T("非暂存状态，必须指定收款方式"))
        if request.data.get('document_type') == 'wholesale':
            if not request.data.get('sales_order'):
                raise WmsException(_T("销售批发订单必须关联销售订单"))
        # 2. 校验零售订单必须指定出库仓库
        if not warehouse:
            raise WmsException(_T("必须指定出库仓库"))




    def wholesale(self,serializer, request):
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        self.check_prepare(serializer, request)
        # 累计总金额
        total_amount = SalesOutViewSet.check_total_sale_price(items_data, total_discount,Decimal(request.data.get('total_sale_price')) )
        if request.data.get('is_draft', False):
            return SalesOutViewSet.create_or_update(serializer=serializer, request=request, is_update=False,
                                                    is_draft=True, total_amount=total_amount,is_retail=False)
        else:
            return SalesOutViewSet.create_or_update(serializer, request, is_update=False, is_draft=False,
                                                    total_amount=total_amount,is_retail=False)
        
    def retail(self, serializer, request):
        items_data = serializer.validated_data.get('items', [])
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        self.check_prepare(serializer, request)
        # 累计总金额
        total_amount = SalesOutViewSet.check_total_sale_price(items_data, total_discount, request.data.get('total_sale_price'))
        if request.data.get('is_draft', False):
            return SalesOutViewSet.create_or_update(serializer=serializer, request=request, is_update=False,
                                                    is_draft=True, total_amount=total_amount,is_retail=True)
        else:
            return SalesOutViewSet.create_or_update(serializer, request, is_update=False, is_draft=False,
                                                    total_amount=total_amount,is_retail=True)

    @staticmethod
    def _create_sales_out_items(sales_out, items_data):
        """批量创建销售出库明细"""
        sales_out_items = []
        for item in items_data:
            sales_out_items.append(
                SalesOutItem(
                    sales_out=sales_out,
                    item=item['item'],
                    quantity=item['quantity'],
                    sale_price=item['sale_price'],
                    unit=item['unit'],
                    out_cost=Decimal('0'),
                    final_cost=Decimal('0'),
                )
            )
        SalesOutItem.objects.bulk_create(sales_out_items)
        return sales_out_items

    # @staticmethod
    # def generate_short_desc(items_data):
    #     """生成简要描述：前三个不重复的物品名称，用、分隔，超过三个加..."""
    #     unique_item_names = []
    #     seen_names = set()
    #
    #     for item_data in items_data:
    #         item_name = item_data['item'].name
    #         if item_name not in seen_names:
    #             unique_item_names.append(item_name)
    #             seen_names.add(item_name)
    #             if len(unique_item_names) >= 3:
    #                 break
    #
    #     # 判断是否需要添加省略号
    #     total_unique_items = len(set(item_data['item'].name for item_data in items_data))
    #     if total_unique_items > 3:
    #         return '、'.join(unique_item_names) + '...'
    #     else:
    #         return '、'.join(unique_item_names)

    @staticmethod
    def _validate_items(items_data, warehouse):
        for item_data in items_data:
            item_instance = item_data['item']
            quantity = Decimal(item_data['quantity'])
            if quantity <= 0:
                raise WmsException(_T("物品数量必须大于0"))
            order_unit = item_data['unit']
            ok, msg = SOA.check_stock_sufficient(item_instance, warehouse, quantity, order_unit)
            if not ok:
                raise WmsException(msg)

    @staticmethod
    def _validate_total_amount(request, total_amount, total_discount):
        if request.data.get('total_sale_price') != total_amount - total_discount:
            raise WmsException(_T("前端传入的金额与服务器计算的金额不一致"))

    @staticmethod
    def create_or_update(serializer, request, total_amount=None, is_update: bool = False, is_draft: bool = False,is_retail: bool = True):
        items_data = serializer.validated_data.get('items', [])
        warehouse = serializer.validated_data.get('warehouse', None)
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        # 正式单校验明细
        if not is_draft:
            SalesOutViewSet._validate_items(items_data, warehouse)
        # 计算总金额
        if total_amount is None:
            total_amount = SalesOutViewSet.check_total_sale_price(items_data, total_discount, request.data.get('total_sale_price'))
        # 更新 or 创建
        if is_retail:
            return SalesOutViewSet.retail_create_or_update(serializer, request, total_amount, is_draft, is_update=is_update)
        else:
            return SalesOutViewSet.wholesale_create_or_update(serializer, request, total_amount, is_draft,
                                                              is_update=is_update)


    @staticmethod
    def retail_create_or_update(serializer, request, total_amount, is_draft: bool = False, is_update: bool = False):
        """零售订单创建或更新统一方法"""
        warehouse = serializer.validated_data.pop('warehouse', None)
        items_data = serializer.validated_data.pop('items', [])
        customer = serializer.validated_data.pop('customer', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        payment_method = serializer.validated_data.pop('payment_method', None)
        handler = serializer.validated_data.pop('handler', None)
        payment_method_discount = serializer.validated_data.pop('payment_method_discount', Decimal('0'))

        # 生成简要描述
        short_desc = CommonTools.generate_short_desc(items_data)

        if is_update:
            sales_out = serializer.instance
            if not sales_out.is_draft:
                raise WmsException(_T('非草稿单不允许更新修改!'))
            # 更新主表字段
            for attr, value in serializer.validated_data.items():
                setattr(sales_out, attr, value)
            # 更新简要描述
            sales_out.short_desc = short_desc
            sales_out.save()
            # 删除原有明细
            sales_out.salesoutitem_set.all().delete()
        else:
            # 创建零售出库单
            sales_out = SalesOut.objects.create(
                document_type='retail',
                sales_order=None,
                customer=customer,
                order_no=Counter.DayCounter("SOUT"),
                handler=handler if handler else request.zt_user,
                out_date=timezone.now().date(),
                discount=total_discount,
                warehouse=warehouse,
                total_sale_price=total_amount - total_discount,
                controller=request.zt_user,
                is_draft=is_draft,
                total_cost=0,
                short_desc=short_desc
            )

        if is_draft:
            # 零售的草稿就创建零售单明细，不做任何其他的收款单
            SalesOutViewSet._create_sales_out_items(sales_out, items_data)
            return make_response(code=0, msg=_T('暂存成功'), data={
               "sales_out":  SalesOutSerializer(sales_out).data,
               "sales_out_items": SalesOutItemSerializer(sales_out.salesoutitem_set.all(), many=True).data
            })
        else:
            # 创建应收账本记录
            receivable_amount = total_amount - total_discount - payment_method_discount
            accounts_receivable = AccountsAdmin.create_receivable(
                customer=customer,
                order_amount=total_amount - total_discount,
                receivable_amount=receivable_amount,
                remaining_amount=Decimal('0'),  # 零售订单全额收款
                handler=request.zt_user,
                source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                source_order_id=str(sales_out.id),
                # remark=f"零售订单 SO-{sales_out.id}",
                sales_out=sales_out,
                remark=request.data.get('remark',None),
            )

            # 设置为已收款状态
            accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PAID
            accounts_receivable.received_amount = receivable_amount
            accounts_receivable.save()

            # 创建收款记录
            receipt_record = ReceiptRecord.objects.create(
                accounts_receivable=accounts_receivable,
                payment_method=payment_method,
                amount=receivable_amount,
                handler=request.zt_user,
                payment_date=timezone.now(),
                controller=request.zt_user,
            )

            # 创建销售出库物品表，并扣减库存
            for item in items_data:
                SOA.create_sales_out_items(sales_out, item['item'], warehouse, item['sale_price'], item['quantity'])

            # 准备返回数据
            from WmsCore.control.Accounts import AccountsReceivableSerializer
            accounts_receivable_data = AccountsReceivableSerializer(accounts_receivable).data
            sales_out_data = SalesOutSerializer(sales_out).data
            sales_out_items_data = SalesOutItemSerializer(SalesOutItem.objects.filter(sales_out=sales_out), many=True).data
            receipt_record_data = ReceiptRecordSerializer(receipt_record).data

            return make_response(
                code=0,
                msg=_T('创建成功' if not is_update else '更新成功'),
                data={
                    "accounts_receivable": accounts_receivable_data,
                    "sales_out": sales_out_data,
                    "sales_out_items": sales_out_items_data,
                    "receipt_record": receipt_record_data
                },
                status=status.HTTP_201_CREATED
            )


    @staticmethod
    def wholesale_create_or_update(serializer, request, total_amount, is_draft: bool = False, is_update: bool = False):
        sales_out = serializer.instance if is_update else None
        items_data = serializer.validated_data.get('items', [])
        warehouse = serializer.validated_data.get('warehouse', None)
        total_discount = serializer.validated_data.get('discount', Decimal('0'))
        payment_method = serializer.validated_data.get('payment_method', None)
        customer = serializer.validated_data.get('customer', None)
        payment_method_discount = serializer.validated_data.get('payment_method_discount', Decimal('0'))
        pay_amount = serializer.validated_data.get('pay_amount', Decimal('0'))
        sales_order = serializer.validated_data.pop('sales_order', None)

        # 生成简要描述
        short_desc = CommonTools.generate_short_desc(items_data)

        if is_update:
            if not sales_out.is_draft:
                raise WmsException(_T('非草稿单不允许更新修改!'))
            # 更新主表字段
            for attr, value in serializer.validated_data.items():
                setattr(sales_out, attr, value)
            # 更新简要描述
            sales_out.short_desc = short_desc
            sales_out.save()
            # 删除原有明细
            sales_out.salesoutitem_set.all().delete()
            sales_out.recipt_items.all().delete()
        else:
            # 创建销售出库单（SalesOut）  销货单
            sales_out = SalesOut.objects.create(
                document_type='wholesale',
                sales_order=sales_order,
                customer=customer,
                handler=request.zt_user,
                order_no=Counter.SysCounterInt('SalesOut'),
                out_date=timezone.now().date(),
                discount=total_discount,
                warehouse=warehouse,
                total_sale_price=total_amount - total_discount,
                controller=request.zt_user,
                is_draft=is_draft,
                total_cost=0,  # 后续可根据实际出库成本更新
                short_desc=short_desc,
                payment_method=payment_method,
            )

        if is_draft:
            # 创建应收账本记录（草稿状态）
            receivable_amount = total_amount - total_discount - payment_method_discount
            accounts_receivable = AccountsAdmin.create_receivable(
                customer=customer,
                order_amount=total_amount - total_discount,
                receivable_amount=receivable_amount,
                remaining_amount=receivable_amount,  # 草稿状态未收款
                handler=request.zt_user,
                source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                source_order_id=str(sales_out.id),
                # remark=f"批发订单草稿 SO-{sales_out.id}",
                remark=request.data.get('remark',None),
                sales_out=sales_out
            )

            # 设置为未收款状态
            accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID
            accounts_receivable.received_amount = Decimal('0')
            accounts_receivable.save()

            #  创建销售出库物品表（SalesOutItem），但不扣减库存
            sales_out_items = SalesOutViewSet._create_sales_out_items(sales_out, items_data)

            # 准备返回数据
            from WmsCore.control.Accounts import AccountsReceivableSerializer
            accounts_receivable_data = AccountsReceivableSerializer(accounts_receivable).data
            sales_out_data = SalesOutSerializer(sales_out).data
            sales_out_items_data = SalesOutItemSerializer(sales_out_items, many=True).data

            return make_response(
                code=0,
                msg=_T('暂存成功'),
                data={
                    "sales_out": sales_out_data,
                    "sales_out_items": sales_out_items_data,
                    "accounts_receivable": accounts_receivable_data
                },
                status=status.HTTP_201_CREATED
            )
        else:
            # 创建销货单的重要过程 - 使用应收账本表

            # 查找销售订单的应收账本记录
            sales_order_accounts_receivable = AccountsReceivable.objects.filter(
                source_order_no=str(sales_order.id),
                source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                sales_order=sales_order
            ).first()

            # 计算预付款使用情况
            if sales_order_accounts_receivable:
                # 可用预付款 = 已收金额 - 已使用的预付款
                available_prepayment = sales_order_accounts_receivable.received_amount - getattr(sales_order_accounts_receivable, 'used_prepayment', Decimal('0'))
                # 本次需要使用的预付款
                needed_prepayment = min(available_prepayment, total_amount - total_discount - pay_amount)
            else:
                needed_prepayment = Decimal('0')

            # 创建销售出库的应收账本记录
            receivable_amount = total_amount - total_discount - payment_method_discount - needed_prepayment
            accounts_receivable = AccountsAdmin.create_receivable(
                customer=customer,
                order_amount=total_amount - total_discount,
                receivable_amount=receivable_amount,
                remaining_amount=receivable_amount - pay_amount,
                handler=request.zt_user,
                source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                source_order_id=str(sales_out.id),
                # remark=f"批发订单 SO-{sales_out.id}，使用预付款 {needed_prepayment}",
                sales_out=sales_out,
                remark=request.data.get('remark',None),
            )

            # 设置收款状态
            if pay_amount == 0:
                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID
            elif pay_amount < receivable_amount:
                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PARTIAL
            else:
                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PAID

            accounts_receivable.received_amount = pay_amount
            accounts_receivable.save()

            # 更新销售订单的预付款使用情况
            if sales_order_accounts_receivable and needed_prepayment > 0:
                # 记录已使用的预付款（这里可能需要添加字段或使用其他方式记录）
                pass

            # 创建收款记录
            receipt_record_data = None
            if pay_amount > 0:
                receipt_record = ReceiptRecord.objects.create(
                    accounts_receivable=accounts_receivable,
                    payment_method=payment_method,
                    amount=pay_amount,
                    handler=request.zt_user,
                    payment_date=timezone.now(),
                    controller=request.zt_user,
                )
                receipt_record_data = ReceiptRecordSerializer(receipt_record).data
            # 9. 创建销售出库物品表（SalesOutItem），并扣减库存
            for item in items_data:
                SOA.create_sales_out_items(sales_out, item['item'], warehouse, item['sale_price'], item['quantity'])

            # 准备返回数据
            from WmsCore.control.Accounts import AccountsReceivableSerializer
            accounts_receivable_data = AccountsReceivableSerializer(accounts_receivable).data
            sales_out_data = SalesOutSerializer(sales_out).data
            sales_out_items_data = SalesOutItemSerializer(SalesOutItem.objects.filter(sales_out=sales_out), many=True).data

            return make_response(
                code=0,
                msg=_T('创建成功 (Created successfully)'),
                data={
                    "accounts_receivable": accounts_receivable_data,
                    "sales_out": sales_out_data,
                    "sales_out_items": sales_out_items_data,
                    "receipt_record": receipt_record_data or None
                },
                status=status.HTTP_201_CREATED
            )





    def retrieve(self, request, pk=None, **kwargs):
        """获取单个销售出库详情"""
        try:
            sales_out = self.get_object()
            # 获取销售出库单数据
            sales_out_data = SalesOutSerializer(sales_out).data
            # 获取销售出库项数据，包含完整的商品和单位信息
            sales_out_items = SalesOutItem.objects.filter(sales_out=sales_out).select_related('item', 'unit__unit_type')
            sales_out_items_data = []
            for item in sales_out_items:
                sales_out_items_data.append({
                    "item": item.item.id,
                    "item_name": item.item.name,
                    "unit": item.unit.id,
                    "unit_name": item.unit.unit_type.name,
                    "quantity": item.quantity,
                    "price": item.sale_price,
                    #当前物品在这个仓库的库存
                    "stock": SalesOrderAdmin.count_stock_quantity(item.item, item.item.unit, warehouse=sales_out.warehouse)[1],
                })
            
            # 将items数据添加到sales_out_data中
            sales_out_data['items'] = sales_out_items_data

            # 获取关联的应收账本数据
            accounts_receivable = AccountsReceivable.objects.filter(
                source_order_no=str(sales_out.id),
                sales_out=sales_out
            ).first()

            from WmsCore.control.Accounts import AccountsReceivableSerializer
            accounts_receivable_data = AccountsReceivableSerializer(accounts_receivable).data if accounts_receivable else None

            # 获取收款记录数据，包含付款账号信息
            receipt_record = None
            receipt_record_data = None
            payment_discount = Decimal('0')  # 收款优惠金额

            if accounts_receivable:
                receipt_record = ReceiptRecord.objects.filter(accounts_receivable=accounts_receivable).select_related('payment_method').first()
                if receipt_record:
                    receipt_record_data = ReceiptRecordSerializer(receipt_record).data
                    # 通过收款记录表获取付款账号信息
                    if receipt_record.payment_method:
                        sales_out_data['payment_method_id'] = receipt_record.payment_method.id
                        sales_out_data['payment_method_name'] = str(receipt_record.payment_method.account_name)
                # 获取收款优惠金额 = 订单金额 - 应收金额
                payment_discount = accounts_receivable.order_amount - accounts_receivable.receivable_amount
                sales_out_data['payment_discount'] = payment_discount
            # 添加订单编号到返回数据中
            # if not sales_out_data.get('order_no'):
            #     sales_out_data['order_no'] = f"SO-{sales_out.id}"
            
            return make_response(
                code=0,
                msg=_T('获取成功'),
                status=status.HTTP_200_OK,
                data={
                    "sales_out": sales_out_data,
                    "accounts_receivable": accounts_receivable_data,
                    "receipt_record": receipt_record_data,
                }
            )
        except SalesOut.DoesNotExist:
            return make_response(code=1, msg=_T('销售出库不存在'))

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新销售出库信息，直接复用retail_create_or_update逻辑"""
        serializer = self.get_serializer(self.get_object(), data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        # 复用零售/批发的创建或更新逻辑
        return self.create_or_update(serializer, request, is_update=True, is_draft=request.data.get('is_draft', False), total_amount=None)

    def destroy(self, request, pk=None, **kwargs):
        """只能删除草稿状态的销售出库单，并删除相关明细"""
        try:
            sales_out = self.get_object()
            if not sales_out.is_draft:
                return make_response(code=1, msg=_T('只能删除草稿状态的销售出库单'), )
            # 先删除所有明细
            sales_out.salesoutitem_set.all().delete()
            sales_out.delete()
            return make_response(code=0, msg=_T('删除成功'), status=status.HTTP_200_OK)
        except SalesOut.DoesNotExist:
            return make_response(code=1, msg=_T('销售出库不存在'))

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索销售出库"""
        query = request.query_params.get('query', '').strip()
        document_type = request.query_params.get('document_type', None)
        
        if not query:
            return make_response(code=1, msg=_T('请输入搜索关键词'), status=status.HTTP_400_BAD_REQUEST)
        
        # 基础查询集
        queryset = self.get_queryset()
        
        # 按单据类型筛选
        if document_type in ['retail', 'wholesale']:
            queryset = queryset.filter(document_type=document_type)
        
        # 先找出匹配的客户和经办人
        from django.db.models import Q
        from WmsCore.models import Customer, ZTUser, SalesOrder
        
        # 查找匹配的客户ID（根据客户名称进行搜索）
        customer_ids = Customer.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 查找匹配的经办人ID（根据经办人名称进行搜索）
        handler_ids = ZTUser.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 查找匹配的销售订单ID（通过order_id字段）
        sales_order_ids = SalesOrder.objects.filter(order_id__icontains=query).values_list('id', flat=True)
        
        # 构建查询条件
        search_query = Q(id__icontains=query) | Q(short_desc__icontains=query) | Q(remark__icontains=query)
        
        # 添加order_no字段的搜索
        search_query |= Q(order_no__icontains=query)
        
        # 添加关联字段的查询
        if customer_ids:
            search_query |= Q(customer_id__in=customer_ids)
        if handler_ids:
            search_query |= Q(handler_id__in=handler_ids)
        if sales_order_ids:
            search_query |= Q(sales_order_id__in=sales_order_ids)
        
        # 执行查询
        sales_outs = queryset.filter(search_query).distinct()
        
        # 序列化结果
        page = self.paginate_queryset(sales_outs)
        if page is not None:
            serializer = SalesOutListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = SalesOutListSerializer(sales_outs, many=True)
        return make_response(code=0, msg=_T('搜索成功'), status=status.HTTP_200_OK, data=serializer.data)


