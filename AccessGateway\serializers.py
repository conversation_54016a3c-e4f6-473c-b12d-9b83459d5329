from rest_framework import serializers
import uuid
import re
#from AccessGateway.model.account import SystemAccount, PlatformAccount 
from AccessGateway.models import Enterprise
from rest_framework.validators import UniqueValidator


# class SystemAccountSerializer(serializers.ModelSerializer):
#     platform_account_id = serializers.IntegerField(write_only=True)
    
#     class Meta:
#         model = SystemAccount
#         fields = ['id', 'account_no', 'nickname', 'avatar', 
#                   'account_type', 'tenant_id', 'status', 'platform_account_id',
#                   'last_login_at', 'created_at', 'updated_at']
#         read_only_fields = ['id', 'account_no', 'last_login_at', 'created_at', 'updated_at']
    
#     def validate_mobile(self, value):
#         """验证手机号格式"""
#         if not re.match(r'^1[3-9]\d{9}$', value):
#             raise serializers.ValidationError("手机号格式不正确")
#         return value
    
#     def create(self, validated_data):
#         """创建系统账号时生成唯一账号编号"""
#         validated_data['account_no'] = f"ACC{uuid.uuid4().hex[:16].upper()}"
#         password = self.context['request'].data.get('password')
#         if not password:
#             raise serializers.ValidationError({"password": ["密码不能为空"]})
        
#         instance = super().create(validated_data)
#         instance.set_password(password)
#         instance.save()
#         return instance


# class PlatformAccountSerializer(serializers.ModelSerializer):
#     system_accounts = SystemAccountSerializer(many=True, read_only=True)
    
#     class Meta:
#         model = PlatformAccount
#         fields = ['id', 'platform_type', 'platform_uid', 'platform_info', 
#                   'mobile', 'is_verified', 'system_accounts', 'enterprise', 'owner_enterprise',
#                   'status', 'account_type', 'created_at', 'updated_at']
#         read_only_fields = ['id', 'created_at', 'updated_at']
    
#     def validate(self, attrs):
#         """验证平台类型和平台UID的组合是否唯一"""
#         platform_type = attrs.get('platform_type')
#         platform_uid = attrs.get('platform_uid')
        
#         # 创建操作才需要验证唯一性
#         if self.instance is None:
#             if PlatformAccount.objects.filter(
#                 platform_type=platform_type, 
#                 platform_uid=platform_uid
#             ).exists():
#                 raise serializers.ValidationError({"non_field_errors": ["该平台账号已存在"]})
        
#         return attrs


# class PlatformLoginSerializer(serializers.Serializer):
#     """平台登录序列化器"""
#     platform_type = serializers.ChoiceField(choices=PlatformAccount.PLATFORM_TYPE_CHOICES)
#     platform_uid = serializers.CharField(max_length=100)
#     platform_info = serializers.JSONField(required=False)
#     mobile = serializers.CharField(max_length=20, required=False)


# class MobileLoginSerializer(serializers.Serializer):
#     """手机号登录序列化器"""
#     mobile = serializers.CharField(max_length=20)
#     platform_type = serializers.ChoiceField(choices=PlatformAccount.PLATFORM_TYPE_CHOICES, default='mobile')
    
#     def validate_mobile(self, value):
#         """验证手机号格式"""
#         if not re.match(r'^1[3-9]\d{9}$', value):
#             raise serializers.ValidationError("手机号格式不正确")
#         return value


# class PlatformRegisterSerializer(serializers.Serializer):
#     """平台账号注册序列化器"""
#     platform_type = serializers.ChoiceField(choices=PlatformAccount.PLATFORM_TYPE_CHOICES)
#     platform_uid = serializers.CharField(max_length=100)
#     platform_info = serializers.JSONField(required=False)
#     mobile = serializers.CharField(max_length=20, required=False)
    
#     def validate(self, attrs):
#         """验证平台账号是否已存在"""
#         platform_type = attrs.get('platform_type')
#         platform_uid = attrs.get('platform_uid')
#         if PlatformAccount.objects.filter(
#             platform_type=platform_type, 
#             platform_uid=platform_uid
#         ).exists():
#             raise serializers.ValidationError({"non_field_errors": ["该平台账号已存在"]})
        
#         return attrs


# class SystemRegisterSerializer(serializers.Serializer):
#     """系统账号注册序列化器"""
#     mobile = serializers.CharField(max_length=20)
#     password = serializers.CharField(max_length=128, write_only=True)
#     nickname = serializers.CharField(max_length=50, required=False)
#     platform_account_id = serializers.IntegerField()
    
#     def validate_mobile(self, value):
#         """验证手机号格式"""
#         if not re.match(r'^1[3-9]\d{9}$', value):
#             raise serializers.ValidationError("手机号格式不正确")
#         return value
    
#     def validate(self, attrs):
#         """验证平台账号是否存在"""
#         platform_account_id = attrs.get('platform_account_id')
#         try:
#             platform_account = PlatformAccount.objects.get(id=platform_account_id)
#         except PlatformAccount.DoesNotExist:
#             raise serializers.ValidationError({"platform_account_id": ["平台账号不存在"]})
        
#         # 验证该平台账号下是否已存在同手机号的系统账号
#         mobile = attrs.get('mobile')
#         if SystemAccount.objects.filter(platform_account=platform_account, mobile=mobile).exists():
#             raise serializers.ValidationError({"mobile": ["该手机号已在此平台下注册"]})
        
#         return attrs


# class SystemLoginSerializer(serializers.Serializer):
#     """系统账号登录序列化器"""
#     account_no = serializers.CharField(max_length=32)
#     password = serializers.CharField(max_length=128, write_only=True)
#     platform_account_id = serializers.IntegerField()


# class WXPlatformLoginResponseSerializer(serializers.Serializer):
#     """平台登录响应序列化器"""
#     openid = serializers.CharField(max_length=100, required=True)
#     phone  = serializers.CharField(max_length=20,required=True)


class EnterpriseSerializer(serializers.ModelSerializer):
    """创建企业序列化器"""
    enterprise_code = serializers.CharField(
        max_length=32,
        error_messages={'unique': '创建失败: 企业编码已存在'},
        # 添加显式唯一验证器
        validators=[UniqueValidator(queryset=Enterprise.objects.all(), message='创建失败: 企业编码已存在')]
    )
    
    class Meta:
        model = Enterprise
        fields = ['id','enterprise_code', 'enterprise_name', 'enterprise_phone', 'enterprise_address', 'enterprise_type']

# class SystemAccountLoginSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = SystemAccount
#         fields = [ 'nickname', 'avatar', 'account_type', 'mobile', 'status', 'created_at', 'updated_at']


class SystemAccountListSerializer(serializers.ModelSerializer):
    """系统账号列表序列化器"""
