from channels.generic.websocket import WebsocketConsumer
from channels.exceptions import StopConsumer
# from urllib.parse import urlencode
# from wsgiref.handlers import format_date_time
# from time import mktime
import json

from AccessGateway.websocket.ifly_asr import IFlyAsr


class Asr(WebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
       
        self.remote_ws = IFlyAsr(ws_cnt=self)
        self.audio_status = 0  # 0: 第一帧, 1: 中间帧, 2: 最后一帧
        
    def websocket_connect(self, message):
        self.accept()
        # self.send(text_data=json.dumps({
        #     'type': 'connection',
        #     'message': '连接成功，准备接收音频数据'
        # }))
        
        # 建立与讯飞语音识别服务的连接
        self.remote_ws.connect_to()

    # def send_message(self, msg):
    #     self.send(text_data=json.dumps(msg))
    
    def websocket_receive(self, message):
        """接收客户端发送的音频数据"""
        try:
            # 解析消息
            if 'text' in message:
                data = json.loads(message['text'])
                msg_type = data.get('type', '')
                if msg_type == 'audio':
                    # 处理音频数据
                    audio_data = data.get('audio', '')
                    is_end = data.get('is_end', False)
                    
                    self.remote_ws.send_audio(audio_data, is_end)
                elif msg_type == 'end':
                    # 结束识别会话
                    self.remote_ws.send_audio('', True)
                    
            elif 'bytes' in message:
                # 直接接收二进制音频数据
                audio_bytes = message['bytes']
                self.remote_ws.send_binary_audio(audio_bytes, False)
                
        except Exception as e:
            print(f"54: 处理音频数据异常: {e}")
            self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'处理音频数据失败: {str(e)}'
            }))
    
    def websocket_disconnect(self, message):
        """客户端断开连接时的处理"""
        print("客户端断开连接")
        
        # 关闭与远端的连接
        if self.remote_ws:
            self.remote_ws.close()
            
        raise StopConsumer()