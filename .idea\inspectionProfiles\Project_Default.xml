<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="15">
            <item index="0" class="java.lang.String" itemvalue="python-docx" />
            <item index="1" class="java.lang.String" itemvalue="PyPDF2" />
            <item index="2" class="java.lang.String" itemvalue="python-pptx" />
            <item index="3" class="java.lang.String" itemvalue="termcolor" />
            <item index="4" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="5" class="java.lang.String" itemvalue="llama-index-embeddings-dashscope" />
            <item index="6" class="java.lang.String" itemvalue="dashscope" />
            <item index="7" class="java.lang.String" itemvalue="llama-index-postprocessor-dashscope-rerank-custom" />
            <item index="8" class="java.lang.String" itemvalue="fire" />
            <item index="9" class="java.lang.String" itemvalue="llama-index-vector-stores-elasticsearch" />
            <item index="10" class="java.lang.String" itemvalue="questionary" />
            <item index="11" class="java.lang.String" itemvalue="llama-index" />
            <item index="12" class="java.lang.String" itemvalue="llama-index-llms-dashscope" />
            <item index="13" class="java.lang.String" itemvalue="pyfiglet" />
            <item index="14" class="java.lang.String" itemvalue="langgraph" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>