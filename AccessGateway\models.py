from django.db import models
from django_multitenant.models import TenantModel
from django.contrib.auth.models import AbstractUser
from WmsCore.model.base import TenantModelManager, TenantModelMixin



class Enterprise(models.Model):
    """企业信息表"""
    enterprise_code = models.CharField(max_length=32, unique=True,null=True, verbose_name='企业编码')
    enterprise_name = models.CharField(max_length=50,null=False, verbose_name='企业名称')
    enterprise_type = models.CharField(max_length=20,null=True, verbose_name='企业类型')
    enterprise_address = models.CharField(max_length=100,null=True, verbose_name='企业地址')
    enterprise_phone = models.CharField(max_length=20,null=True, verbose_name='企业电话')
    member_count = models.IntegerField(default=10, verbose_name='成员数')

    class Meta:
        db_table = "cxz_enterprise"

class Ledger(TenantModel):
    name = models.CharField(max_length=100)
    ledger_name = models.CharField(max_length=100)
    enterprise = models.ForeignKey(Enterprise, on_delete=models.PROTECT)
    created_on = models.DateField(auto_now_add=True)

    class TenantMeta:
        tenant_field_name = "id"

    class Meta:
        db_table = "cxz_ledger"

class LedgerModel(TenantModel, TenantModelMixin):
    """
    多租户模型基类，所有业务模型都应该继承此类
    自动提供租户数据隔离功能
    """
    ledger = models.ForeignKey(Ledger, on_delete=models.PROTECT)
    
    # 使用自定义的租户感知管理器
    objects = TenantModelManager()
    
    class TenantMeta:
        tenant_field_name = "ledger_id"
        
    class Meta:
        abstract = True

class CXZUser(AbstractUser):
#    pass

# Create your models here.
#class PlatformAccount(models.Model):
    """平台账号表"""
    PLATFORM_TYPE_CHOICES = (
        ('wechat_mini', '微信小程序'),
        ('alipay', '支付宝'),
        ('mobile', '手机号'),
    )
    class PlatFormChoices(models.TextChoices):
        WECHAT_MINI = 'wechat_mini', '微信小程序'
        ALIPAY = 'alipay', '支付宝'
        MOBILE = 'mobile', '手机号'
    class StatusChoices(models.IntegerChoices):
        DISABLE = 0, '禁用'
        ENABLE = 1, '启用'
    class VerifyStatusChoices(models.IntegerChoices):
        UNVERIFIED = 0, '未验证'
        VERIFIED = 1, '已验证'
    class AccountTypeChoices(models.TextChoices):
        ADMIN = 'admin', '管理员'
        MEMBER = 'member', '普通成员'
        VISITOR = 'visitor', '访客'

    platform_type = models.CharField(max_length=20, choices=PlatFormChoices, verbose_name='平台类型')
    platform_uid = models.CharField(max_length=100, verbose_name='平台唯一标识')
    platform_info = models.JSONField(null=True, blank=True, verbose_name='平台相关信息')
    nick_name = models.CharField(max_length=100, null=True, blank=True, verbose_name='昵称')
    wechat_openid = models.CharField(max_length=100, null=True, blank=True, verbose_name='微信openid')
    mobile = models.CharField(max_length=20, null=True, blank=True, unique=True,verbose_name='手机号')
    is_verified = models.BooleanField(default=0, choices=VerifyStatusChoices, verbose_name='是否已验证')
    status = models.BooleanField(default=1, choices=StatusChoices, verbose_name='状态')
    account_type = models.CharField(max_length=20, choices=AccountTypeChoices, default=AccountTypeChoices.MEMBER,
                                    verbose_name='账号类型')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    last_ledger_id = models.IntegerField(null=True, blank=True, verbose_name='最后操作的账套ID')
    enterprise = models.ForeignKey(
        related_name='platform_accounts',  # 改为 platform_accounts
        null=True,
        to='Enterprise',
        on_delete=models.PROTECT,
        verbose_name='所属企业'
    )

    owner_enterprise = models.ForeignKey(
        related_name='owner_platform_accounts',  # 改为 owner_platform_accounts
        null=True,
        to='Enterprise',
        on_delete=models.PROTECT,
        verbose_name='自己的企业'
    )
    class Meta:
        db_table = 'cxz_user'
        verbose_name = '平台账号'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['mobile'], name='idx_mobile'),
        ]

    def __str__(self):
        return f"{self.platform_type}:{self.platform_uid}"



class StatusPermission(models.Model):
    """状态权限表"""
    class StatusChoices(models.IntegerChoices):
        ENABLE = 1, '启用'
        DISABLE = 0, '注销'
    class FrozenStatusChoices(models.IntegerChoices):
        ALLOW = 0, '允许使用'
        REMOVE = 1, '移除或冻结'

    platform_account = models.ForeignKey(
        CXZUser,
        on_delete=models.CASCADE,
        related_name='account_permission'
    )
    # tenant = models.ForeignKey(
    #     Ledger,
    #     on_delete=models.CASCADE,
    #     related_name='tenant_permission'
    # )
    ledger = models.ForeignKey(Ledger, on_delete=models.CASCADE, related_name='tenant_permission')
    nick_name = models.CharField(max_length=100, null=True, blank=True, verbose_name='昵称') #默认 cxzuser.username
    status = models.BooleanField(default=0, choices=StatusChoices, verbose_name='状态')
    frozen_status = models.BooleanField(default=0, choices=FrozenStatusChoices, verbose_name='冻结状态，管理员操作')
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'cxz_status_permission'
        verbose_name = '状态权限'
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=['platform_account', 'ledger_id'],
                name='unique_platform_account_tenant'
            )
        ]

