from decimal import Decimal

from django.db import models, transaction
from django.db import IntegrityError
from django.db.models import Sum, Q
from rest_framework import status, serializers
from rest_framework.decorators import action
from django.utils.translation import gettext_lazy as _
from AccessGateway.control.auth import make_response
from WmsCore.admin.warehouse import WarehouseAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import Warehouse, Stock, Item
from WmsCore.utils.submission import prevent_duplicate_submission
from common.exception import WmsException
from common.make_response import make_response
from WmsCore.data.make_datas import create_initial_data, make_default_data_to_db


class WarehouseSerializer(serializers.ModelSerializer):
    location= serializers.CharField(required=False,allow_blank=True,allow_null=True)
    id=serializers.IntegerField(required=False,read_only= True)
    class Meta:
        model = Warehouse
        fields = ['id', 'name', 'location', 'is_parent', 'parent']

    def validate(self, attrs):
        is_parent = attrs.get('is_parent')
        parent = attrs.get('parent')
        # 创建时 parent 可能在 initial_data 里
        if is_parent in [False, 0, 'false', 'False', '0', None]:
            # 允许 parent=0/None 但必须明确传递
            if parent is None:
                raise WmsException(_("非父仓库必须指定父节点"))
        return attrs

    def validate_parent(self, value):
        if value and value.parent is not None:
            raise WmsException(_("只能设置两级仓库，父级不能再有父级"))
        return value

class WarehouseViewSet(SafeModelViewSet):
    """
    仓库管理的增删改查接口
    """
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    pagination_class = StandardResultsSetPagination
    
    def validate_warehouse_data(self, data, instance=None):
        """
        通用的仓库数据验证方法，用于创建和更新操作
        
        Args:
            data: 请求数据
            instance: 当前实例（更新操作时提供，创建操作时为None）
            
        Returns:
            (is_valid, error_message): 验证是否通过及错误信息
        """
        # 检查名称是否重复
        name = data.get('name')
        if name:
            query = Warehouse.objects.filter(name=name)
            # 更新操作需要排除当前实例
            if instance:
                query = query.exclude(id=instance.id)
            
            if query.exists():
                return False, _("仓库名称已存在，请使用其他名称")
        
        # 检查从非父仓变为父仓时的条件
        is_parent_new = data.get('is_parent')
        if instance and is_parent_new in [True, 'true', 'True', 1, '1'] and not instance.is_parent:
            # 检查该仓库下是否有关联物品（如库存/商品等）
            has_stock = Stock.objects.filter(warehouse=instance).exists()
            has_item = Item.objects.filter(location=instance.name).exists()
            if has_stock or has_item:
                return False, _("该仓库下存在物品或库存，不能设置为父仓")
        
        # 检查从父仓变为子仓时的条件
        if instance and is_parent_new in [False, 'false', 'False', 0, '0'] and instance.is_parent:
            # 检查是否有子仓库
            if Warehouse.objects.filter(parent=instance).exists():
                return False, _("该仓库下存在子仓库，不能设置为子仓")
        
        return True, None

    def get_warehouse_tree(self, queryset=None):
        if queryset is None:
            queryset = Warehouse.objects.all()
        children_map = {}
        for w in queryset:
            children_map.setdefault(w.parent_id, []).append(w)
        tree = []
        for w in queryset:
            if w.parent_id is None:
                tree.append(w)
        def build_node(node):
            return {
                "id": node.id,
                "name": node.name,
                "location": node.location,
                "is_parent": node.is_parent,
                "children": [build_node(child) for child in children_map.get(node.id, [])]
            }
        return [build_node(w) for w in tree]

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新仓库"""
        # 首先验证数据
        is_valid, error_message = self.validate_warehouse_data(request.data)
        if not is_valid:
            return make_response(code=1, msg=error_message, status=status.HTTP_200_OK)
            
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            instance = serializer.save()
            serializer = self.get_serializer(instance)
            return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)
        except IntegrityError as e:
            # 捕获可能漏网的唯一性约束错误
            return make_response(code=1, msg=_("创建失败"), data={}, status=status.HTTP_200_OK)
        except Exception as e:
            return make_response(code=1, msg=_("创建失败"), data={}, status=status.HTTP_200_OK)

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个仓库详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新仓库信息"""
        instance = self.get_object()
        
        # 使用通用验证方法
        is_valid, error_message = self.validate_warehouse_data(request.data, instance)
        if not is_valid:
            return make_response(code=1, msg=error_message, data={}, status=status.HTTP_200_OK)
        
        # 如果从非父仓变为父仓，自动将 parent 设为 None
        is_parent_new = request.data.get('is_parent')
        if is_parent_new in [True, 'true', 'True', 1, '1'] and not instance.is_parent:
            data = request.data.copy()
            data['parent'] = None
        else:
            data = request.data
        
        try:
            serializer = self.get_serializer(instance, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()
            serializer = self.get_serializer(instance)
            return make_response(code=0, msg=_('更新成功'), data=serializer.data, status=status.HTTP_200_OK)
        except IntegrityError as e:
            return make_response(code=1, msg=_("更新失败"), data={}, status=status.HTTP_200_OK)
        except Exception as e:
            return make_response(code=1, msg=_("更新失败"), data={}, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def destroy(self, request, pk=None, **kwargs):
        """删除仓库"""
        instance = self.get_object()
        
        # 检查是否有子仓库
        if Warehouse.objects.filter(parent=instance).exists():
            return make_response(code=1, msg=_("该仓库下存在子仓库，无法删除"), data={}, status=status.HTTP_200_OK)        
        
        # 检查是否有库存
        if Stock.objects.filter(warehouse=instance).exists():
            return make_response(code=1, msg=_("该仓库下存在库存，无法删除"), data={}, status=status.HTTP_200_OK)
        
        instance.delete()
        return make_response(code=0, msg=_('删除成功'), data={}, status=status.HTTP_200_OK)

    def list(self, request, *args, **kwargs):
        queryset = Warehouse.objects.all()
        roots = queryset.filter(parent=None)
        def build_node(node):
            return {
                "id": node.id,
                "name": node.name,
                "location": node.location,
                "is_parent": node.is_parent,
                "children": [build_node(child) for child in queryset.filter(parent=node)]
            }
        results = [build_node(root) for root in roots]
        count = queryset.count()
        data = {
            "count": count,
            "next": None,
            "previous": None,
            "results": results
        }
        return make_response(code=0, msg=_('获取成功'), data=data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        query = request.query_params.get('query', '')
        if not query:
            data = self.get_warehouse_tree()
            return make_response(code=0, msg=_('获取成功'), data=data, status=status.HTTP_200_OK)
        warehouses = Warehouse.objects.filter(
            models.Q(name__icontains=query) |
            models.Q(location__icontains=query)
        )
        # 只返回匹配的节点及其父节点
        ids = set()
        for w in warehouses:
            ids.add(w.id)
            if w.parent_id:
                ids.add(w.parent_id)
        filtered = Warehouse.objects.filter(id__in=ids)
        data = self.get_warehouse_tree(filtered)
        return make_response(code=0, msg=_('获取成功'), data=data, status=status.HTTP_200_OK)
