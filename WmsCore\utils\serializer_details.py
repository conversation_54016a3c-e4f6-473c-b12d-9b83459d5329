# -*- coding: utf-8 -*-
from rest_framework import serializers
from collections.abc import Mapping
from common.logger import logger as log


class SerializerCollector:
    """
    装饰器类，用于收集所有被装饰的 ModelSerializer 类。
    
    使用方法:
        @SerializerCollector(alias="用户")  # 可选的别名参数
        class YourSerializer(serializers.ModelSerializer):
            pass
    """
    _registry = {}  # 格式: {class_name: {"class": class_obj, "alias": alias_name}}

    def __init__(self, alias: str = ""):
        """
        初始化方法。

        :param alias: 序列化器的别名，用于更友好的显示
        """
        self.alias = alias

    def __call__(self, cls):
        if not issubclass(cls, serializers.ModelSerializer):
            raise TypeError(f"{cls.__name__} 必须是 ModelSerializer 的子类")
        SerializerCollector._registry[cls.__name__] = {
            "class": cls,
            "alias": self.alias 
        }
        return cls

    @classmethod
    def get_all_serializers(cls) -> dict:
        """
        获取所有被收集的序列化器类
        
        :return: 包含序列化器类和别名的字典
        """
        return cls._registry


class SerializerDetailPrinter:
    """
    一个工具类，用于递归地提取和展示 Django Rest Framework 的 ModelSerializer 的字段详细信息。

    使用方法:
        printer = SerializerDetailPrinter(YourSerializer)
        details = printer.get_details()
        # details 将会是一个包含所有字段信息的列表
    """

    def __init__(self, serializer_class: type[serializers.ModelSerializer]):
        """
        初始化方法。

        :param serializer_class: 需要分析的 ModelSerializer 类。
        """
        if not isinstance(serializer_class, type) or not issubclass(serializer_class, serializers.ModelSerializer):
            raise TypeError("传入的必须是一个 serializers.ModelSerializer 的子类")
        self.serializer_class = serializer_class

    def get_details(self) -> list[dict]:
        """
        获取序列化器所有字段的详细信息，并以列表形式返回。

        :return: 一个包含字段信息的字典列表。
        """
        details = []
        # 需要一个序列化器实例来正确地检查字段
        serializer_instance = self.serializer_class()
        self._recursive_get_fields(serializer_instance, details)
        return details

    def generate_test_data(self) -> dict:
        """
        生成用于测试序列化器的示例数据。

        :return: 包含测试数据的字典。
        """
        serializer_instance = self.serializer_class()
        return self._recursive_generate_test_data(serializer_instance)

    def _recursive_generate_test_data(self, serializer_instance: serializers.Serializer, depth: int = 0) -> dict:
        """
        递归生成测试数据。

        :param serializer_instance: 序列化器实例
        :param depth: 当前递归深度，用于防止无限递归
        :return: 测试数据字典
        """
        if depth > 3:  # 防止无限递归
            return {}

        result = {}
        for field_name, field in serializer_instance.get_fields().items():
            # 跳过只读字段
            if field.read_only:
                continue

            # 处理列表序列化器
            if isinstance(field, serializers.ListSerializer):
                result[field_name] = [
                    self._recursive_generate_test_data(field.child, depth + 1)
                ]
                continue

            # 处理嵌套序列化器
            if isinstance(field, serializers.ModelSerializer):
                result[field_name] = self._recursive_generate_test_data(field, depth + 1)
                continue

            # 处理基本字段类型
            if isinstance(field, serializers.IntegerField):
                result[field_name] = 1
            elif isinstance(field, serializers.FloatField):
                result[field_name] = 1.0
            elif isinstance(field, serializers.DecimalField):
                result[field_name] = "1.00"
            elif isinstance(field, serializers.BooleanField):
                result[field_name] = True
            elif isinstance(field, serializers.DateTimeField):
                result[field_name] = "2024-03-20T10:00:00Z"
            elif isinstance(field, serializers.DateField):
                result[field_name] = "2024-03-20"
            elif isinstance(field, serializers.ChoiceField):
                # 如果有选项，使用第一个选项的值
                choices = getattr(field, 'choices', None)
                if choices and isinstance(choices, Mapping):
                    result[field_name] = next(iter(choices.keys()))
                else:
                    result[field_name] = ""
            else:
                # 默认作为字符串处理
                result[field_name] = f"test_{field_name}"

        return result

    def _recursive_get_fields(self, serializer_instance: serializers.Serializer, details_list: list, prefix: str = ''):
        """
        核心递归函数，用于遍历和解析所有字段。

        :param serializer_instance: 当前要处理的序列化器实例。
        :param details_list: 用于存储结果的列表。
        :param prefix: 用于嵌套字段的名称前缀，例如 "items."。
        """
        for field_name, field in serializer_instance.get_fields().items():
            full_field_name = f"{prefix}{field_name}"

            # 情况一: 嵌套的序列化器 (many=True)，例如 ForeignKey(many=True) 或 ManyToManyField
            if isinstance(field, serializers.ListSerializer):
                # 这是一个列表序列化器，表示 to-many 关系
                child_serializer = field.child
                # 递归调用，传递子序列化器实例和更新后的前缀
                self._recursive_get_fields(child_serializer, details_list, prefix=f"{full_field_name}.")
                continue

            # 情况二: 嵌套的序列化器 (many=False)，例如 ForeignKey
            if isinstance(field, serializers.ModelSerializer):
                # 这是一个嵌套的对象序列化器，表示 to-one 关系
                # 递归调用，传递子序列化器实例和更新后的前缀
                self._recursive_get_fields(field, details_list, prefix=f"{full_field_name}.")
                continue

            # 基础情况: 简单字段
            field_info = {
                'field_name': full_field_name,
                'field_type': type(field).__name__,
                'label': str(field.label) if field.label else field_name,
                'read_only': field.read_only,
                'help_text': str(field.help_text) if field.help_text else ''
            }

            # 处理关系型字段的特殊情况（不要触发choices属性，避免__str__调用）
            if isinstance(field, serializers.PrimaryKeyRelatedField):
                # 安全地获取关联模型信息
                try:
                    if hasattr(field, 'queryset') and field.queryset:
                        model_class = field.queryset.model
                        field_info['related_model'] = model_class.__name__
                        field_info['app_label'] = model_class._meta.app_label
                        field_info['related_name'] = getattr(field, 'source', None)
                        # 不要调用choices属性，避免触发__str__
                except Exception as e:
                    log.error(f"获取PrimaryKeyRelatedField信息时出错: {str(e)}")
            # 对于常规ChoiceField，直接获取choices属性是安全的
            elif isinstance(field, serializers.ChoiceField) and not isinstance(field, serializers.PrimaryKeyRelatedField):
                try:
                    choices = [{"value": key, "display_name": value} for key, value in field.choices.items()]
                    field_info['choices'] = choices
                except Exception as e:
                    log.error(f"获取字段{full_field_name}的choices时出错: {str(e)}")

            details_list.append(field_info)


class SerializerDetailMgr:
    @staticmethod
    def get_serializer_details(serializer_class_name: str) -> list[dict]:
        from WmsCore.utils.serializer_details import SerializerCollector
        serializer_class_mp = SerializerCollector.get_all_serializers()
        if serializer_class_name not in serializer_class_mp:
            raise ValueError(f"序列化器类 {serializer_class_name} 不存在")
        serializer_class = serializer_class_mp[serializer_class_name]["class"]
        printer = SerializerDetailPrinter(serializer_class)
        return printer.get_details()
    
    @staticmethod
    def get_serializer_classes() -> list[dict]:
        """
        获取所有被收集的序列化器类的信息列表
        
        :return: 包含序列化器名称和别名的字典列表
        """
        serializers = SerializerCollector.get_all_serializers()
        return [{"name": name, "alias": info["alias"]} for name, info in serializers.items()]

    @staticmethod
    def get_test_data(serializer_class_name: str) -> dict:
        from WmsCore.utils.serializer_details import SerializerCollector
        serializer_class_mp = SerializerCollector.get_all_serializers()
        if serializer_class_name not in serializer_class_mp:
            raise ValueError(f"序列化器类 {serializer_class_name} 不存在")
        serializer_class = serializer_class_mp[serializer_class_name]["class"]
        printer = SerializerDetailPrinter(serializer_class)
        return printer.generate_test_data()
