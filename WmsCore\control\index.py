from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

# Create your views here.
class Resp:
    code = 0;
    msg = "";
    data = {};

def index(request,schema_name:str ):
    resp = Resp();
    resp.code = 0;
    resp.msg = "Hello, world. You're at the WmsCore index.";
    return JsonResponse(resp.__dict__);

def get_tenant_list(request):
    return HttpResponse(f"Hello, world. You're at the WmsCore index.")