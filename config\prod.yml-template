# 生产环境配置

# 安全配置
secret_key: 'your-production-secret-key'

# JWT配置
jwt:
  #secret_key: 'your-production-jwt-secret-key' 不配置默认使用secret_key
  algorithm: 'HS256'
  expiration: 7  # 生产环境token有效期缩短
  refresh_expiration: 30

# 微信配置
wechat:
  appid: 'your_production_wechat_appid'
  secret: 'your_production_wechat_secret'

# 支付宝配置
alipay:
  appid: 'your_production_alipay_appid'
  private_key: 'your_production_alipay_private_key'
  public_key: 'your_production_alipay_public_key'

# 调试模式
debug: false

# 允许的主机
allowed_hosts:
  - 'your-domain.com'
  - 'www.your-domain.com'
  - 'api.your-domain.com'
  - 'localhost'
  - '127.0.0.1'

# 租户配置
tenant:
  path_prefix: 'ztx'

# 管理节点地址
manage_server:
  check_key: 'cangxiaozhu'
  check_key2: 'cangxiaozhu2'

# 数据库配置
database:
  engine: 'django_multitenant.backends.postgresql'
  name: 'postgres'
  user: 'flyso_wms'
  password: 'AApHTxouYJYrazO4eTOWIH16jzhj4LSH'
  host: '127.0.0.1'
  port: '5432'
  schema: 'public'

# Redis配置
redis:
  location: 'redis://:8MOGOhN5rzV6zaikTZv9SY94@127.0.0.1:6379/0'

# 国际化配置
language_code: 'zh-hans'
time_zone: 'Asia/Shanghai'

# 静态文件配置
static_url: '/static/'
static_root: '/var/www/static' 