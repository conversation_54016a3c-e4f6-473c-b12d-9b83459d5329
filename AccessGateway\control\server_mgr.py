from django.conf import settings
from AccessGateway.models import CXZUser
from common.APIViewSafe import APIViewSafe
from common.auth.jwt_serv_auth import JWTServiceAuth
from AccessGateway.control.auth import make_response

class ServerMgr():
    @staticmethod
    def new_token( ) -> str:
        return JWTServiceAuth.NewToken(settings.MANAGE_SERVER_CHECK_KEY)

class ServerMgrView(APIViewSafe):
    """登录视图"""
    authentication_classes = [JWTServiceAuth]

    def get(self, request, action):
        # 直接使用 action 作为方法名
        method = getattr(self, "get_" + action, None)
        if method:
            return method(request)
        return make_response(code=1, msg='Invalid action')

    def post(self, request, action):
        method = getattr(self, "post_" + action, None)
        if method:
            return method(request)
        return make_response(code=1, msg='Invalid action')

    def get_user_info(self, request):
        user_id = request.GET.get('user_id')
        if not user_id:
            return make_response(code=1, msg='User ID is required')
        user:CXZUser = CXZUser.objects.filter(id=user_id).first()
        if not user:
            return make_response(code=1, msg='User not found')
        data = {
            'user_id': user.id,
            #'user_name': user.nick_name,
            'platform_type': user.platform_type,
            #'platform_uid': user.platform_uid,
            #'platform_info': user.platform_info,
            'nick_name': user.nick_name,
            'wechat_openid': user.wechat_openid,
            'mobile': user.mobile,
            'enterprise_id': user.enterprise_id,
            #'is_verified': user.is_verified,
            #'status': user.status,
            #'account_type': user.account_type,
        }
        return make_response(code=0, msg='Userinfo', data=data)