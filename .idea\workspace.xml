<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="11d13019-8ac1-4b81-b0fc-2d52a1d25475" name="更改" comment="完善销售订单相关">
      <change beforePath="$PROJECT_DIR$/WmsCore/control/SalesOrder.py" beforeDir="false" afterPath="$PROJECT_DIR$/WmsCore/control/SalesOrder.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/WmsCore/control/SalesOut.py" beforeDir="false" afterPath="$PROJECT_DIR$/WmsCore/control/SalesOut.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/WmsCore/control/SalesOutReturn.py" beforeDir="false" afterPath="$PROJECT_DIR$/WmsCore/control/SalesOutReturn.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/WmsCore/utils/serializer_details.py" beforeDir="false" afterPath="$PROJECT_DIR$/WmsCore/utils/serializer_details.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" show_ignored="true" />
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/AccessGateway/control/cxz_login.py" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/AccessGateway/control/login.py" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/WmsCore/control/Item.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/WmsCore/control/SalesOrder.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2yGZ5N0PTXPV0u0KXElsivsje4r" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.create (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.create.executor&quot;: &quot;Run&quot;,
    &quot;Python.manage.executor&quot;: &quot;Run&quot;,
    &quot;Python.views.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Comma-separated (CSV)_id&quot;,
    &quot;database.data.extractors.current.id&quot;: &quot;Comma-separated (CSV)_id&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/下载/smartshift-1.0-SNAPSHOT.zip&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\fs\fswms_serv\WmsCore\migrations" />
      <recent name="D:\fs\fswms_serv\AccessGateway\control" />
      <recent name="D:\fs\fswms_serv\AccessGateway\utils" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\fs\fswms_serv\WmsCore\utils" />
      <recent name="D:\fs\fswms_serv\AccessGateway\control" />
      <recent name="D:\fs\fswms_serv\接口文档md" />
      <recent name="D:\fs\fswms_serv" />
    </key>
  </component>
  <component name="RunManager" selected="Python.manage">
    <configuration name="create (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="fswms_serv" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/create.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="create" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="fswms_serv" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/WmsCore" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\fs\fswms_serv\create.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="manage" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="fswms_serv" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/manage.py" />
      <option name="PARAMETERS" value="runserver  *************:8000" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="views" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="fswms_serv" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/WmsCore" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/WmsCore/views.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.create" />
      <item itemvalue="Python.create (1)" />
      <item itemvalue="Python.views" />
      <item itemvalue="Python.manage" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.manage" />
        <item itemvalue="Python.create (1)" />
        <item itemvalue="Python.create" />
        <item itemvalue="Python.views" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.25659.43" />
        <option value="bundled-python-sdk-181015f7ab06-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="11d13019-8ac1-4b81-b0fc-2d52a1d25475" name="更改" comment="" />
      <created>1749462687066</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749462687066</updated>
      <workItem from="1749462688316" duration="55891000" />
      <workItem from="1749691868347" duration="58885000" />
      <workItem from="1750037429483" duration="5574000" />
      <workItem from="1750043230769" duration="24112000" />
      <workItem from="1750123822383" duration="22942000" />
      <workItem from="1750210157214" duration="22509000" />
      <workItem from="1750296821416" duration="24069000" />
      <workItem from="1750383235736" duration="3488000" />
      <workItem from="1750388675747" duration="21409000" />
      <workItem from="1751246095468" duration="28868000" />
      <workItem from="1751333110803" duration="4494000" />
      <workItem from="1751337735049" duration="23998000" />
      <workItem from="1751368261023" duration="236000" />
      <workItem from="1751419891296" duration="28085000" />
      <workItem from="1751506012207" duration="27431000" />
      <workItem from="1751592728466" duration="21463000" />
      <workItem from="1751851599960" duration="28547000" />
      <workItem from="1751938504538" duration="1157000" />
      <workItem from="1751939675643" duration="10622000" />
      <workItem from="1751955524058" duration="15214000" />
      <workItem from="1752024796785" duration="15610000" />
      <workItem from="1752045604841" duration="14726000" />
      <workItem from="1752111213049" duration="22779000" />
      <workItem from="1752141262028" duration="4269000" />
      <workItem from="1752197393957" duration="139000" />
      <workItem from="1752197601945" duration="26850000" />
      <workItem from="1752232346085" duration="86000" />
      <workItem from="1752456702207" duration="25235000" />
      <workItem from="1752542839591" duration="22984000" />
      <workItem from="1752629100271" duration="10712000" />
      <workItem from="1752715544827" duration="26297000" />
      <workItem from="1752801860665" duration="837000" />
      <workItem from="1752802800708" duration="18070000" />
      <workItem from="1753061019259" duration="27054000" />
      <workItem from="1753148217951" duration="20208000" />
      <workItem from="1753234238569" duration="24928000" />
      <workItem from="1753320687625" duration="22240000" />
      <workItem from="1753406877593" duration="20890000" />
      <workItem from="1753665793959" duration="21641000" />
      <workItem from="1753752395732" duration="15796000" />
      <workItem from="1753780323938" duration="2298000" />
      <workItem from="1753838794156" duration="1891000" />
    </task>
    <task id="LOCAL-00021" summary="均摊入库，第三版，简单优化一下，减少db读取，修复忘记同步到采购物品数量和库存表">
      <option name="closed" value="true" />
      <created>1750410287607</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750410287607</updated>
    </task>
    <task id="LOCAL-00022" summary="均摊入库，第三版，简单优化一下，减少db读取，修复忘记同步到采购物品数量和库存表">
      <option name="closed" value="true" />
      <created>1750410471754</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1750410471754</updated>
    </task>
    <task id="LOCAL-00023" summary="均摊入库，第三版，简单优化一下，减少db读取，修复忘记同步到采购物品数量和库存表">
      <option name="closed" value="true" />
      <created>1750412493688</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1750412493688</updated>
    </task>
    <task id="LOCAL-00024" summary="增加库存退货">
      <option name="closed" value="true" />
      <created>1750417294834</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1750417294834</updated>
    </task>
    <task id="LOCAL-00025" summary="增加销售（零售）的一站式处理，&#10;批发的还没做，&#10;处理了部分bug和修改字段">
      <option name="closed" value="true" />
      <created>1751365484291</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1751365484291</updated>
    </task>
    <task id="LOCAL-00026" summary="修复防重提出现校验图片出现问题的bug，修改Item处理图片的功能，但还是报错">
      <option name="closed" value="true" />
      <created>1751368064742</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1751368064742</updated>
    </task>
    <task id="LOCAL-00027" summary="修复部分图片bug，增加通用图片curd">
      <option name="closed" value="true" />
      <created>1751440195334</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1751440195334</updated>
    </task>
    <task id="LOCAL-00028" summary="修改model">
      <option name="closed" value="true" />
      <created>1751455822517</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1751455822517</updated>
    </task>
    <task id="LOCAL-00029" summary="完善图片相关">
      <option name="closed" value="true" />
      <created>1751523465804</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1751523465804</updated>
    </task>
    <task id="LOCAL-00030" summary="优化图片相关查询性能，虽然是利用序列化器避免遍历，但实际测试消耗的时间一样。">
      <option name="closed" value="true" />
      <created>1751524227444</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1751524227444</updated>
    </task>
    <task id="LOCAL-00031" summary="枚举">
      <option name="closed" value="true" />
      <created>1751525663243</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1751525663243</updated>
    </task>
    <task id="LOCAL-00032" summary="修改">
      <option name="closed" value="true" />
      <created>1751528353239</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1751528353239</updated>
    </task>
    <task id="LOCAL-00033" summary="模型修改，移植通用功能">
      <option name="closed" value="true" />
      <created>1751538236948</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1751538236948</updated>
    </task>
    <task id="LOCAL-00034" summary="修复部分bug，进一步优化零售相关功能">
      <option name="closed" value="true" />
      <created>1751540789533</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1751540789533</updated>
    </task>
    <task id="LOCAL-00035" summary="补交销售物品相关的通用功能文件">
      <option name="closed" value="true" />
      <created>1751540857514</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1751540857514</updated>
    </task>
    <task id="LOCAL-00036" summary="错误处理">
      <option name="closed" value="true" />
      <created>1751596475387</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1751596475387</updated>
    </task>
    <task id="LOCAL-00037" summary="修复bug">
      <option name="closed" value="true" />
      <created>1751598457449</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1751598457449</updated>
    </task>
    <task id="LOCAL-00038" summary="修改model，重写统一的返回格式，优化统一报错，完善销售订单，再修复一些bug">
      <option name="closed" value="true" />
      <created>1751602515881</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1751602515881</updated>
    </task>
    <task id="LOCAL-00039" summary="修改model，重写统一的返回格式，优化统一报错，完善销售订单，再修复一些bug">
      <option name="closed" value="true" />
      <created>1751627542949</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1751627542949</updated>
    </task>
    <task id="LOCAL-00040" summary="修改model，重写统一的返回格式，优化统一报错，完善销售订单，再修复一些bug">
      <option name="closed" value="true" />
      <created>1751628468127</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1751628468127</updated>
    </task>
    <task id="LOCAL-00041" summary="修改model，重写统一的返回格式，完善销售订单处理逻辑，再修复一些bug">
      <option name="closed" value="true" />
      <created>1751887648835</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1751887648835</updated>
    </task>
    <task id="LOCAL-00042" summary="创建销售订单相关的已经基本完成">
      <option name="closed" value="true" />
      <created>1751948842539</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1751948842539</updated>
    </task>
    <task id="LOCAL-00043" summary="又改一下销售订单，增加图片的接口，修改Item的处理">
      <option name="closed" value="true" />
      <created>1751973139514</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1751973139514</updated>
    </task>
    <task id="LOCAL-00044" summary="又改一下销售订单">
      <option name="closed" value="true" />
      <created>1752046031808</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1752046031809</updated>
    </task>
    <task id="LOCAL-00045" summary="改了下销售订单相关的，因为销售订单有点繁杂，这里打算把里面的零售迁移到销售出库">
      <option name="closed" value="true" />
      <created>1752060594722</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1752060594722</updated>
    </task>
    <task id="LOCAL-00046" summary="改了下销售订单相关的，因为销售订单有点繁杂，这里打算把里面的零售迁移到销售出库，v2">
      <option name="closed" value="true" />
      <created>1752148744812</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1752148744812</updated>
    </task>
    <task id="LOCAL-00047" summary="先上传一个版本，这里没有用到库存变更流水日志，但可以用着先&#10;完善了零售和销售退货相关的">
      <option name="closed" value="true" />
      <created>1752230119056</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1752230119056</updated>
    </task>
    <task id="LOCAL-00048" summary="先上传一个版本，这里没有用到库存变更流水日志，但可以用着先&#10;完善了零售和销售退货相关的">
      <option name="closed" value="true" />
      <created>1752230134686</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1752230134686</updated>
    </task>
    <task id="LOCAL-00049" summary="开始修改销售订单">
      <option name="closed" value="true" />
      <created>1752232401463</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1752232401463</updated>
    </task>
    <task id="LOCAL-00050" summary="完善了销售订单相关的增删改查，还有一个支付相关的，还没有原型图就留着先">
      <option name="closed" value="true" />
      <created>1752466204325</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1752466204326</updated>
    </task>
    <task id="LOCAL-00051" summary="完善仓库和物品相关">
      <option name="closed" value="true" />
      <created>1752492086233</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1752492086233</updated>
    </task>
    <task id="LOCAL-00052" summary="完善仓库和物品相关,v2">
      <option name="closed" value="true" />
      <created>1752561461860</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1752561461861</updated>
    </task>
    <task id="LOCAL-00053" summary="完善了不少东西，注意图片相关的后面要处理">
      <option name="closed" value="true" />
      <created>1752577698113</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1752577698114</updated>
    </task>
    <task id="LOCAL-00054" summary="销售订单小改">
      <option name="closed" value="true" />
      <created>1752659700465</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1752659700465</updated>
    </task>
    <task id="LOCAL-00055" summary="添加bulk_create 重写方法&#10;修复部分bug">
      <option name="closed" value="true" />
      <created>1752806419832</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1752806419833</updated>
    </task>
    <task id="LOCAL-00056" summary="添加物品信息返回在途数量，和完善销售订单">
      <option name="closed" value="true" />
      <created>1752810817211</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1752810817211</updated>
    </task>
    <task id="LOCAL-00057" summary="添加物品信息返回在途数量，和完善销售订单">
      <option name="closed" value="true" />
      <created>1752837155193</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1752837155193</updated>
    </task>
    <task id="LOCAL-00058" summary="完善物品初始化能第二次初始化，处理其他bug">
      <option name="closed" value="true" />
      <created>1753068067443</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1753068067444</updated>
    </task>
    <task id="LOCAL-00059" summary="修改字段符合前端要求，并修复部分bug">
      <option name="closed" value="true" />
      <created>1753097143923</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1753097143923</updated>
    </task>
    <task id="LOCAL-00060" summary="修改字段符合前端要求，并修复部分bug">
      <option name="closed" value="true" />
      <created>1753183383693</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1753183383694</updated>
    </task>
    <task id="LOCAL-00061" summary="修改字段(物品、采购订单）符合前端要求，并修复部分bug">
      <option name="closed" value="true" />
      <created>1753259211719</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1753259211719</updated>
    </task>
    <task id="LOCAL-00062" summary="修改字段(物品、采购订单）符合前端要求，并修复部分bug">
      <option name="closed" value="true" />
      <created>1753269169061</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753269169062</updated>
    </task>
    <task id="LOCAL-00063" summary="完善采购订单状态检查">
      <option name="closed" value="true" />
      <created>1753330826796</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753330826796</updated>
    </task>
    <task id="LOCAL-00064" summary="完善采购订单状态检查">
      <option name="closed" value="true" />
      <created>1753356518588</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753356518588</updated>
    </task>
    <task id="LOCAL-00065" summary="对接前端要求">
      <option name="closed" value="true" />
      <created>1753436965315</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753436965315</updated>
    </task>
    <task id="LOCAL-00066" summary="对接前端要求">
      <option name="closed" value="true" />
      <created>1753442822380</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753442822380</updated>
    </task>
    <task id="LOCAL-00067" summary="修改销售订单对接应收表">
      <option name="closed" value="true" />
      <created>1753690030835</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1753690030836</updated>
    </task>
    <task id="LOCAL-00068" summary="完善销售订单相关">
      <option name="closed" value="true" />
      <created>1753701254204</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1753701254204</updated>
    </task>
    <task id="LOCAL-00069" summary="完善销售订单相关">
      <option name="closed" value="true" />
      <created>1753773345516</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1753773345517</updated>
    </task>
    <option name="localTasksCounter" value="70" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="87" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="87" />
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="错误处理" />
    <MESSAGE value="修复bug" />
    <MESSAGE value="修改model，重写统一的返回格式，优化统一报错，完善销售订单，再修复一些bug" />
    <MESSAGE value="修改model，重写统一的返回格式，完善销售订单处理逻辑，再修复一些bug" />
    <MESSAGE value="创建销售订单相关的已经基本完成" />
    <MESSAGE value="又改一下销售订单，增加图片的接口，修改Item的处理" />
    <MESSAGE value="又改一下销售订单" />
    <MESSAGE value="改了下销售订单相关的，因为销售订单有点繁杂，这里打算把里面的零售迁移到销售出库" />
    <MESSAGE value="改了下销售订单相关的，因为销售订单有点繁杂，这里打算把里面的零售迁移到销售出库，v2" />
    <MESSAGE value="先上传一个版本，这里没有用到库存变更流水日志，但可以用着先&#10;完善了零售和销售退货相关的" />
    <MESSAGE value="开始修改销售订单" />
    <MESSAGE value="完善了销售订单相关的增删改查，还有一个支付相关的，还没有原型图就留着先" />
    <MESSAGE value="完善仓库和物品相关" />
    <MESSAGE value="完善仓库和物品相关,v2" />
    <MESSAGE value="完善了不少东西，注意图片相关的后面要处理" />
    <MESSAGE value="销售订单小改" />
    <MESSAGE value="添加bulk_create 重写方法&#10;修复部分bug" />
    <MESSAGE value="添加物品信息返回在途数量，和完善销售订单" />
    <MESSAGE value="完善物品初始化能第二次初始化，处理其他bug" />
    <MESSAGE value="修改字段符合前端要求，并修复部分bug" />
    <MESSAGE value="修改字段(物品、采购订单）符合前端要求，并修复部分bug" />
    <MESSAGE value="完善采购订单状态检查" />
    <MESSAGE value="对接前端要求" />
    <MESSAGE value="修改销售订单对接应收表" />
    <MESSAGE value="完善销售订单相关" />
    <option name="LAST_COMMIT_MESSAGE" value="完善销售订单相关" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="item.item.expiry_days" />
        <watch expression="item_data" />
        <watch expression="item_data['item'].expiry_days" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/fswms_serv$create.coverage" NAME="create 覆盖结果" MODIFIED="1751437585022" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/WmsCore" />
    <SUITE FILE_PATH="coverage/fswms_serv$manage.coverage" NAME="manage 覆盖结果" MODIFIED="1753838882963" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/fswms_serv$create__1_.coverage" NAME="create (1) 覆盖结果" MODIFIED="1752138651948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/fswms_serv$views.coverage" NAME="views 覆盖结果" MODIFIED="1749623878723" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/WmsCore" />
  </component>
</project>