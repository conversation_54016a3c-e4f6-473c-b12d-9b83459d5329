from django.db import models, transaction
from django.http import JsonResponse
from rest_framework import status
from rest_framework.decorators import action

from WmsCore.utils.serializer_details import SerializerCollector
from common.make_response import make_response
from WmsCore.control.base import SafeModelViewSet
from WmsCore.models import Purchase<PERSON>rder, PurchaseOrderItem, PaymentMethod
from WmsCore.utils.counter import Counter
from WmsCore.utils.submission import prevent_duplicate_submission
from WmsCore.control.base import StandardResultsSetPagination
from rest_framework import serializers
from django.db.models import Prefetch
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _
from WmsCore.models import Payment
from common.exception import WmsException
import traceback;
from WmsCore.control.Image import MultiImagesField
from WmsCore.utils.tools import CommonTools

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ['id', 'amount', 'payment_date', 'payment_method' ]
        read_only_fields = ['payment_date']

class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    # id = serializers.PrimaryKeyRelatedField( required=False, allow_null=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    code = serializers.CharField(source='item.code', read_only=True)
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True)
    unit_id = serializers.IntegerField(source='unit.id', read_only=True)
    delivered_quantity = serializers.DecimalField(max_digits=30, decimal_places=8, read_only=True)
    images = MultiImagesField(required=False)
    handler_name = serializers.CharField(source='handler.name', read_only=True)
    class Meta:
        model = PurchaseOrderItem
        fields = [
            'id', 'item', 'unit', 'unit_id', 'item_name', 'code', 'unit_name',
            'quantity', 'purchase_price', 'discount_rate', 'actual_purchase_price',
            'delivered_quantity', 'images', 'handler_name'
        ]
        read_only_fields = ['actual_purchase_price', 'discount_rate', 'delivered_quantity', 'unit_id']

@SerializerCollector(alias="采购订单列表")
class PurchaseOrderListSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    handler_name = serializers.CharField(source='handler.name', read_only=True)
    class Meta:
        model = PurchaseOrder
        fields = ['id', 'order_id', 'supplier','order_date', 'expected_arrival_date','item_count', 'short_desc',
            'total_amount', 'total_actual_amount', 'handler', 'handler_name', 'order_status', 'payment_status', 'discount', 
            'deposit', 'undeducted_deposit','supplier_name'
        ]

@SerializerCollector(alias="采购订单")
class PurchaseOrderSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    # 这里得用链接名称purchaseorderitem_set,或者在PurchaseOrderItem定义里面，purchase_order的related_name='items'
    # items = PurchaseOrderItemSerializer(source='purchaseorderitem_set', many=True)
    items = PurchaseOrderItemSerializer(many=True)
    payment_method=serializers.PrimaryKeyRelatedField(allow_null=True,queryset=PaymentMethod.objects.all(),label=_('付款方式'))
    payment_method_name = serializers.CharField(source='payment_method.account_name', read_only=True, label="收款方式名称")
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'order_id', 'supplier', 'supplier_name', 'order_date', 'expected_arrival_date','item_count', 'short_desc', 'items',
            'total_amount', 'total_actual_amount', 'handler', 'order_status', 'payment_status', 'discount', 
            'deposit', 'undeducted_deposit', 'remark', 'pay_amount', 'payment_method', 'payment_method_name'
        ]
        read_only_fields = ['item_count', 'order_id', "order_status", "payment_status", "total_amount", "total_actual_amount", 'supplier_name',"deposit", "undeducted_deposit", 'handler','payment_method_name']

class PurchaseOrderViewSet(SafeModelViewSet):
    """
    采购订单的增删改查接口
    TODO: 如何更好的定制化异常的输出内容？譬如，子 foreignkey 不存在时的信息，如何更好的输出？
    """
    queryset = PurchaseOrder.objects.prefetch_related(
        Prefetch('items', queryset=PurchaseOrderItem.objects.select_related('item'))
    ).all()
    serializer_class = PurchaseOrderSerializer
    pagination_class = StandardResultsSetPagination

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'search':
            # 按照日期降序排序
            self.queryset = self.queryset.order_by('-order_date')
            return PurchaseOrderListSerializer
        return PurchaseOrderSerializer

    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        if self.action in ['create', 'update', 'partial_update']:
            # 对于写操作，不需要预取关系
            return super().get_serializer(*args, **kwargs)
        
        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()
            
        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            instance = self.get_queryset().get(id=instance.id)
            kwargs['instance'] = instance
            
        return super().get_serializer(*args, **kwargs)

    def list(self, request, *args, **kwargs):
        """获取采购订单列表"""
        response = super().list(request, *args, **kwargs)
        #serializer = self.get_serializer(response.data, many=True)
        return make_response(code=0, msg=_('获取成功'), data=response.data, status=status.HTTP_200_OK)


    # @action(detail=True, methods=['get'])
    # def list_item(self, request, pk=None):
    #     """获取采购订单的物品列表"""
    #     try:
    #         order = PurchaseOrder.objects.get(id=pk)
    #         items = PurchaseOrderItem.objects.filter(purchase_order=order).order_by('-id').all()
    #         serializer = PurchaseOrderItemSerializer(items, many=True)
    #         return make_response(code=0, msg='获取成功', data=serializer.data, status=status.HTTP_200_OK)
    #     except Exception as e:
    #         return make_response(code=1, msg=f'获取失败: {str(e)}', status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新采购订单"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} create purchase order: {serializer.validated_data}")
        order = self.perform_create(serializer, request.zt_user)
        serializer = self.get_serializer(order)
        log.debug(f"create purchase order: {serializer.data}")
        return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)

    def perform_create(self, serializer, handler):
        order_items = serializer.validated_data.pop('items', [])
        serializer.validated_data.pop('images', None)
        if len(order_items) == 0:
            raise WmsException(_("采购订单必须包含至少一个物品"))
        #payment_data = serializer.validated_data.pop('payment', None)
        
        # 先创建订单
        total_amount = serializer.validated_data.get('total_amount', 0)
        order = PurchaseOrder.objects.create(**serializer.validated_data,handler=handler, total_amount=total_amount, total_actual_amount=0 )
        order.payment_status = PurchaseOrder.PaymentStatusChoices.UNPAID
        order.order_status = PurchaseOrder.OrderStatusChoices.PENDING
        order_id = Counter.DayCounter("PO")
        order.order_id = order_id
        
        # 处理支付信息
        if order.pay_amount > 0:
            if order.payment_method is None:
                raise WmsException(_("付款方式不能为空") + " " + str(order.payment_method))
            Payment.objects.create(
                order_type=Payment.OrderTypeChoices.PURCHASE_ORDER,
                order_id=order_id,
                amount=order.pay_amount,
                actual_amount=order.pay_amount,
                supplier=order.supplier,
                payment_method=order.payment_method,
                handler=handler,
                controller=handler,
                remark=_("预付款")
            )
            order.deposit = order.pay_amount
            order.undeducted_deposit = order.pay_amount
            order.payment_status = PurchaseOrder.PaymentStatusChoices.PREPAID
            order.save()
        # 处理订单项
        total_amount = 0
        total_item_count = 0;
        discount_value = order.discount
        po_items: list[PurchaseOrderItem] = [];
        for item_data in order_items:
            item_data.pop('images', None)
            item_data.pop('id', None)
            item = PurchaseOrderItem.objects.create(**item_data, purchase_order=order)
            if item.unit.item != item.item:
                raise WmsException(_("此物品没有该计量单位" + " " + item.item.name))
            total_amount += item.quantity * item.purchase_price
            #total_item_count += item.quantity;
            po_items.append(item)
        total_item_count = len(po_items)

        discount = discount_value / total_amount * 100;
        for item in po_items:
            # 使用订单的折扣率
            item.discount_rate = discount
            item.actual_purchase_price = item.purchase_price * (100 - item.discount_rate) / 100
            item.save()

        # 使用 CommonTools 生成简要描述
        order.short_desc = CommonTools.generate_short_desc(po_items)

        # 更新订单总金额和状态
        order.total_amount = total_amount;
        order.total_actual_amount = total_amount - discount_value;
        order.item_count = total_item_count
        order.handler = handler
        order.save()
        
        log.info(f"create purchase order: {order} (id: {order.id})")
        return order

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个采购订单详情"""
        order = self.get_object()
        log.debug(f"retrieve purchase order: {order} (id: {order.id})")
        serializer = self.get_serializer(order)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新采购订单信息"""
        order = self.get_object()
        serializer = self.get_serializer(order, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} update purchase order: {serializer.validated_data}")
        order = self.perform_update(serializer, request.zt_user)
        serializer = self.get_serializer(order)
        log.debug(f"update purchase order: {serializer.data}")
        return make_response(code=0, msg=_('修改成功'), data=serializer.data, status=status.HTTP_200_OK)

    def perform_update(self, serializer, handler):
        order_items = serializer.validated_data.pop('items', [])
        #payment_data = serializer.validated_data.pop('payment', None)
        order: PurchaseOrder = serializer.save()
        #if order.order_status != PurchaseOrder.OrderStatusChoices.PENDING or order.payment_status != PurchaseOrder.PaymentStatusChoices.UNPAID:
        if order.order_status != PurchaseOrder.OrderStatusChoices.PENDING :
            raise WmsException(_("只有处于初始状态的订单才可以修改"))

        existing_ids = set()
        current_ids = set(order.items.values_list('id', flat=True))
        log.debug(f"update_items order: {order.items.all()} items_data: {order_items}")

        total_amount = 0
        total_item_count = 0;
        discount_value = order.discount
        po_items: list[PurchaseOrderItem] = [];
        for item_data in order_items:
            item_id = item_data.get('id',None)
            if item_id:
                item = PurchaseOrderItem.objects.get(id=item_id, purchase_order=order)
                for attr, value in item_data.items():
                    setattr(item, attr, value)
                log.debug(f"update item after: item.unit: {item.unit.item} item.item: {item.item}")
                if item.item != item.unit.item :
                    raise WmsException(_("此物品没有该计量单位") + " " + item.item.name)
                #item.save()
                total_amount += item.quantity * item.purchase_price;
                total_item_count += item.quantity;
                existing_ids.add(item.id)
                po_items.append(item)
            else:
                item_data.pop('images', None)
                item = PurchaseOrderItem.objects.create(**item_data, purchase_order=order)
                if item.item != item.unit.item :
                    raise WmsException(_("此物品没有该计量单位") + " " + item.item.name)
                total_amount += item.quantity * item.purchase_price;
                total_item_count += item.quantity;
                po_items.append(item)
        discount = discount_value / total_amount * 100;
        for item in po_items:
            # 使用订单的折扣率
            item.discount_rate = discount
            item.actual_purchase_price = item.purchase_price * (100 - item.discount_rate) / 100
            item.save()

        # 使用 CommonTools 生成简要描述
        order.short_desc = CommonTools.generate_short_desc(po_items)
        if order.pay_amount > 0:
            if order.pay_amount != order.deposit:
                if order.pay_amount > total_amount - discount_value:
                    raise WmsException(_("预付款金额大于订单总金额"))
                # if order.pay_amount - order.deposit > order.undeducted_deposit:
                #     raise WmsException(_("付款金额大于未被抵扣的预付款金额"))
                real_amount = order.pay_amount - order.deposit
                Payment.objects.create(
                    order_type=Payment.OrderTypeChoices.PURCHASE_ORDER,
                    order_id=order.order_id,
                    amount=order.pay_amount,
                    actual_amount=real_amount,
                    supplier=order.supplier,
                    payment_method=order.payment_method,
                    handler=handler,
                    controller=handler,
                    remark=_("预付款调整单")
                )
                # order.undeducted_deposit = order.pay_amount - (order.deposit - order.undeducted_deposit)
        order.undeducted_deposit = order.pay_amount
        order.deposit = order.pay_amount
        if order.pay_amount > 0:
            order.payment_status = PurchaseOrder.PaymentStatusChoices.PREPAID
        else:
            order.payment_status = PurchaseOrder.PaymentStatusChoices.UNPAID

        to_delete = current_ids - existing_ids
        if to_delete:
            PurchaseOrderItem.objects.filter(id__in=to_delete).delete()
        order.total_amount = total_amount ;
        order.total_actual_amount = total_amount - discount_value;
        order.item_count = total_item_count
        order.remark = serializer.validated_data.get('remark', '')
        order.save()
        log.info(f"update purchase order: {order} (id: {order.id})")
        # Refresh the order instance to get updated related items
        return self.get_queryset().get(id=order.id)

    def destroy(self, request, pk=None, **kwargs):
        """删除采购订单"""
        order = self.get_object()
        if order.order_status != PurchaseOrder.OrderStatusChoices.PENDING:
            raise WmsException(_("只有预订单才可以删除"))
        if order.order_status == PurchaseOrder.OrderStatusChoices.PENDING and order.payment_status != PurchaseOrder.PaymentStatusChoices.UNPAID:
            Payment.objects.create(
                order_type=Payment.OrderTypeChoices.PURCHASE_ORDER,
                order_id=order.order_id,
                amount=order.pay_amount,
                actual_amount=-order.pay_amount or 0,
                supplier=order.supplier,
                payment_method=order.payment_method,
                handler=request.zt_user,
                controller=request.zt_user,
                remark=_("预付款调整单")
            )
        # if order.deposit > 0:
        #     raise WmsException(_("有预付款的订单不能删除"))

        order.delete()
        return make_response(code=0, msg=_('删除成功'), data={}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索采购订单"""
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=1, msg=_('搜索失败'), data={}, status=status.HTTP_400_BAD_REQUEST)
        orders = PurchaseOrder.objects.filter(
            models.Q(handler__name__icontains=query)|
            models.Q(supplier__name__icontains=query)|
            models.Q(short_desc__icontains=query)|
            models.Q(order_id__icontains=query)
        )
        serializer = self.get_serializer(orders, many=True)
        return make_response(code=0, msg=_('搜索成功'), data=serializer.data, status=status.HTTP_200_OK)

    # @transaction.atomic
    # @prevent_duplicate_submission(timeout=3, methods=['POST'])
    # def update_status(self, request, pk=None, **kwargs):
