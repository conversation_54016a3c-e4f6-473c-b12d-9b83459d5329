# import speech_recognition as sr
import uuid
from django import forms
from django.http import HttpResponse, JsonResponse
import requests
import time
import hashlib
import base64
import json
#import pyttsx3
from aip import AipSpeech
from pydub import AudioSegment
from django.views.decorators.csrf import csrf_exempt
from AccessGateway.control.bytedance_asr import AsrWsClient

# 从文件中加载音频
def _get_file_content(file_name):
    # 要转换的音频源文件
    speech = AudioSegment.from_wav(file_name).set_frame_rate(16000)
    return speech.raw_data

#def speech_to_text_baidu(audio_path: str = "test.wav", if_microphone: bool = True):
def speech_to_text_baidu(audio_path: str = "test.wav" ):
    # https://cloud.baidu.com/product/speech 申请api
    app_id = ""
    api_key = ""
    secret_key = ""
    client = AipSpeech(app_id, api_key, secret_key)

    # 麦克风读入
    # if if_microphone:
    #     result = client.asr(_record(), 'pcm', 16000, {
    #         'dev_pid': 1537,  # 识别普通话，使用输入法模型
    #     })
    # # 文件读入
    # else:
    #     result = client.asr(_get_file_content(audio_path), 'pcm', 16000, {
    #         'dev_pid': 1537,  # 识别普通话，使用输入法模型
    #     })
    result = client.asr(_get_file_content(audio_path), 'pcm', 16000, {
            'dev_pid': 1537,  # 识别普通话，使用输入法模型
        })

    if result["err_msg"] != "success.":
        return "..."
    else:
        return result['result'][0]

# def speech_to_text_cmu(audio_path: str = "test.wav", if_microphone: bool = True):
#     # 语种
#     language_type = "zh-CN"

#     # 麦克风读入
#     if if_microphone:
#         audio = _record(if_cmu = True)
#     # 文件读入
#     else:
#         with sr.AudioFile(audio_path) as source:
#             audio = r.record(source)

#     try:
#         # print(r.recognize_sphinx(audio, language=language_type))
#         return r.recognize_sphinx(audio, language=language_type)
#     except sr.UnknownValueError:
#         print("Could not understand")
#     except sr.RequestError as e:
#         print("Sphinx error; {0}".format(e))


# def text_to_speech(sentence: str):
#     engine = pyttsx3.init()
#     engine.say(sentence)
#     engine.runAndWait()

class UploadFileForm(forms.Form):
    uid = forms.CharField(max_length=50, required=False)  # 设为可选
    file = forms.FileField()

def handle_uploaded_file(f):
    with open("./name.wav", "wb+") as destination:
        for chunk in f.chunks():
            destination.write(chunk)

@csrf_exempt
def asr_request(request):
    # 接收客户端的语音文件输入，转成文本
    start_tm = time.time()
    print(f"request: {request.POST}")
    try:
        if request.method == 'POST':
            form = UploadFileForm(request.POST, request.FILES)
            print(f"is_valid: {form.is_valid()}")
            
            if form.is_valid():
                audio_id = form.cleaned_data.get('uid', 'wechat_user');
                file = request.FILES['file']
                #handle_uploaded_file(file)
                chunk_data = b""  # 改为bytes类型
                #5M大小
                max_size = 5 * 1024 * 1024
                for chunk in file.chunks():
                    chunk_data += chunk
                    if len(chunk_data) > max_size:
                        raise Exception("chunk_data too large")
                    print(f"chunk_data: {len(chunk_data)}")
                #asr_client = AsrWsClient(cluster="volcengine_input_common")
                asr_client = AsrWsClient()
                result = asr_client.execute_one(audio_id, chunk_data)
                end_tm = time.time()
                print(f"cost time: {end_tm - start_tm} result: {result}")
                #return HttpResponse(json.dumps(result))
                return JsonResponse(result);

                #text = speech_to_text_baidu(request.FILES['file'])
            return HttpResponse("ok")
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"error: {e}")
        return HttpResponse(f"error: {e}")

@csrf_exempt
def asr_request_bytedance(request):
   #text = speech_to_text_ifly ("./name.wav")
   audio_id = str(uuid.uuid4())
   with open("./name1.wav", "rb") as f:
      chunk_data = f.read()
      start_tm = time.time()
      asr_client = AsrWsClient(cluster="volcengine_input_common")
      result = asr_client.execute_one(audio_id, chunk_data)
      end_tm = time.time()
      print(f"cost time: {end_tm - start_tm} result: {result}")
      return JsonResponse(result);
   return HttpResponse("ok")