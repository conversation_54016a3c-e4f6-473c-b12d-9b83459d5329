
from django.http import HttpResponse
from rest_framework import status
from AccessGateway.control.auth import make_response
from AccessGateway.models import CXZUser
from common.APIViewSafe import APIViewSafe
from common.auth.JWT import J<PERSON>TUtil
from common.logger import logger as log
from AccessGateway.control.platform.wechat import WechatPlatform
from AccessGateway.model.ret_code import RetCode
from common.auth.jwt_auth import JWTAuthenticationWithNoPhoneCheck
from common.translation import _T

class LoginView(APIViewSafe):
    """登录视图"""
    no_safe = True

    def WechatLogin(self, code)->HttpResponse:
        try:
            openid = WechatPlatform.GetOpenId(code);
            # openid = "test156789200";
            if openid == "":
                return make_response(
                    code=RetCode.WECHAT_LOGIN_CHECK_FAILED,
                    msg=_T("微信登录密钥校验失败"),
                    status=status.HTTP_400_BAD_REQUEST
                );
            platform_account = CXZUser.objects.filter(wechat_openid=openid).first();
            data = {
                "platform_type": "wechat_mini",
            }
            new_user = 0;
            if platform_account is None:
                platform_account = CXZUser.objects.create(
                    platform_type="wechat_mini",
                    wechat_openid=openid,
                    nick_name=_T("仓小助用户"),
                    username=openid,
                    password="cangxiaozhu",
                )
                platform_account.save()
                new_user = 1;
            if platform_account.mobile is None or platform_account.mobile == "":
                #没有绑定手机号，则需要绑定手机号
                new_user = 1;
            acc_id = platform_account.id;
            tokens = JWTUtil.generate_token(
                    user_id=acc_id,
                    account_type='platform'
                )
            data["access_token"] = tokens['access_token'];
            data["refresh_token"] = tokens['refresh_token'];
            data["isnew"] = new_user ;
            log.info(f"微信登录成功 openid: {openid} user_id: {acc_id} new_user: {new_user} access_token: {tokens['access_token']} ")

            return make_response(
                code=RetCode.SUCCESS,
                msg=_T("微信登录成功"),
                data=data,
                status=status.HTTP_200_OK
            );
        except Exception as e:
            import traceback
            traceback.print_exc()
            log.error(f"微信登录异常 - 错误: {str(e)}")
            return make_response(
                code=RetCode.UNKNOWN_ERROR,
                msg=_T("处理请求时发生错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            );

    def post(self, request):
        try:
            data = request.data
            platform_type = data.get('platform_type')
            if platform_type == 'wechat_mini':
                code = data.get('code', "")
                if code == "":
                    return make_response(
                        code=RetCode.WECHAT_LOGIN_CODE_EMPTY,
                        msg=_T("微信登录密钥不能为空"),
                        status=status.HTTP_400_BAD_REQUEST
                    );
                return self.WechatLogin(code)
            else:
                return make_response(
                    code=RetCode.UNKNOWN_ERROR,
                    msg=_T("不支持的平台类型: ") + platform_type,
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            log.error(f"统一登录注册API处理异常 - 平台: {data.get('platform_type', 'unknown')} 错误: {str(e)}")
            return make_response(
                code=RetCode.UNKNOWN_ERROR,
                msg=_T("处理请求时发生错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            );

class LoginBindPhoneView(APIViewSafe):
    authentication_classes = [JWTAuthenticationWithNoPhoneCheck]
    """登录绑定手机号"""
    def WechatBindPhone(self, user_id, code)->HttpResponse:
        try:
            log.debug(f"微信登录绑定手机号 user_id: {user_id} code: {code}")
            phone_number = WechatPlatform.GetPhoneNumber(code);
            # phone_number = "1380013001100";
            if phone_number == "":
                return make_response(
                    code=RetCode.WECHAT_LOGIN_CODE_EMPTY,
                    msg=_T("微信登录密钥不能为空"),
                    status=status.HTTP_400_BAD_REQUEST
                );

            cur_acc = CXZUser.objects.filter(id=user_id).first();
            if cur_acc is None:
                return make_response(
                    code=RetCode.WECHAT_LOGIN_CHECK_FAILED,
                    msg=_T("微信登录密钥校验失败"),
                    status=status.HTTP_400_BAD_REQUEST
                );
            if cur_acc.mobile is not None and cur_acc.mobile != "":
                return make_response(
                    code=RetCode.WECHAT_LOGIN_PHONE_EXIST, 
                    #取手机号码的最后 4 位提示
                    msg=_T("账号已绑定了手机号尾号为{mobile}的手机", mobile=cur_acc.mobile[-4:]),

                    status=status.HTTP_400_BAD_REQUEST
                );
            phone_acc = CXZUser.objects.filter(mobile=phone_number).first();
            data = {};
            if phone_acc is None:
                #直接绑定
                cur_acc.mobile = phone_number;
                cur_acc.nick_name = f"用户#{phone_number[-4:]}";
                cur_acc.save();
            else:
                #手机已经存在账号了，则查看是否已经绑定了其他人的账号了
                if phone_acc.wechat_openid is not None:
                    return make_response(
                        code=RetCode.WECHAT_LOGIN_PHONE_ALREADY_BIND,
                        msg=_T("手机号已经绑定了其他账号"),
                        status=status.HTTP_400_BAD_REQUEST
                    );
                #合并到已有账号，删除当前账号，重新生成 access_token
                phone_acc.wechat_openid = cur_acc.wechat_openid;
                phone_acc.save();
                cur_acc.delete();
                tokens = JWTUtil.generate_token(
                    user_id=phone_acc.id,
                    account_type='platform'
                )
                data = {
                    "access_token": tokens['access_token'],
                    "refresh_token": tokens['refresh_token'],
                }
                log.debug(f"微信登录绑定手机号成功 phone_number: {phone_number} user_id: {user_id} access_token: {tokens['access_token']}")
            return make_response(
                code=RetCode.SUCCESS,
                msg=_T("绑定手机号成功"),
                data=data,
                status=status.HTTP_200_OK
            )
        except Exception as e:
            log.error(f"微信登录绑定手机号异常 - 错误: {str(e)}")
            return make_response(
                code=RetCode.UNKNOWN_ERROR,
                msg=_T("处理请求时发生错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        try:
            data = request.data
            platform_type = data.get('platform_type')
            user_id = request.user_id;
            if platform_type == 'wechat_mini':
                code = data.get('code', "");
                return self.WechatBindPhone(user_id, code)
            else:
                return make_response(
                    code=RetCode.UNKNOWN_ERROR,
                    msg=_T("不支持的平台类型: ") + platform_type,
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            log.error(f"登录绑定手机号异常 - 平台: {data.get('platform_type', 'unknown')} 错误: {str(e)}")
            return make_response(
                code=RetCode.UNKNOWN_ERROR,
                msg=_T("处理请求时发生错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            );