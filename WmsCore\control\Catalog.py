# 通用分类
from django.db.models import ProtectedError
from rest_framework import serializers
from rest_framework.decorators import action
from WmsCore.control.base import HugeResultsSetPagination, SafeModelViewSet
from WmsCore.models import CatalogTypeTree
from WmsCore.utils.serializer_details import SerializerCollector
from common.exception import WmsException
from common.make_response import make_response
from common.translation import _T
from common.logger import logger
from django.db import transaction
from WmsCore.utils.submission import prevent_duplicate_submission

@SerializerCollector(alias="物品分类")
class CatalogTypeSerializer(serializers.ModelSerializer):
    for_type = serializers.CharField(write_only=True)
    class Meta:
        model = CatalogTypeTree
        fields = ['id', 'name', 'parent', 'for_type']

    def validate(self, attrs):
        # 验证for_type参数
        for_type = attrs.get('for_type')
        if not for_type or for_type not in CatalogTypeTree.ForTypeChoices.values:
            raise WmsException(_T("类型错误,不存在分类"))

        # 验证父节点层级限制
        parent = attrs.get('parent')
        if parent:
            # 检查父节点是否属于同一类型
            if parent.for_type != for_type:
                raise WmsException(_T("父节点类型不匹配"))

            # 对于所有分类类型，都限制为2层树节点
            if for_type in [CatalogTypeTree.ForTypeChoices.SUPPLIER, CatalogTypeTree.ForTypeChoices.CUSTOMER, CatalogTypeTree.ForTypeChoices.ITEM]:
                # 检查父节点是否已有父节点（即是否为第3层）
                if parent.parent:
                    if for_type == CatalogTypeTree.ForTypeChoices.ITEM:
                        raise WmsException(_T("物品分类仅支持2层树节点"))
                    elif for_type == CatalogTypeTree.ForTypeChoices.SUPPLIER:
                        raise WmsException(_T("供应商分类仅支持2层树节点"))
                    elif for_type == CatalogTypeTree.ForTypeChoices.CUSTOMER:
                        raise WmsException(_T("客户分类仅支持2层树节点"))

        return attrs

@SerializerCollector(alias="物品分类树")
class CatalogTypeTreeSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    level = serializers.SerializerMethodField()

    class Meta:
        model = CatalogTypeTree
        fields = ['id', 'name', 'parent', 'for_type', 'children', 'level']

    def get_children(self, obj):
        """递归获取子节点"""
        children = CatalogTypeTree.objects.filter(parent=obj, for_type=obj.for_type)
        return CatalogTypeTreeSerializer(children, many=True).data

    def get_level(self, obj):
        """获取节点层级"""
        level = 0
        current = obj
        while current.parent:
            level += 1
            current = current.parent
        return level

# @SerializerCollector(alias="物品分类树")
# class CatalogTypeTreeSerializer(serializers.ModelSerializer):
#     children = serializers.SerializerMethodField()

#     class Meta:
#         model = CatalogTypeTree
#         fields = ['id', 'name', 'parent', 'children']

#     def get_children(self, obj):
#         children = CatalogTypeTree.objects.filter(parent=obj)
#         return CatalogTypeTreeSerializer(children, many=True).data


class CatalogTypeTreeViewSet(SafeModelViewSet):
    """
    物品类型增删改查接口
    """
    queryset = CatalogTypeTree.objects.all()
    serializer_class = CatalogTypeSerializer
    pagination_class = HugeResultsSetPagination

    def validate_category_level(self, category, for_type):
        """
        校验分类层级，确保不超过两层

        Args:
            category: 分类实例
            for_type: 分类类型

        Raises:
            WmsException: 当层级超过限制时抛出异常
        """
        if not category:
            return True

        # 计算层级
        level = 0
        current = category
        while current.parent:
            level += 1
            current = current.parent
            if level > 1:  # 超过两层
                if for_type == CatalogTypeTree.ForTypeChoices.ITEM:
                    raise WmsException(_T("物品分类仅支持2层树节点"))
                elif for_type == CatalogTypeTree.ForTypeChoices.SUPPLIER:
                    raise WmsException(_T("供应商分类仅支持2层树节点"))
                elif for_type == CatalogTypeTree.ForTypeChoices.CUSTOMER:
                    raise WmsException(_T("客户分类仅支持2层树节点"))

        return True

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action == 'tree':
            return CatalogTypeTreeSerializer
        return CatalogTypeSerializer
    
    def get_catalog_type(self, request):
        """获取并验证for_type参数"""
        catalog_type = request.data.get('for_type') or request.query_params.get('for_type')
        if catalog_type is None or catalog_type not in CatalogTypeTree.ForTypeChoices.values:
            logger.error(f"类型错误,不存在分类: {catalog_type}")
            raise WmsException(_T("类型错误,不存在分类"))
        return catalog_type

    def get_queryset(self):
        """根据for_type过滤查询集"""
        catalog_type = self.get_catalog_type(self.request)
        return CatalogTypeTree.objects.filter(for_type=catalog_type)

    def list(self, request, *args, **kwargs):
        """获取分类列表（树形结构）"""
        queryset = self.get_queryset()
        roots = queryset.filter(parent=None)
        
        def build_node(node):
            return {
                "id": node.id,
                "name": node.name,
                "parent": node.parent_id,
                "for_type": node.for_type,
                "children": [build_node(child) for child in queryset.filter(parent=node)]
            }
        
        results = [build_node(root) for root in roots]
        count = queryset.count()
        
        data = {
            "count": count,
            "next": None,
            "previous": None,
            "results": results
        }
        return make_response(code=0, msg=_T('获取成功'), data=data)

    def tree(self, request, *args, **kwargs):
        """获取树形结构的分类"""
        queryset = self.get_queryset()
        roots = queryset.filter(parent=None)
        serializer = self.get_serializer(roots, many=True)
        return make_response(code=0, msg=_T('获取成功'), data=serializer.data)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建分类"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        obj = self.perform_create(serializer)
        serializer = self.get_serializer(obj)
        return make_response(code=0, msg=_T('创建成功'), data=serializer.data)

    def perform_create(self, serializer):
        catalog_type = self.get_catalog_type(self.request)
        parent: CatalogTypeTree = serializer.validated_data.get('parent', None)
        if parent:
            if parent.for_type != catalog_type:
                raise WmsException(_T("父节点类型不匹配"))
            if parent.parent:
                # 所有分类类型都限制为两层
                if catalog_type == CatalogTypeTree.ForTypeChoices.ITEM:
                    raise WmsException(_T("物品分类仅支持2层树节点，不能创建第三层"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.SUPPLIER:
                    raise WmsException(_T("供应商分类仅支持2层树节点，不能创建第三层"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.CUSTOMER:
                    raise WmsException(_T("客户分类仅支持2层树节点，不能创建第三层"))
        serializer.validated_data.pop('for_type')
        obj = CatalogTypeTree.objects.create(for_type=catalog_type, **serializer.validated_data)
        return obj;

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, *args, **kwargs):
        """更新分类"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=kwargs.get('partial', False))
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return make_response(code=0, msg=_T('更新成功'), data=serializer.data)

    def perform_update(self, serializer):
        catalog_type = self.get_catalog_type(self.request)
        instance: CatalogTypeTree = serializer.instance
        if instance.for_type != catalog_type:
            raise WmsException(_T("类型不匹配"))
        logger.debug(f"instance: {instance} (id: {instance.id}) parent: {instance.parent}")

        # 获取新的父节点（如果有的话）
        new_parent = serializer.validated_data.get('parent', instance.parent)

        if new_parent:
            if new_parent.id == instance.id:
                raise WmsException(_T("不能设置自己为上层目录"))
            if new_parent.for_type != catalog_type:
                raise WmsException(_T("上层目录类型不匹配"))
            if new_parent.parent:
                # 所有分类类型都限制为两层
                if catalog_type == CatalogTypeTree.ForTypeChoices.ITEM:
                    raise WmsException(_T("物品分类仅支持2层树节点，不能设置第三层父节点"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.SUPPLIER:
                    raise WmsException(_T("供应商分类仅支持2层树节点，不能设置第三层父节点"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.CUSTOMER:
                    raise WmsException(_T("客户分类仅支持2层树节点，不能设置第三层父节点"))

        # 检查是否会导致子节点超过两层
        if instance.parent != new_parent:
            # 如果当前节点有子节点，且新的父节点不为空，则会形成三层结构
            if new_parent and CatalogTypeTree.objects.filter(parent=instance, for_type=catalog_type).exists():
                if catalog_type == CatalogTypeTree.ForTypeChoices.ITEM:
                    raise WmsException(_T("物品分类仅支持2层，超过层级限制"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.SUPPLIER:
                    raise WmsException(_T("供应商分类仅支持2层，超过层级限制"))
                elif catalog_type == CatalogTypeTree.ForTypeChoices.CUSTOMER:
                    raise WmsException(_T("客户分类仅支持2层，超过层级限制"))

        serializer.save()

    def partial_update(self, request, *args, **kwargs):
        """部分更新分类"""
        return self.update(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        """获取单个分类详情"""
        catalog_type = self.get_catalog_type(request)
        instance = self.get_object()
        
        # 验证实例是否属于指定类型
        if instance.for_type != catalog_type:
            raise WmsException(_T("分类类型不匹配"))
        
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_T('获取成功'), data=serializer.data)

    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        """删除分类"""
        catalog_type = self.get_catalog_type(request)
        instance = self.get_object()
        
        # 验证实例是否属于指定类型
        if instance.for_type != catalog_type:
            return make_response(code=1, msg=_T("分类类型不匹配"))
        
        # 检查是否有子节点
        if CatalogTypeTree.objects.filter(parent=instance, for_type=catalog_type).exists():
            return make_response(code=1, msg=_T("存在子节点，无法删除"))

        # 检查是否为最后一个根节点
        if CatalogTypeTree.objects.filter(for_type=catalog_type).count() == 1:
            return make_response(code=1, msg=_T("根类型不能删除"))
        
        # 检查是否有物品引用此分类
        if hasattr(instance, 'item_set') and instance.item_set.exists():
            return make_response(code=1, msg=_T("该分类已被物品引用，无法删除"))
            
        # 检查是否有供应商引用此分类
        if hasattr(instance, 'supplier_set') and instance.supplier_set.exists():
            return make_response(code=1, msg=_T("该分类已被供应商引用，无法删除"))
            
        # 检查是否有客户引用此分类
        if hasattr(instance, 'customer_set') and instance.customer_set.exists():
            return make_response(code=1, msg=_T("该分类已被客户引用，无法删除"))
        try:
            instance.delete()
        except ProtectedError:
            return make_response(code=1, msg=_T("该分类已被引用，无法删除"))
        return make_response(code=0, msg=_T('删除成功'), data={})
