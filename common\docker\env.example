
PROJ_NAME=wms
DOCKER_VOLUME_DIRECTORY=/data/server/wms

POSTGRES_ROOTPASSWORD=CMOwXzzLiCjIsn5gTLvSab
POSTGRES_DEFAULTDB=postgress
POSTGRES_USER=pger
POSTGRES_USER_PWD=pger_pass



# ------------------------------
# Environment Variables for Nginx reverse proxy
# ------------------------------
# step 1  
# NGINX_HTTPS_ENABLED=false 
# docker compose --profile certbot up --force-recreate -d
# docker compose exec -it certbot /bin/sh /update-cert.sh
# step 2 
#    NGINX_HTTPS_ENABLED=true
# docker compose --profile certbot up -d --no-deps --force-recreate nginx
#
# step 3   recreate ssl
# docker compose exec -it certbot /bin/sh /update-cert.sh
# docker compose exec nginx nginx -s reload

NGINX_SERVER_NAME=_
NGINX_HTTPS_ENABLED=true
# HTTP port
NGINX_PORT=80
# SSL settings are only applied when HTTPS_ENABLED is true
NGINX_SSL_PORT=443
# if HTTPS_ENABLED is true, you're required to add your own SSL certificates/keys to the `./nginx/ssl` directory
# and modify the env vars below accordingly.
NGINX_SSL_CERT_FILENAME=fullchain.pem
NGINX_SSL_CERT_KEY_FILENAME=privkey.pem
NGINX_SSL_PROTOCOLS=TLSv1.1 TLSv1.2 TLSv1.3

# Nginx performance tuning
NGINX_WORKER_PROCESSES=auto
NGINX_CLIENT_MAX_BODY_SIZE=15M
NGINX_KEEPALIVE_TIMEOUT=65

# Proxy settings
NGINX_PROXY_READ_TIMEOUT=3600s
NGINX_PROXY_SEND_TIMEOUT=3600s

# Set true to accept requests for /.well-known/acme-challenge/
NGINX_ENABLE_CERTBOT_CHALLENGE=false

# ------------------------------
# Certbot Configuration
# ------------------------------

# Email address (required to get certificates from Let's Encrypt)
CERTBOT_EMAIL=<EMAIL>

# Domain name
CERTBOT_DOMAIN=devcxz.flysoai.com

# certbot command options
# i.e: --force-renewal --dry-run --test-cert --debug
CERTBOT_OPTIONS=