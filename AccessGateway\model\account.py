from django.db import models
from django.contrib.auth.hashers import make_password, check_password

# # 暂时不用
# class SystemAccount(models.Model):
#     """系统账号表"""
#     STATUS_CHOICES = (
#         (0, '禁用'),
#         (1, '启用'),
#     )
#     platform_account = models.ForeignKey(
#         'PlatformAccount',
#         on_delete=models.CASCADE,
#         # 修改 related_name
#         related_name='access_gateway_system_accounts'
#     )
#     account_no = models.CharField(max_length=32, unique=True, verbose_name='账号编号',auto_created=True)
#     nickname = models.CharField(max_length=50, null=True, blank=True, verbose_name='昵称')
#     avatar = models.CharField(max_length=255, null=True, blank=True, verbose_name='头像URL')
#     tenant_id = models.ForeignKey(related_name='system_accounts', null=True, to='TenantLedger', on_delete=models.CASCADE, verbose_name='所属租户')
#     password = models.Char<PERSON>ield(max_length=128, null=True, verbose_name='密码')
#     status = models.BooleanField(default=1, choices=STATUS_CHOICES, verbose_name='状态')
#     last_login_at = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
#     created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
#     updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
#     def set_password(self, raw_password):
#         """设置密码"""
#         self.password = make_password(raw_password)
        
#     def check_password(self, raw_password):
#         """检查密码"""
#         return check_password(raw_password, self.password)

#     class Meta:
#         db_table = 'system_accounts'
#         verbose_name = '系统账号'
#         verbose_name_plural = verbose_name
#         unique_together = [('platform_account', 'account_no')]
        
#     def __str__(self):
#         return f"{self.account_no}"



