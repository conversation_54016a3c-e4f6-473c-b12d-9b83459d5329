from decimal import Decimal
from typing import Tuple

from django.db import models
from django.utils.translation import gettext as _
from rest_framework import serializers

from WmsCore.admin.warehouse import WarehouseAdmin
from WmsCore.models import Stock, Unit, SalesOutItem, Item, PurchaseOrderItem, SalesOut, Warehouse, SalesOrder, \
    SalesOrderItem, Receipt, Customer, PaymentMethod, ReceiptRecord
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.exception import WmsException
from common.make_response import make_response
from common.logger import logger as log
from common.translation import _T
from WmsCore.admin.stock_history import StockHistoryAdmin
from WmsCore.models import StockHistory


class SalesOrderAdmin(object):

    @staticmethod
    def count_stock_quantity(item: Item, unit, warehouse=None) -> tuple[Decimal, Decimal] | None:
        """
        获取所有仓库某物品某单位的库存数量
        必须是以主单位进行换算，但返回的数量是以传入单位换算得来
        :param warehouse: 指定的仓库
        :param item: Item实例
        :param unit: Unit实例 也是返回的数量的单位
        :return: 指定单位库存数量和主单位库存数量
        """
        main_unit = item.unit
        total_available_quantity = Decimal('0')  # 这里的数量是参数单位下的数量，就是订单物品单位的数量
        main_qty = Decimal('0')  # 主单位下的数量
        if warehouse is None:
            stocks = Stock.objects.filter(item=item, remaining_quantity__gt=0)
        else:
            stocks = Stock.objects.filter(item=item, warehouse=warehouse, remaining_quantity__gt=0)
        for stock in stocks:
            if stock.unit == unit:  # 如果订单单位和库存单位一样
                total_available_quantity += stock.remaining_quantity or 0
                main_qty += stock.remaining_quantity or 0
            else:
                try:
                    stock_main_qty = (stock.remaining_quantity or 0) * stock.unit.conversion_rate
                    # 订单单位->主单位
                    # 这里就是除法，是主单位转换为指定单位
                    total_available_quantity += stock_main_qty / unit.conversion_rate
                    main_qty += stock_main_qty
                except Unit.DoesNotExist:
                    raise f"物品 '{item.name}' 的计量单位无法与主单位互相换算"
        return total_available_quantity, main_qty

    @staticmethod
    def check_stock_sufficient(item: Item, warehouse, quantity, unit):
        """
        校验某仓库某物品某单位的库存是否充足，支持单位换算。
        :param item: Item实例
        :param warehouse: Warehouse实例
        :param quantity: 需求数量（Decimal） 是参数单位的数量，也是订单的物品的数量而不是物品主单位的
        :param unit: 需求单位（Unit实例）
        :return: (bool, str) 是否充足，错误信息（充足时为空字符串）
        """
        # 要核对 unit 信息是这个 item 的单位吗？
        total_available_quantity, main_qty = SalesOrderAdmin.count_stock_quantity(item=item,
                                                                                  unit=unit, warehouse=warehouse)
        # 有个 bug， 一个订单是可能存在同个物品不同计量的销售的，你这只处理一种一个物品的一个计量单位处理而已
        if total_available_quantity < quantity:
            # 多语言支持，这里要处理_()
            # 要支持负库存逻辑
            return False, _T(
                "物品 '{item}' 在仓库 '{warehouse}' 中库存不足。订单需求：{quantity} {unit}，实际需求: {real_quantity} {main_unit}，可用: {available} {unit},实际库存：{main_qty} {main_unit}",
                item=item.name,
                warehouse=warehouse.name,
                quantity=quantity,
                unit=unit.unit_type.name,
                real_quantity=quantity * unit.conversion_rate,
                main_unit=item.unit.unit_type.name,
                available=total_available_quantity,
                main_qty=main_qty
            )
        return True, '校验成功'

    @staticmethod
    def create_sales_out_items(sales_out: SalesOut, order_item: Item, warehouse: Warehouse, sale_price,quantity):
        """
        按先进先出分配库存，扣减库存并创建出库物品表，支持单位换算。
        会将所有数量不同单位都统一转换为主单位的数量进行操作
        :param quantity:
        :param sale_price: 销售价格
        :param sales_out: SalesOut实例
        :param order_item: SalesOrderItem实例
        :param warehouse: Warehouse实例
        :return: list[SalesOutItem]
        """

        item_instance = order_item
        order_item_mian_unit_qty =order_item.unit.conversion_rate * quantity  # 主单位下的订单物品的数量
        main_unit = item_instance.unit   # 物品的主计量单位（如"瓶"），所有换算的基准单位
        sales_out_items = []
        # 按先进先出分配库存
        stocks = Stock.objects.filter(
            item=item_instance,
            warehouse=warehouse,
            remaining_quantity__gt=0
        ).order_by('in_date', 'id')
        remaining_quantity = order_item_mian_unit_qty  # 剩余待出库的数量，单位为主单位
        while remaining_quantity > 0:
            stock = stocks.first()
            if not stock:
                raise serializers.ValidationError(
                    _("物品 '%(item)s' 在仓库 '%(warehouse)s' 中库存不足（出库时）") % {
                        "item": item_instance.name,
                        "warehouse": warehouse.name
                    }
                )
            try:
                # 库存单位->主单位
                if stock.unit == main_unit:
                    available_stock_quantity = stock.remaining_quantity or 0  # 库存批次数量，单位为主单位
                else:
                    available_stock_quantity = (stock.remaining_quantity or 0) * stock.unit.conversion_rate  # 换算为主单位数量，单位为主单位
            except Unit.DoesNotExist:
                raise serializers.ValidationError(
                    _("物品 '%(item)s' 的计量单位无法与主单位互相换算") % {
                        "item": item_instance.name
                    }
                )
            deduct_order_quantity = min(order_item_mian_unit_qty, available_stock_quantity)  # 实际本次出库数量，单位为主单位
            # 【补充说明】deduct_stock_quantity为实际要扣减的库存批次数量，单位为库存批次的单位
            deduct_stock_quantity = deduct_order_quantity / stock.unit.conversion_rate  # 单位为库存批次单位,主单位换其他单位是除，其他单位换主单位是乘
            # 扣减库存
            # 你这里扣的谁的计量单位的 ？
            # 【补充说明】这里实际扣减的是库存批次的计量单位
            stock.remaining_quantity = (stock.remaining_quantity or 0) - deduct_stock_quantity
            stock.save(update_fields=['remaining_quantity'])
            
            # 记录销售出库历史
            StockHistoryAdmin.DecStockHistory(
                stock=stock,
                history_type=StockHistory.HistoryTypeChoices.SALES_OUT,
                history_order_id=sales_out.id,
                demand_item_keyid=item_instance.id,
                track_id=stock.id,
                quantity=deduct_stock_quantity
            )
            
            # 创建销售物品记录
            out_item = SalesOutItem.objects.create(
                sales_out=sales_out,
                item=item_instance,
                quantity=deduct_order_quantity,
                out_cost=0,  # 零售的没有出库成本？
                final_cost=0,
                unit=item_instance.unit,
                sale_price=sale_price
            )
            sales_out_items.append(out_item)
            remaining_quantity -= deduct_order_quantity
            stocks = stocks.exclude(id=stock.id)  # 下次循环不再用已用完的批次
            # 这里不应该是这个物品全部处理完了再更新总信息吗？
        WarehouseAdmin.UpdateItemQuantity(item_instance, warehouse)
        return sales_out_items

    @staticmethod
    def check_promise_quantity(item: Item, quantity, unit: Unit, expected_delivery_date=None) -> Tuple[bool, str]:
        """
        校验物品的可承诺量是否可以创建销售订单
        :param item:
        :param quantity:
        :param unit:
        :param expected_delivery_date: 预计交货日期（只统计该日期前的库存和在途）
        :return: (bool, str) 是否充足，详细的提示信息
        """
        main_unit = item.unit
        if not main_unit:
            return False, _("物品 '%(item)s' 没有设置主计量单位，无法校验可承诺量。") % {"item": item.name}
        elif main_unit.conversion_rate != 1:
            return False, _("物品 '%(item)s' 的主计量单位 '%(unit)s' 的兑换率必须为1。") % {"item": item.name,
                                                                                           "unit": unit}

        # 1. 仓库数量
        warehouse_quantity,warehouse_main_unit_qty =SalesOrderAdmin.count_stock_quantity(item=item, warehouse=None, unit=unit)

        # 2. 在途数量
        on_way_main_qty=SalesOrderAdmin.calculate_on_way_quantity(item=item,expected_delivery_date=expected_delivery_date)

        # 3. 可承诺量
        atp = warehouse_quantity*unit.conversion_rate + on_way_main_qty

        if atp < quantity:
            return (
                False,
                _("物品 '%(item)s' 可承诺量不足。"
                  
                  "订单需求："
                  "需求: %(order_need)s %(order_unit)s，"
                  "仓库数量: %(order_stock)s %(order_unit)s，"
                  "在途数量: %(order_onway)s %(order_unit)s，"
                  "可承诺量: %(order_atp)s %(order_unit)s。"
                  
                  "实际需求(主单位下)："
                  "需求: %(need)s %(unit)s，"
                  "仓库数量: %(stock)s %(unit)s，"
                  "在途数量: %(onway)s %(unit)s，"
                  "可承诺量: %(atp)s %(unit)s。"
                  
                  "请检查库存、在途采购或调整下单数量。") % {
                    "item": item.name,
                    "order_need": quantity,
                    "order_stock": warehouse_quantity,
                    "order_onway":on_way_main_qty/unit.conversion_rate ,
                    "order_atp": atp/unit.conversion_rate,
                    "order_unit": unit.unit_type.name,

                    "need": quantity*unit.conversion_rate,
                    "stock": warehouse_quantity*unit.conversion_rate,
                    "onway": on_way_main_qty,
                    "atp": atp,
                    "unit": item.unit.unit_type.name,

                }
            )
        else:
            return (
                True,
                _("物品 '%(item)s' 可承诺量充足。"

                 "订单需求："
                 "需求: %(order_need)s %(order_unit)s，"
                 "仓库数量: %(order_stock)s %(order_unit)s，"
                 "在途数量: %(order_onway)s %(order_unit)s，"
                 "可承诺量: %(order_atp)s %(order_unit)s。"

                 "实际需求："
                 "需求: %(need)s %(unit)s，"
                 "仓库数量: %(stock)s %(unit)s，"
                 "在途数量: %(onway)s %(unit)s，"
                 "可承诺量: %(atp)s %(unit)s。"
                  ) % {
                    "item": item.name,
                    "order_need": quantity,
                    "order_stock": warehouse_quantity / unit.conversion_rate,
                    "order_onway": on_way_main_qty / unit.conversion_rate,
                    "order_atp": atp / unit.conversion_rate,
                    "order_unit": unit.unit_type.name,

                    "need": quantity * unit.conversion_rate,
                    "stock": warehouse_quantity,
                    "onway": on_way_main_qty,
                    "atp": atp,
                    "unit": item.unit.unit_type.name,

                }
            )

    @staticmethod
    def calculate_on_way_quantity(item: Item, expected_delivery_date=None):
        """
        计算某物品(在主单位下）的在途数量（采购数量-已交付数量）
        :param expected_delivery_date: 截止时间
        :param item: Item实例
        :return: Decimal 在途数量
        """
        po_items = PurchaseOrderItem.objects.filter(
            item=item,
            delivered_quantity__lt=models.F('quantity')
        ).select_related('unit', 'purchase_order')
        if expected_delivery_date:
            po_items = po_items.filter(purchase_order__expected_arrival_date__lte=expected_delivery_date)

        on_way_main_qty = Decimal('0')
        for po_item in po_items:
            remaining_qty = (po_item.quantity - po_item.delivered_quantity) or Decimal('0')
            if remaining_qty <= 0:
                continue
            on_way_main_qty += po_item.unit.conversion_rate * remaining_qty
        return on_way_main_qty
