from django.utils.deprecation import MiddlewareMixin
from common.logger import thread_local;

class RequestUserMiddleware(MiddlewareMixin):
    """中间件：将当前请求存储到线程局部变量"""
    
    def process_request(self, request):
        # 将当前请求存储到线程局部变量
        thread_local.request = request
        return None
    
    def process_response(self, request, response):
        # 请求结束后清理线程局部变量
        if hasattr(thread_local, 'request'):
            del thread_local.request
        return response
    
    def process_exception(self, request, exception):
        # 异常发生时清理线程局部变量
        if hasattr(thread_local, 'request'):
            del thread_local.request