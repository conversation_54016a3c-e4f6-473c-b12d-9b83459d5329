from datetime import timedelta
from decimal import Decimal, InvalidOperation, ConversionSyntax
import json
from typing import List, Dict, Tuple

from django.db import models, transaction
from django.forms.models import model_to_dict
from rest_framework import status
from rest_framework.decorators import action

from AccessGateway.control.auth import make_response
from WmsCore.admin.AllocatedCost import AllocatedCostAdmin
from WmsCore.admin.stock_history import StockHistoryAdmin
from WmsCore.admin.warehouse import WarehouseAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import Payment, PaymentMethod, PurchaseIn, PurchaseInItem, PurchaseOrder, PurchaseOrderItem, Stock, \
    CostAllocation, StockHistory, ZTUser, Supplier
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.logger import logger
from rest_framework import serializers
from django.db.models import Prefetch, Sum
from django.utils.translation import gettext_lazy as _
from common.logger import logger as log
from common.exception import WmsException
from common.make_response import make_response
from WmsCore.control.PurchaseOrder import PaymentSerializer
from WmsCore.control.Image import MultiImagesField
from common.translation import _T
from WmsCore.utils.tools import CommonTools
class PurchaseInItemSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True, read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    code = serializers.CharField(source='item.code', read_only=True)
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True)
    production_date = serializers.DateField(required=False, allow_null=True)
    expiry_days = serializers.IntegerField(required=False, allow_null=True)
    order_quantity = serializers.DecimalField(max_digits=30, decimal_places=8, source='purchase_order_item.quantity', read_only=True)
    order_delivered_quantity = serializers.DecimalField(max_digits=30, decimal_places=8, source='purchase_order_item.delivered_quantity', read_only=True )
    images = MultiImagesField(required=False)

    class Meta:
        model = PurchaseInItem
        fields = [
            'id', 'item', 'item_name', 'code', 'purchase_order_item', 'order_quantity', 'order_delivered_quantity',
            'unit', 'unit_name', 'quantity', 'purchase_price', 'production_date', 'expiry_days',
            'images'
        ]

@SerializerCollector(alias="采购入库列表")
class PurchaseInListSerializer(serializers.ModelSerializer):
    """用于列表查询的序列化器，只返回指定字段"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)

    class Meta:
        model = PurchaseIn
        fields = [
            'id', 'short_desc', 'handler', 'items_count', 'actual_total_cost',
            'deposit_deduction', 'unpaid_amount', 'pay_amount', 'order_id',
            'in_type', 'in_date', 'in_status', 'supplier_name'
        ]


@SerializerCollector(alias="采购入库库存列表")
class PurchaseInStockSerializer(serializers.ModelSerializer):
    item_name = serializers.CharField(source='item.name', read_only=True)
    total_cost = serializers.DecimalField(max_digits=30, decimal_places=8, source='actual_cost + allocated_cost', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True)
    class Meta:
        model = Stock
        fields = ['id', 'in_date', 'item', 'item_name', 'batch_number', 'unit', 'unit_name', 'quantity', 'remaining_quantity', 'total_cost', 'warehouse', 'warehouse_name']

class AllocatedCostSerializer(serializers.ModelSerializer ):
    # 用于写入的字段
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    
    class Meta:
        model = CostAllocation
        fields = ['id', 'source_type', 'order_id', 'total_cost', 'allocation_method', 'allocation_details', 'supplier', 'supplier_name',  ]
        read_only_fields = ['order_id', 'total_cost' , 'source_type', 'source_order_id', ]

@SerializerCollector(alias="采购入库")
class PurchaseInSerializer(serializers.ModelSerializer):
    purchase_order = serializers.PrimaryKeyRelatedField(queryset=PurchaseOrder.objects.all(), required=False, allow_null=True)
    purchase_order_name = serializers.CharField(source='purchase_order.order_id', read_only=True)
    batch_number = serializers.CharField(required=False, allow_null=True)
    order_id = serializers.CharField(required=False, allow_null=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    in_date = serializers.DateField(required=False, allow_null=True)
    items = PurchaseInItemSerializer(many=True)
    allocated_cost = AllocatedCostSerializer(required=False, allow_null=True )
    is_commit = serializers.IntegerField(required=False, allow_null=True, write_only=True)
    supplier=serializers.PrimaryKeyRelatedField(queryset=Supplier.objects.all(),required=True)
    # 用于写入的字段
    payment = PaymentSerializer(required=False, write_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    payment_account_name = serializers.CharField(source='payment_method.account_name', read_only=True)
    allocated_account_name = serializers.CharField(source='allocated_payment_method.account_name', read_only=True)
    discount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True)
    class Meta:
        model = PurchaseIn
        fields = [
            'id', 'in_type', 'purchase_order', 'purchase_order_name', 'supplier', 'supplier_name', 'order_id', 'batch_number', 'short_desc', 'items', 'handler', 'is_commit',
            'in_date', 'warehouse', 'warehouse_name', 'total_cost', 'allocated_cost', 'payment',
            'payment_account_name', 'allocated_account_name', 'allocated_pay_amount', 'allocated_payment_method', 'allocated_pay_amount', 'in_status', 'payment_method', 'pay_amount', 'discount'
        ]
        read_only_fields = ['handler', 'short_desc', 'total_cost', 'payment_account_name' ,'allocated_account_name' ]

class PurchaseInViewSet(SafeModelViewSet):
    """
    采购入库的增删改查接口
    """
    queryset = PurchaseIn.objects.prefetch_related(
        Prefetch('items', queryset=PurchaseInItem.objects.select_related('item'))
    ).all()
    serializer_class = PurchaseInSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """获取查询集，过滤掉已取消的订单"""
        return super().get_queryset().exclude(in_status=PurchaseIn.InStatusChoices.CANCELLED)

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'search':
            return PurchaseInListSerializer
        if self.action == 'StockList':
            return PurchaseInStockSerializer
        return PurchaseInSerializer

    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        if self.action in ['create', 'update', 'partial_update']:
            # 对于写操作，不需要预取关系
            return super().get_serializer(*args, **kwargs)

        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()

        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            instance = self.get_queryset().get(id=instance.id)
            kwargs['instance'] = instance

        return super().get_serializer(*args, **kwargs)

    @staticmethod
    def validate_purchase_order_items(purchase_order: PurchaseOrder, current_purchase_in: PurchaseIn = None) -> Tuple[bool, str, float]:
        """
        验证采购订单下所有进货单的物品是否满足采购订单中的需求
        一旦发现有物品未满足需求，立即返回失败结果
        
        Args:
            purchase_order: 采购订单实例
            current_purchase_in: 当前正在处理的进货单实例，如果提供则在统计时排除该进货单的数据
            
        Returns:
            Tuple[bool, str, float]: 验证结果、错误消息和完成百分比，如果验证通过则返回 (True, "", 100.0)
        """
        if not purchase_order:
            return True, "", 100.0
        
        # 获取采购订单中的所有物品及其需求数量
        order_items = PurchaseOrderItem.objects.filter(purchase_order=purchase_order).select_related('item', 'unit')
        
        # 构建采购订单物品映射 {(item_id, unit_id): (quantity, delivered_quantity)}
        order_items_map = {}
        total_required_quantity = 0  # 总需求数量
        
        for order_item in order_items:
            key = (order_item.item_id, order_item.unit_id)
            # 如果已经存在相同的物品和单位，则累加数量
            if key in order_items_map:
                existing_quantity, existing_delivered = order_items_map[key]
                order_items_map[key] = (existing_quantity + order_item.quantity, 
                                        existing_delivered + order_item.delivered_quantity)
            else:
                order_items_map[key] = (order_item.quantity, order_item.delivered_quantity)
            total_required_quantity += order_item.quantity
        
        # 获取该采购订单下所有已完成的进货单（排除当前正在处理的进货单和非完成状态的进货单）
        completed_purchase_ins = PurchaseIn.objects.filter(
            purchase_order=purchase_order, 
            in_status=PurchaseIn.InStatusChoices.COMPLETED  # 只统计已完成状态的进货单
        )
        
        if current_purchase_in and current_purchase_in.id:
            completed_purchase_ins = completed_purchase_ins.exclude(id=current_purchase_in.id)
        
        log.info(f"采购订单 {purchase_order.order_id} 下已完成的进货单数量: {completed_purchase_ins.count()}")
        
        # 统计所有已完成进货单中的物品数量，按物品ID和单位ID分组
        delivered_items = {}  # {(item_id, unit_id): delivered_quantity}
        
        for purchase_in in completed_purchase_ins:
            in_items = PurchaseInItem.objects.filter(purchase_in=purchase_in).select_related('item', 'unit')
            for in_item in in_items:
                key = (in_item.item_id, in_item.unit_id)
                if key not in delivered_items:
                    delivered_items[key] = 0
                delivered_items[key] += in_item.quantity
                log.debug(f"进货单 {purchase_in.order_id} 物品 {in_item.item.name} 单位 {in_item.unit.unit_type.name} 数量 {in_item.quantity}")
        
        # 验证每个物品的交付数量是否满足需求
        total_delivered_quantity = 0  # 总交付数量
        
        # 首先计算总交付数量，用于计算完成百分比
        for (item_id, unit_id), (quantity, delivered_quantity) in order_items_map.items():
            item_delivered_quantity = delivered_quantity  # 采购订单中已记录的交付数量
            
            # 加上其他进货单中的交付数量
            key = (item_id, unit_id)
            if key in delivered_items:
                item_delivered_quantity += delivered_items[key]
            
            total_delivered_quantity += item_delivered_quantity
        
        # 计算完成百分比
        completion_percentage = 100.0
        if total_required_quantity > 0:
            completion_percentage = (total_delivered_quantity / total_required_quantity) * 100
        
        # 现在检查每个物品，一旦发现不满足需求的物品就退出
        for (item_id, unit_id), (quantity, delivered_quantity) in order_items_map.items():
            item_delivered_quantity = delivered_quantity  # 采购订单中已记录的交付数量
            
            # 加上其他进货单中的交付数量
            key = (item_id, unit_id)
            if key in delivered_items:
                item_delivered_quantity += delivered_items[key]
            
            # 获取物品信息用于日志记录
            # 使用filter替代get，因为可能有多个相同物品和单位的订单项
            order_items_for_log = PurchaseOrderItem.objects.filter(
                purchase_order=purchase_order, 
                item_id=item_id, 
                unit_id=unit_id
            ).select_related('item', 'unit').first()  # 取第一个用于日志记录
            
            if not order_items_for_log:
                log.warning(f"采购订单 {purchase_order.order_id} 中未找到物品ID {item_id} 单位ID {unit_id} 的订单项")
                continue
            
            # 检查是否满足需求
            if item_delivered_quantity < quantity:
                error_msg = f"物品 '{order_items_for_log.item.name}' (单位: {order_items_for_log.unit.unit_type.name}) 的交付数量不足，需求 {quantity}，已交付 {item_delivered_quantity}"
                log.warning(f"采购订单 {purchase_order.order_id} 未完成，原因: {error_msg}，完成度: {completion_percentage:.2f}%")
                return False, error_msg, completion_percentage
            elif item_delivered_quantity > quantity:
                # 记录超出部分的情况
                exceed_percentage = ((item_delivered_quantity - quantity) / quantity) * 100
                log.info(f"物品 '{order_items_for_log.item.name}' (单位: {order_items_for_log.unit.unit_type.name}) 的交付数量超出订单，订单需求 {quantity}，实际交付 {item_delivered_quantity}，超出 {exceed_percentage:.2f}%")
            else:
                log.info(f"物品 '{order_items_for_log.item.name}' (单位: {order_items_for_log.unit.unit_type.name}) 的交付数量正好满足订单，需求 {quantity}，实际交付 {item_delivered_quantity}")
        
        # 如果所有物品都满足需求，则返回成功
        log.info(f"采购订单 {purchase_order.order_id} 已完成，完成度: {completion_percentage:.2f}%")
        return True, "", completion_percentage

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新采购入库"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        purchase_in = self.perform_create_or_update(serializer, request.zt_user, is_update=False)
        return make_response(code=0, msg=_T('创建成功'), data=self.get_serializer(purchase_in).data, status=status.HTTP_200_OK)

    def perform_create_or_update(self, serializer, handler, is_update=False):
        is_commit = serializer.validated_data.pop('is_commit', 0)
        items_data = serializer.validated_data.pop('items', [])
        supplier=serializer.validated_data.get('supplier', None)
        purchase_order = serializer.validated_data.get('purchase_order', None)
        if supplier :
            if purchase_order and purchase_order.supplier != supplier:
                raise WmsException(_T('与采购订单供应商不一致'))
        if len(items_data) == 0:
            raise WmsException(_("采购入库必须包含至少一个物品"))
        allocated_cost_data = serializer.validated_data.pop('allocated_cost', None)
        log.debug(f"serializer.validated_data: {serializer.validated_data}")

        if is_update:
            # 更新操作
            purchase_in = serializer.instance
            # 检查入库单状态
            if purchase_in.in_status != PurchaseIn.InStatusChoices.DRAFT and purchase_in.in_status != PurchaseIn.InStatusChoices.CANCELLED:
                raise WmsException(_("只能修改暂存状态的入库单"))
            
            # 更新基本信息
            for field, value in serializer.validated_data.items():
                setattr(purchase_in, field, value)
            purchase_in.save()
        else:
            # 创建操作
            purchase_in = PurchaseIn.objects.create(**serializer.validated_data, handler=handler, total_cost=0, actual_total_cost=0, items_count=0)

        #purchase_order = purchase_in.purchase_order
        purchase_order_items = []
        #purchase_order_items_map: dict[int, PurchaseOrderItem] = {}
        # purchase_order: PurchaseOrder = None;

        total_amount = 0
        items_count = 0
        in_items: list[PurchaseInItem] = []
        
        for item_data in items_data:
            pi_item_id = item_data.get('id', 0)
            if pi_item_id > 0:
                item = PurchaseInItem.objects.get(id=pi_item_id)
                for field, value in item_data.items():
                    setattr(item, field, value)
                item.save()
            else:
                item = PurchaseInItem.objects.create(**item_data, purchase_in=purchase_in)
            if item.item != item.unit.item:
                raise serializers.ValidationError(_("此物品没有该计量单位") + " " + item.item.name + " " + item.unit.unit_type.name)
            #允许额外增加采购单以外的物品
            # if purchase_order and item.item.id not in purchase_order_items_map:
            #     raise serializers.ValidationError(_("采购订单中没有此物品" + " " + item.item.name))
            if item.purchase_order_item:
                order_item = item.purchase_order_item;
                # if order_item.unit.id != item.unit.id:
                #     raise serializers.ValidationError(_("采购订单中此物品的计量单位与入库单中不一致" + " " + item.item.name + " " + order_item.unit.unit_type.name + " " + item.unit.unit_type.name))
                # 严格限制按照采购单进货的话，在这里限制
                # if order_item.delivered_quantity + item.quantity > order_item.quantity:
                #     raise serializers.ValidationError(_("采购订单中此物品的数量不足" + " " + item.item.name + " " + str(order_item.quantity) + " " + str(order_item.delivered_quantity) + " " + str(item.quantity)))
                if is_commit == 1:
                    #commit 的时候，更新采购单的已交货数量
                    if order_item.unit.id != item.unit.id:
                        order_item.delivered_quantity += item.quantity * item.unit.conversion_rate / order_item.unit.conversion_rate
                    else:
                        order_item.delivered_quantity += item.quantity
                    order_item.save()

            if purchase_order and is_commit == 1:
                order_status = PurchaseOrder.OrderStatusChoices.COMPLETED
                for order_item in purchase_order_items:
                    if order_item.delivered_quantity < order_item.quantity:
                        order_status = PurchaseOrder.OrderStatusChoices.PARTIAL
                        break
                if purchase_order.order_status != order_status:
                    purchase_order.order_status = order_status
                    purchase_order.save()

            if item.item.expiry_days > 0:
                if item.production_date is None:
                    raise serializers.ValidationError(_("存在有效期管理的商品生产日期不能为空") + " " + item.item.name)
                if item.expiry_days == 0:
                    item.expiry_days = item.item.expiry_days
                #入库日期已经过期了？
                if item.production_date + timedelta(days=item.expiry_days) < purchase_in.in_date:
                    raise serializers.ValidationError(_("生产日期+有效期小于入库日期") + " " + item.item.name + " " + str(item.production_date) + " " + str(item.expiry_days) + " " + str(purchase_in.in_date))

            total_amount += item.quantity * item.purchase_price
            #items_count += item.quantity
            in_items.append(item)
        items_count = len(in_items)
        
        #更新的话，需要删除本次PurchaseInItem之外的商品
        if is_update:
            PurchaseInItem.objects.filter(purchase_in=purchase_in).exclude(id__in=[item.id for item in in_items]).delete()

        discount_rate = purchase_in.discount / total_amount * 100 if total_amount > 0 else 0
        for item in in_items:
            #item.discount_rate = discount_rate
            item.actual_purchase_price = item.purchase_price * (100 - discount_rate) / 100

        # 在完成入库前更新采购订单状态
        if is_commit == 1:
            is_completed, error_msg, completion_percentage = self.validate_purchase_order_items(purchase_in.purchase_order, purchase_in)
            
            # 更新采购订单状态
            if is_completed:
                purchase_order.order_status = PurchaseOrder.OrderStatusChoices.COMPLETED
            else:
                # 如果有任何交付，则为部分交付
                if completion_percentage > 0:
                    purchase_order.order_status = PurchaseOrder.OrderStatusChoices.PARTIAL
                else:
                    purchase_order.order_status = PurchaseOrder.OrderStatusChoices.PENDING
            
            purchase_order.save()
            log.info(f"采购订单 {purchase_in.purchase_order.order_id} 状态更新为 {purchase_in.purchase_order.order_status}，完成度 {completion_percentage:.2f}%")

        # 使用 CommonTools 生成简要描述
        purchase_in.short_desc = CommonTools.generate_short_desc(in_items)

        purchase_in.total_cost = total_amount
        purchase_in.actual_total_cost = total_amount - purchase_in.discount
        purchase_in.items_count = items_count
        purchase_in.in_status = PurchaseIn.InStatusChoices.COMPLETED if is_commit == 1 else PurchaseIn.InStatusChoices.DRAFT

        if not is_update:
            purchase_in.order_id = Counter.DayCounter("PI")
            purchase_in.batch_number = Counter.DayCounter("SBN")

        # 计算分摊成本
        self.calculate_allocated_cost(purchase_in, in_items, allocated_cost_data, is_commit)
        # 处理入库变成库存物品
        if is_commit == 1:
            self.handle_items_into_warehouse(purchase_in, in_items)
        # 付款操作
        self.handle_payment(purchase_in, is_commit)

        for item in in_items:
            item.save()
        purchase_in.save()
        return purchase_in

    def calculate_allocated_cost(self, purchase_in: PurchaseIn, in_items: List[PurchaseInItem], allocated_cost_data: dict, is_commit: int):
        # 计算均摊费用
        source_type = CostAllocation.SourceTypeChoices.PURCHASE_IN
        if purchase_in.in_type == PurchaseIn.InTypeChoices.PURCHASE_IN:
            source_type = CostAllocation.SourceTypeChoices.PURCHASE_IN;
        allocated_cost_obj: CostAllocation = AllocatedCostAdmin.create_or_update(allocated_cost_data, purchase_in.handler, source_type, purchase_in.order_id)
        if allocated_cost_obj is None:
            return 0;

        
        # total_cost = Decimal(0); 
        # details = json.loads(allocated_cost_data.get('allocation_details', "[]"));
        # log.debug(f"details: {details} type: {type(details)}")
        # for cost in details:
        #     print(f"cost: {cost}")
        #     cost = Decimal( float(cost['cost']) );
        #     if cost <= 0:
        #         raise WmsException(_("分摊费用不能小于0") + " " + str(cost))
        #     total_cost += cost;
        # if total_cost == 0:
        #     #清空
        #     if allocated_id:
        #         allocated_cost_obj = CostAllocation.objects.get(id=allocated_id, source_order_id=purchase_in.order_id, source_type=source_type).first();
        #         allocated_cost_obj.delete()
        #     return 0;
        # log.debug(f"total_cost: {total_cost}")
        # if allocated_id:
        #     allocated_cost_obj = CostAllocation.objects.get(id=allocated_id, source_order_id=purchase_in.order_id, source_type=source_type).first();
        #     for field, value in allocated_cost_data.items():
        #         setattr(allocated_cost_obj, field, value)
        #     #allocated_cost_obj.save()
        # else:
        #     order_id = Counter.SysDayCounter("CostAllocation" ) #CAL
        #     allocated_pay_method = allocated_cost_data.get('allocation_method', None);
        #     if allocated_pay_method is None:
        #         raise WmsException(_("分摊费用中的付款方式不能为空"))
        #     allocated_cost_obj = CostAllocation.objects.create(**allocated_cost_data, handler=purchase_in.handler, paid_amount=0, source_type=source_type, source_order_id=purchase_in.order_id, order_id=order_id, total_cost=0)
        # allocated_cost_obj.total_cost = total_cost;

        # 计算均摊费用到具体商品上
        total_cost = purchase_in.total_cost;
        unallocated_items: list[PurchaseInItem] = [];
        allocated_method = allocated_cost_obj.allocation_method;
        discount_rate = allocated_cost_obj.discount / total_cost * 100;
        #已分配的分摊费用总和
        allocated_cost = Decimal(0);
        for in_item in in_items:
            if in_item.allocated_cost == 0:
                unallocated_items.append(in_item)
            else:
                allocated_cost += in_item.allocated_cost;
                in_item.actual_allocated_cost = in_item.allocated_cost * (100 - discount_rate) / 100;
        if allocated_cost > total_cost:
            raise WmsException(_("已分配的分摊费用大于总费用") + " " + str(allocated_cost) + " " + str(total_cost))
        unallocated_total_cost = total_cost - allocated_cost;
        total_purchase_values = [];
        if allocated_method == CostAllocation.AllocationMethodChoices.AMOUNT:
            total_purchase_values = [in_item.purchase_price * in_item.quantity for in_item in unallocated_items]
        elif allocated_method == CostAllocation.AllocationMethodChoices.QUANTITY:
            total_purchase_values = [in_item.quantity for in_item in unallocated_items]
        else:
            raise WmsException(_("分配方式不正确" + " " + allocated_method))
        total_purchase_value = sum(total_purchase_values)
        for idx, in_item in enumerate(unallocated_items):
            allocated_value = total_purchase_values[idx]
            in_item.allocated_cost = allocated_value / total_purchase_value * unallocated_total_cost 
            in_item.actual_allocated_cost = in_item.allocated_cost * (100 - discount_rate) / 100 / in_item.quantity;
            # 不确定，purchase_in的 save()有没有顺便保存这些 item
            #in_item.save()

        if is_commit == 1:
            # 这里如果 有均摊的付款，则生成付款单
            if purchase_in.allocated_pay_amount > 0:
                if purchase_in.allocated_pay_amount > allocated_cost_obj.total_cost:
                    raise WmsException(_("支付费用大于分摊费用") + " " + str(purchase_in.allocated_pay_amount) + " " + str(allocated_cost_obj.total_cost))
                if purchase_in.allocated_payment_method is None:
                    raise WmsException(_("付款方式不能为空") + " " + str(purchase_in.allocated_payment_method))
                AllocatedCostAdmin.create_payment(allocated_cost_obj, purchase_in.allocated_pay_amount, purchase_in.allocated_payment_method)
                
            # 有欠款
            AllocatedCostAdmin.create_payable(allocated_cost_obj)

        #allocated_cost_obj.source_type = CostAllocation.SourceTypeChoices.PURCHASE_IN;
        #allocated_cost_obj.source_order_id = purchase_in.order_id;
        purchase_in.allocated_cost = allocated_cost_obj;
        #返回实际均摊的费用总和
        return allocated_cost_obj.total_cost - allocated_cost_obj.discount;

    def handle_items_into_warehouse(self, purchase_in: PurchaseIn, in_items: List[PurchaseInItem]):
        # 处理入库变成库存物品
        for in_item in in_items:
            # TODO
            # 处理正库存增加的，也可能有负库存扣减的，这里还得处理
            # 入库修正单和组装单会可能出现负数
            if in_item.quantity > 0:
                #计算最小计量单位的各种数值
                unit_conversion_rate = in_item.unit.conversion_rate;
                quantity = in_item.quantity * unit_conversion_rate;
                actual_cost = in_item.actual_purchase_price / unit_conversion_rate;
                allocated_cost = in_item.actual_allocated_cost / unit_conversion_rate;
                batch_number = in_item.batch_number; #支持自带批次号
                if batch_number is None:
                    batch_number = purchase_in.batch_number;
                log.debug(f"item: {in_item.item} unit: {in_item.unit} unit:{ in_item.item.unit} warehouse: {purchase_in.warehouse} in_type: {Stock.StockInChoices.PURCHASE_IN} in_order_id: {purchase_in.order_id} batch_number: {purchase_in.batch_number} quantity: {quantity} expiry_days: {in_item.item.expiry_days} actual_cost: {actual_cost} allocated_cost: {allocated_cost} remaining_quantity: {quantity}")
                stock = Stock.objects.create(
                    item=in_item.item,
                    unit=in_item.item.unit,
                    warehouse=purchase_in.warehouse,
                    in_type=Stock.StockInChoices.PURCHASE_IN,
                    in_order_id=purchase_in.order_id,
                    batch_number=batch_number,
                    track_id = in_item.track_id,
                    quantity=quantity,
                    expiry_days=in_item.expiry_days,
                    actual_cost=actual_cost,
                    allocated_cost=allocated_cost,
                    out_cost=actual_cost+allocated_cost, #先算实际成本+分摊成本，后面再继续成本计价方式
                    remaining_quantity=quantity,
                )
                if in_item.track_id == 0:
                    stock.track_id = stock.id;
                    in_item.track_id = stock.track_id;
                    stock.save();
                    in_item.save();
                StockHistoryAdmin.AddStockHistory(stock, StockHistory.HistoryTypeChoices.PURCHASE_IN, purchase_in.order_id, in_item.id, stock.track_id, quantity)
            #处理库存余量
            WarehouseAdmin.UpdateItemQuantity(in_item.item, purchase_in.warehouse)
            # 处理成本计价

    def handle_payment(self, purchase_in: PurchaseIn, is_commit: int ):
        # 处理付款
        purchase_in.deposit_deduction = 0;
        purchase_in.unpaid_amount = purchase_in.actual_total_cost;
        if purchase_in.in_type == PurchaseIn.InTypeChoices.PURCHASE_IN and purchase_in.purchase_order:
            deposit_deduction = purchase_in.purchase_order.undeducted_deposit;
            if deposit_deduction > 0 and is_commit == 1:
                if purchase_in.actual_total_cost < deposit_deduction:
                    deposit_deduction = purchase_in.actual_total_cost;
                    purchase_in.purchase_order.undeducted_deposit = deposit_deduction - purchase_in.actual_total_cost;
                else:
                    purchase_in.purchase_order.undeducted_deposit = 0;
                purchase_in.purchase_order.save()
                purchase_in.deposit_deduction = deposit_deduction;
                #抵扣预付款后，剩余未付金额
                purchase_in.unpaid_amount -= purchase_in.deposit_deduction;

        if purchase_in.pay_amount > 0 and is_commit == 1:
            if purchase_in.pay_amount > purchase_in.unpaid_amount:
                raise WmsException(_("支付金额大于未付款金额") + " " + str(purchase_in.pay_amount) + " " + str(purchase_in.unpaid_amount))
            if purchase_in.payment_method is None:
                raise WmsException(_("付款方式不能为空") + " " + str(purchase_in.payment_method))
            Payment.objects.create(
                order_type=Payment.OrderTypeChoices.PURCHASE_IN,
                order_id=purchase_in.order_id,
                supplier=purchase_in.supplier,
                amount=purchase_in.pay_amount,
                discount = purchase_in.discount,
                deposit_deduction = purchase_in.deposit_deduction,
                handler=purchase_in.handler,
                controller=purchase_in.handler,
                payment_method=purchase_in.payment_method,
                remark=_("入库付款")
                )
            purchase_in.unpaid_amount -= purchase_in.pay_amount;

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个采购入库详情"""
        return super().retrieve(request, pk, **kwargs)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新采购入库信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        purchase_in = self.perform_create_or_update(serializer, request.zt_user, is_update=True)
        return make_response(code=0, msg=_T('更新成功'), data=self.get_serializer(purchase_in).data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    @action(detail=True, methods=['put'])
    def cancel(self, request, pk=None, **kwargs):
        """取消采购入库"""
        instance = self.get_object()
        if instance.in_status != PurchaseIn.InStatusChoices.DRAFT :
            raise WmsException(_("只能取消暂存状态的入库单"))
        instance.in_status = PurchaseIn.InStatusChoices.CANCELLED
        instance.save()
        return make_response(code=0, msg=_('取消成功'), status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索采购入库，支持批次号、经办人、简短描述、供应商名称模糊查询"""
        query = request.query_params.get('query', '').strip()
        qs = self.get_queryset()
        if query:
            # 先查找匹配的供应商ID
            from WmsCore.models import Supplier, ZTUser
            supplier_ids = Supplier.objects.filter(name__icontains=query).values_list('id', flat=True)
            # 先查找匹配的经办人ID
            handler_ids = ZTUser.objects.filter(name__icontains=query).values_list('id', flat=True)
            
            # 批次号、简短描述(包含物品名称)、供应商ID、经办人ID模糊查找
            qs = qs.filter(
                models.Q(batch_number__icontains=query) |
                models.Q(short_desc__icontains=query) |
                models.Q(supplier_id__in=supplier_ids) |
                models.Q(handler_id__in=handler_ids)
            ).distinct()
        
        # 使用列表序列化器
        serializer = PurchaseInListSerializer(qs, many=True)
        return make_response(
            code=0,
            msg=_('搜索成功'),
            data=serializer.data,
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['get'])
    def StockList(self, request, pk=None, **kwargs):
        """获取入库订单的库存列表"""
        # 采购入库的订单会根据所有的批次号获取所有库存内容(跟踪所有的变动情况)
        # 其他入库的订单，只会获取当前库存表的直接关联库存
        purchase_in = PurchaseIn.objects.get(id=pk)
        if purchase_in.in_type == PurchaseIn.InTypeChoices.PURCHASE_IN:
            log.debug(f"purchase_in: {purchase_in} type: {purchase_in.in_type}")
            # 采购入库的订单会根据所有的批次号获取所有库存内容(跟踪所有的变动情况)
            track_ids = []
            purchase_in_items = PurchaseInItem.objects.filter(purchase_in=purchase_in)
            for purchase_in_item in purchase_in_items:
                if purchase_in_item.track_id != 0 and purchase_in_item.track_id not in track_ids:
                    track_ids.append(purchase_in_item.track_id)
            log.debug(f"purchase_in: {purchase_in.order_id} track_ids: {track_ids}")
            stock_ins = Stock.objects.filter(track_id__in=track_ids).order_by('in_date')
        else:
            # 其他入库的订单，只会获取当前库存表的直接关联库存
            stock_ins = Stock.objects.filter(purchase_in=purchase_in)
        
        log.debug(f"stock_ins: {stock_ins}")
        serializer = self.get_serializer(stock_ins, many=True)
        return make_response(code=0, msg='获取成功', status=status.HTTP_200_OK, data=serializer.data)

