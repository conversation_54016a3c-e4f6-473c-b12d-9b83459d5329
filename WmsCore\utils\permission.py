
from rest_framework import permissions
from common.logger import logger as log;

class IsZTUser(permissions.BasePermission):
    """
    自定义权限类，检查用户是否是ZTUser
    """
    def has_permission(self, request, view):
        # 我的 enterprise_id 跟 请求的 enterprise_id 要一致

        # 只要通过了认证，用户存在即可
        #log.debug(f"request.zt_user: {request.zt_user is None} request.user_id: {request.user_id} {request.zt_user.user_id}")
        #log.debug(f"检查用户是否是ZTUser: {bool(request.zt_user and hasattr(request.zt_user, 'user_id'))}")
        is_zt_user = bool(request.zt_user and hasattr(request.zt_user, 'user_id'))
        if not is_zt_user:
            log.error(f"检查用户是否是ZTUser: {is_zt_user} user_id: {request.user_id}")
        return is_zt_user
    
class IsLedgerPermission(permissions.BasePermission):
    """
    自定义权限类，检查用户是否是账套权限
    """
    def has_permission(self, request, view):
        # 我的 enterprise_id 跟 请求的 enterprise_id 要一致
        if request.user_enterprise != request.ledger_enterprise.id:
            log.error(f"user: {request.user_id} enterprise: {request.user_enterprise} ledger_enterprise: {request.ledger_enterprise}")
            return False;
        return True;