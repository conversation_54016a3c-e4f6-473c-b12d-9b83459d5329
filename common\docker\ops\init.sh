#!/bin/bash 
#

function GetRootPath(){
    local op=$0
    # get path before last /
    local cur_path=${op%/*}
    # get absolute path 
    local op_path=`cd $cur_path && pwd` 
    # get base path, erase "/ops" tail 
    local base_path=${op_path%ops*}
    #echo $op_path  `pwd` $base_path
    echo $base_path
}

base_path=`GetRootPath $0`

function InitDir()
{
    mkdir -p ./static/
    mkdir -p ./proxy/conf/
    mkdir -p ./config/
    mkdir -p ./logs/
}

function InitEnv()
{
    ENV_FILE="${base_path}/.env"

    echo "#env file" > ${ENV_FILE}
    echo "WORK_DIR=${base_path}" >> ${ENV_FILE}
    echo "PROJ_NAME=wms" >> ${ENV_FILE}
    echo "DJANGO_ENV=prod" >> ${ENV_FILE}
    #echo "DJANGO_ENV=dev" >> ${ENV_FILE}

}

InitDir
InitEnv
