from WmsCore.models import *
from WmsCore.data.model_data import ModelData
from common.logger import logger
from common.translation import _T


class DefaultData(ModelData):
    def __init__(self, save_to_db = True):
        super().__init__(save_to_db)

    def generate_default_data(self):
        self.Warehouse()
        self.Customer()
        self.Item()
        self.Supplier()

    def Warehouse(self):
        data1 = Warehouse( name = _T('国内仓'), location = _T('中国'), is_parent = True, parent = None)
        data2 = Warehouse( name = _T('总仓'), location = _T('中国'), is_parent = False, parent = data1)
        datas = [data1, data2]
        self.add_model_data(*datas)
        return datas

    def ItemType(self):
        data1 = CatalogTypeTree( name = _T('商品类型'), parent = None, for_type = CatalogTypeTree.ForTypeChoices.ITEM)
        data2 = CatalogTypeTree( name = _T('默认分类'), parent = data1, for_type = CatalogTypeTree.ForTypeChoices.ITEM)
        datas = [data1, data2]
        self.add_model_data(*datas)
        return datas

    def Customer(self):
        customer_type = CatalogTypeTree( name = _T('默认分类'), parent = None, for_type = CatalogTypeTree.ForTypeChoices.CUSTOMER)
        self.add_model_data(customer_type)
        data1 = Customer( name = _T('零售客户(通用)'), contact_person = _T('零售客户'), phone = '1234567890', address = _T('零售客户地址'), customer_type = customer_type)
        data2 = Customer( name = _T('批发客户(通用)'), contact_person = _T('批发客户'), phone = '1234567890', address = _T('批发客户地址'), customer_type = customer_type)
        datas = [data1, data2]
        self.add_model_data(*datas)
        return datas
    
    def UnitType(self):
        # 各个行业最常用的 20个单位
        units_names = {
            "wujin": [_T("个"), _T("箱"), _T("包"), _T("件"), _T("条"), _T("瓶"), _T("盒"), _T("袋"), _T("桶"), _T("盘"), _T("卷"), _T("张"), _T("片"), _T("根"), _T("支"), _T("束"), _T("朵"), _T("颗"), _T("粒"), _T("枚"), _T("只"), _T("双"), _T("对"), _T("套"), _T("组"), _T("台"), _T("辆"), _T("艘")],
            "default": [_T("支"), _T("箱"), _T("盒"), _T("件"), _T("条"), _T("瓶"), _T("包"), _T("袋"), _T("桶"), _T("盘"), _T("卷"), _T("张"), _T("片"), _T("根"), _T("个"), _T("束"), _T("朵"), _T("颗"), _T("粒"), _T("枚"), _T("只"), _T("双"), _T("对"), _T("套"), _T("组"), _T("台"), _T("辆"), _T("艘")],
        }
        datas = []
        for name in units_names["default"]:
            data = UnitType( name = name)
            datas.append(data)
        self.add_model_data(*datas)
        return datas

    def Item(self):
        # 6953787364954 中性笔 支
        # default
        unit_types = self.UnitType();
        item_types = self.ItemType();
        item_type = item_types[1];
        logger.debug(f"item_type: {item_type} id:{item_type.id}")

        item = Item(name = _T('中性笔'), code = '6953787364954',item_type = item_type, is_active = True)
        unit = Unit(item = item, unit_type = unit_types[0], conversion_rate = 1, retail_price = 0.9, wholesale_price = 0.8, price_strategy = [], min_price = 0.6)
        unit2 = Unit(item = item, unit_type = unit_types[1], conversion_rate = 200, retail_price = 180, wholesale_price = 160, price_strategy = [], min_price = 110)
        unit3 = Unit(item = item, unit_type = unit_types[2], conversion_rate = 10, retail_price = 9, wholesale_price = 8, price_strategy = [], min_price = 6)
        self.add_model_data(item, unit, unit2, unit3)
        item.unit = unit;
        if self.save_to_db:
            item.save()

    def Supplier(self):
        supplier_type = CatalogTypeTree( name = _T('默认分类'), parent = None, for_type = CatalogTypeTree.ForTypeChoices.SUPPLIER)
        self.add_model_data(supplier_type)
        data1 = Supplier( name = _T('仓小助有限公司'), contact_person = _T('小助'), phone = '1234567890', address = _T('北京'), supplier_type = supplier_type )
        self.add_model_data(data1)
        return data1



