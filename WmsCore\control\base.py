from django.db import transaction
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from rest_framework import pagination
from rest_framework import status
from rest_framework.exceptions import NotFound
from AccessGateway.permissions import IsPlatformAccount
from common.auth.jwt_auth import JWTAuthentication
from common.exception import WmsException
from common.make_response import make_response
#from common.auth.ledger_permission import IsLedgerPermission
from WmsCore.utils.submission import prevent_duplicate_submission
from rest_framework.response import Response
from WmsCore.utils.jwt_auth import JWTWmsCoreAuthentication
from WmsCore.utils.permission import IsZTUser, IsLedgerPermission
from django_multitenant.utils import get_current_tenant
from django.core.exceptions import ImproperlyConfigured
from common.logger import logger as log
from django.core.paginator import InvalidPage, EmptyPage



@method_decorator(csrf_exempt, name='dispatch')
class SafeModelViewSet(viewsets.ModelViewSet):
    #authentication_classes = [JWTAuthentication]
    authentication_classes = [JWTWmsCoreAuthentication]
    permission_classes = [IsZTUser, IsLedgerPermission]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if getattr(self, 'no_safe', False):
            self.authentication_classes = []
            self.permission_classes = [AllowAny]

    def list(self, request, *args, **kwargs):
        """
        重写list方法，添加分页错误处理
        """
        try:
            # 使用原始的list方法
            return super().list(request, *args, **kwargs)
        except (NotFound, InvalidPage, EmptyPage) as e:
            # 获取当前页码和总页数
            page_number = request.query_params.get('page', 1)
            try:
                page_number = int(page_number)
            except (TypeError, ValueError):
                page_number = 1
            
            # 获取总页数
            queryset = self.filter_queryset(self.get_queryset())
            page_size = self.get_paginator().get_page_size(request)
            total_count = queryset.count()
            total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 1
            
            # 构建友好的错误信息
            if page_number <= 0:
                error_msg = f"页码必须大于0，当前请求页码: {page_number}"
            elif page_number > total_pages:
                error_msg = f"请求的页码 {page_number} 超出范围，总页数: {total_pages}"
            else:
                error_msg = f"无效的分页请求: {str(e)}"
            
            log.warning(f"分页错误: {error_msg}")
            return make_response(
                code=1, 
                msg=error_msg, 
                data={"current_page": page_number, "total_pages": total_pages, "total_count": total_count},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            log.error(f"列表查询未处理异常: {str(e)}")
            return make_response(
                code=1,
                msg="获取数据列表失败",
                data={"error_details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get_paginator(self):
        """
        获取分页器实例
        """
        if not hasattr(self, '_paginator'):
            if self.pagination_class is None:
                self._paginator = None
            else:
                self._paginator = self.pagination_class()
        return self._paginator
            
    def get_queryset(self):
        """
        重写 get_queryset 方法，确保所有查询都自动包含租户过滤条件
        """
        queryset = super().get_queryset()
        
        # 检查模型是否是多租户模型
        if hasattr(queryset.model, 'TenantMeta'):
            # 获取当前租户
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error("当前租户上下文未设置，无法进行数据查询")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(queryset.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 构建租户过滤条件
            tenant_filter = {tenant_field: current_tenant.id}
            
            # 应用租户过滤
            queryset = queryset.filter(**tenant_filter)
            
            # log.debug(f"应用租户过滤: {queryset.model.__name__} - {tenant_filter}")
        
        return queryset

    def perform_create(self, serializer):
        """
        重写 perform_create 方法，确保创建时自动设置租户ID
        """
        # 检查模型是否是多租户模型
        if hasattr(serializer.Meta.model, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error("当前租户上下文未设置，无法创建数据")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(serializer.Meta.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 自动设置租户ID
            serializer.save(**{tenant_field: current_tenant.id})
            log.debug(f"创建数据时自动设置租户ID: {serializer.Meta.model.__name__} - {tenant_field}={current_tenant.id}")
        else:
            serializer.save()

    def perform_update(self, serializer):
        """
        重写 perform_update 方法，确保更新时验证租户权限
        """
        instance = serializer.instance
        
        # 检查模型是否是多租户模型
        if hasattr(instance.__class__, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error("当前租户上下文未设置，无法更新数据")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(instance.__class__.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 验证数据是否属于当前租户
            instance_tenant_id = getattr(instance, tenant_field)
            if instance_tenant_id != current_tenant.id:
                log.error(f"尝试更新其他租户的数据: 实例租户ID={instance_tenant_id}, 当前租户ID={current_tenant.id}")
                raise ImproperlyConfigured("无权操作其他租户的数据")
            
            log.debug(f"更新数据时验证租户权限: {instance.__class__.__name__} - {tenant_field}={current_tenant.id}")
        
        serializer.save()

    def perform_destroy(self, instance):
        """
        重写 perform_destroy 方法，确保删除时验证租户权限
        """
        # 检查模型是否是多租户模型
        if hasattr(instance.__class__, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error("当前租户上下文未设置，无法删除数据")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(instance.__class__.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 验证数据是否属于当前租户
            instance_tenant_id = getattr(instance, tenant_field)
            if instance_tenant_id != current_tenant.id:
                log.error(f"尝试删除其他租户的数据: 实例租户ID={instance_tenant_id}, 当前租户ID={current_tenant.id}")
                raise ImproperlyConfigured("无权操作其他租户的数据")
            
            log.debug(f"删除数据时验证租户权限: {instance.__class__.__name__} - {tenant_field}={current_tenant.id}")
        
        instance.delete()

    def get_object(self):
        """
        重写 get_object 方法，确保获取单个对象时也应用租户过滤
        """
        # 调用父类的 get_object，它会使用我们重写的 get_queryset 方法
        obj = super().get_object()
        
        # 额外的租户验证（双重保险）
        if hasattr(obj.__class__, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error("当前租户上下文未设置，无法获取数据")
                raise ImproperlyConfigured("租户上下文未设置")
            
            tenant_field = getattr(obj.__class__.TenantMeta, 'tenant_field_name', 'ledger_id')
            instance_tenant_id = getattr(obj, tenant_field)
            
            if instance_tenant_id != current_tenant.id:
                log.error(f"尝试获取其他租户的数据: 实例租户ID={instance_tenant_id}, 当前租户ID={current_tenant.id}")
                raise ImproperlyConfigured("无权访问其他租户的数据")
        
        return obj

    def finalize_response(self, request, response, *args, **kwargs):
        # 先调用父类，拿到标准Response对象
        response = super().finalize_response(request, response, *args, **kwargs)

        # 只处理Response对象，HttpResponse等不处理
        if isinstance(response, Response):
            # 如果已经是你想要的结构就不包裹
            if not (isinstance(response.data, dict) and {'code', 'msg', 'status', 'data'}.issubset(response.data.keys())):
                result = {
                    'code': response.data.get('code', 0) if isinstance(response.data, dict) else 0,
                    'msg': response.data.get('msg', 'success') if isinstance(response.data, dict) else 'success',
                    'status': response.status_code,
                    'data': response.data if not (isinstance(response.data, dict) and 'data' in response.data) else response.data['data']
                }
                response.data = result
                # 重新渲染
                response._is_rendered = False
                response.render()
        return response


class StandardResultsSetPagination(pagination.PageNumberPagination):
    page_size = 20 
    page_size_query_param = 'page_size'
    max_page_size = 500

class HugeResultsSetPagination(pagination.PageNumberPagination):
    page_size = 200 
    page_size_query_param = 'page_size'
    max_page_size = 10000

