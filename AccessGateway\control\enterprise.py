from django.db import transaction
from rest_framework import status

from AccessGateway.control.auth import make_response
from AccessGateway.models import Enterprise
from AccessGateway.serializers import EnterpriseSerializer
from common.APIViewSafe import APIViewSafe
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _

# class EnterpriseAPI(APIViewSafe):

#     @transaction.atomic
#     def post(self, request):
#         """
#         创建企业
#         """
#         try:
#             serializer = EnterpriseSerializer(data=request.data)
#             # 尝试进行序列化验证
#             try:
#                 is_valid = serializer.is_valid(raise_exception=True)
#             except Exception as serialization_error:
#                 # 捕获序列化过程中的错误
#                 log.warning(f"创建企业参数验证失败,错误: {serialization_error}")
#                 return make_response(
#                     code=1,
#                     msg=_('参数错误: ') + str(serialization_error),
#                     data=request.data,
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             if is_valid:
#                 data = serializer.validated_data
#                 enterprise = Enterprise.objects.create(
#                     enterprise_name=data['enterprise_name'],
#                     enterprise_phone=data['enterprise_phone'],
#                     enterprise_code=data['enterprise_code'],
#                     enterprise_address=data['enterprise_address'],
#                     enterprise_type=data['enterprise_type']
#                 )
#                 log.info(f"创建企业成功: id={enterprise.id}, name={data['enterprise_name']}")

#                 return make_response(
#                     code=0,
#                     msg=_('创建成功'),
#                     data={
#                         'enterprise_id': enterprise.id,
#                         'enterprise_name': enterprise.enterprise_name
#                     },
#                     status=status.HTTP_200_OK
#                 )
#         except Exception as e:
#             log.error(f"创建企业失败: {str(e)}")
#             return make_response(
#                 code=1,
#                 msg=_('创建失败: ') + str(e),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

#     @transaction.atomic
#     def put(self, request):
#         """
#         修改企业信息
#         """
#         log.info("修改企业信息请求")
#         try:
#             data = request.data
#             enterprise = Enterprise.objects.filter(id=data['enterprise_id']).update(
#                 enterprise_name=data['enterprise_name'],
#                 enterprise_phone=data['enterprise_phone'],
#                 enterprise_code=data['enterprise_code'],
#                 enterprise_address=data['enterprise_address'],
#                 enterprise_type=data['enterprise_type']
#             )
#             return make_response(
#                 code=0,
#                 msg=_('修改成功'),
#                 data={
#                     'enterprise_id': data['enterprise_id'],
#                     'enterprise_name': data['enterprise_name'],
#                     'enterprise_phone': data['enterprise_phone'],
#                     'enterprise_code': data['enterprise_code'],
#                     'enterprise_address': data['enterprise_address'],
#                     'enterprise_type': data['enterprise_type']
#                 },
#                 status=status.HTTP_200_OK
#             )
#         except Exception as e:
#             log.error(f"修改企业信息失败: {str(e)}")
#             return make_response(
#                 code=1,
#                 msg=_('修改失败: ') + str(e),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )
