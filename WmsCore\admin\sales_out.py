from WmsCore.models import Item, Unit, SalesOutItem


class SalesOutAdmin(object):
    @staticmethod
    def sales_draft_items(sales_out, sale_price, quantity, item: Item, unit: Unit):
        """
        创建出库物品但不扣库存
                :param unit: 单位
                :param quantity:
                :param sale_price: 销售价格
                :param sales_out: SalesOut实例
                :param item: Item实例
                :return: SalesOutItem
        """
        # 创建出库物品记录
        out_item = SalesOutItem.objects.create(
            sales_out=sales_out,
            item=item,
            quantity=quantity,
            out_cost=0,  # 零售的没有出库成本？
            final_cost=0,
            unit=unit,
            sale_price=sale_price
        )
        return out_item
