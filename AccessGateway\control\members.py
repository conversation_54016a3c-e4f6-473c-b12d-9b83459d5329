import time
from django.http import HttpResponse
from AccessGateway.control.auth import make_response
from AccessGateway.models import CXZUser, StatusPermission
from AccessGateway.models import Enterprise 
from common.APIViewSafe import APIViewSafe
from common.db.Redis import RedisUtil
from common.logger import logger as log
from AccessGateway.model.ret_code import RetCode
import random
import string
from django.utils.translation import gettext_lazy as _

class InviteCode:
    @staticmethod
    def create_invite_code():
        # 生成由字母和数字组成的六位邀请码
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if not RedisUtil.get(f"invite_code:{code}"):  # 检查Redis中是否已存在该邀请码
                return code

    @staticmethod
    def create_invite(enterprise_id, day: int = 7):
        if RedisUtil.get(f"invite_codee:{enterprise_id}"):
            invite_code = RedisUtil.get(f"invite_codee:{enterprise_id}")
            RedisUtil.delete(f"invite_codee:{enterprise_id}")
            RedisUtil.delete(f"invite_code:{invite_code}")
            log.debug(f"公司已经存在该邀请码:{invite_code}，删除后重新生成")
        invite_code = InviteCode.create_invite_code()
        duration_time = day * 86400
        expire_time = int(time.time()) + duration_time
        RedisUtil.set(f"invite_code:{invite_code}", enterprise_id, timeout=duration_time)
        RedisUtil.set(f"invite_code_expire:{invite_code}", expire_time, timeout=duration_time)
        RedisUtil.set(f"invite_codee:{enterprise_id}", invite_code, timeout=duration_time)
        log.debug(f"创建邀请码:{invite_code}，有效期:{day}天")
        return invite_code, expire_time


class InviteCreateAPIView(APIViewSafe):
    def get_enterprise_id(self, user_id):
        if user_id is None:
            return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
        platform_account = CXZUser.objects.filter(id=user_id).first();
        if platform_account is None:
            return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
        if platform_account.account_type != CXZUser.AccountTypeChoices.ADMIN:
            return make_response(code=RetCode.ACCOUNT_NOT_ADMIN, msg=_("需要有管理员权限"), status=400)
        if platform_account.enterprise_id is None:
            return make_response(code=RetCode.ACCOUNT_NOT_BIND_ENTERPRISE, msg=_("请先绑定企业"), status=400)
        return platform_account.enterprise_id

    def post(self, request):
        """创建邀请码"""
        user_id = request.user_id;
        try:
            result = self.get_enterprise_id(user_id);
            if isinstance(result, HttpResponse):
                return result;
            enterprise_id = result;
            create_flag = request.data.get('create', 0)
            if create_flag == 0:
                invite_code = RedisUtil.get(f"invite_codee:{enterprise_id}")
                if invite_code:
                    expire_time = RedisUtil.get(f"invite_code_expire:{invite_code}")
                    return make_response(code=0, msg=_("获取成功"), data={"code": invite_code, "expire_time": expire_time}, status=200)
            # 新建/重置邀请码
            day = request.data.get('day', 3)
            if day < 1:
                day = 3;
            if day > 7:
                day = 7;
            invite_code, expire_time = InviteCode.create_invite(enterprise_id, day)
            return make_response(code=0, msg=_("创建成功"), data={"invite_code": invite_code, "expire_time": expire_time}, status=200)
        except  Exception as e:
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("创建失败: ") + str(e), status=500)

    # def get(self,  request):
    #     """获取企业邀请码"""
    #     try:
    #         user_id = request.user_id;
    #         result = self.get_enterprise_id(user_id);
    #         if isinstance(result, HttpResponse):
    #             return result;
    #         enterprise_id = result;
    #         invite_code = RedisUtil.get(f"invite_codee:{enterprise_id}")
    #         if not invite_code:
    #             return make_response(code=RetCode.UNKOWN_ERROR, msg="邀请码不存在", status=400);
    #         expire_time = RedisUtil.get(f"invite_code_expire:{invite_code}")
    #         return make_response(code=0, msg="获取成功", data={"code": invite_code, "expire_time": expire_time}, status=200)
    #     except Exception as e:
    #         return make_response(code=RetCode.UNKOWN_ERROR, msg=f"获取失败: {str(e)}", status=500)

    #关闭邀请码
    def delete(self, request):
        try:
            user_id = request.user_id;
            result = self.get_enterprise_id(user_id);
            if isinstance(result, HttpResponse):
                return result;
            enterprise_id = result;
            invite_code = RedisUtil.get(f"invite_codee:{enterprise_id}")
            if not invite_code:
                return make_response(code=0, msg=_("删除成功"), status=200)
            RedisUtil.delete(f"invite_code:{invite_code}")
            RedisUtil.delete(f"invite_codee:{enterprise_id}")
            return make_response(code=0, msg=_("删除成功"), status=200)
        except  Exception as e:
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("删除失败: ") + str(e), status=500)


class InviteToAPIView(APIViewSafe):
    def post(self, request):
        """检查邀请码"""
        try:
            user_id = request.user_id;
            invite_code = request.data.get('invite_code', "")
            if invite_code == "":
                return make_response(code=RetCode.INVITE_CODE_NOT_EXIST, msg=_("请输入邀请码"), status=400)
            if not RedisUtil.get(f"invite_code:{invite_code}"):
                return make_response(code=RetCode.INVITE_CODE_NOT_EXIST, msg=_("邀请码不存在"), status=400)
            enterprise_id = RedisUtil.get(f"invite_code:{invite_code}")
            enterprise = Enterprise.objects.filter(id=enterprise_id).first()
            if enterprise is None:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            platform_account = CXZUser.objects.filter(id=user_id).first()
            if platform_account is None:
                return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
            if platform_account.enterprise_id != platform_account.owner_enterprise_id:
                #加入了其他人的企业
                log.info(f"user:{platform_account.id} already join other enterprise:{platform_account.enterprise_id} own enterprise:{platform_account.owner_enterprise_id}")
                return make_response(code=RetCode.ENTERPRISE_ALREADY_JOIN, msg=_("用户已绑定企业"), status=400)
            if platform_account.enterprise.member_count <= 1:
                #加入了多人的企业
                log.info(f"user:{platform_account.id} already in enterprise:{platform_account.enterprise_id} member count:{platform_account.enterprise.member_count}")
                return make_response(code=RetCode.ENTERPRISE_ALREADY_JOIN, msg=_("用户已在多人企业中"), status=400)

            platform_account.enterprise = enterprise
            platform_account.account_type = CXZUser.AccountTypeChoices.MEMBER;
            platform_account.save()
            enterprise.member_count += 1
            enterprise.save()
            log.info(f"用户{user_id}绑定到企业{enterprise_id} {enterprise.enterprise_name}")

            return make_response(code=0, msg=_("加入成功"),
                                 data={"enterprise_id": enterprise_id, "enterprise_name": enterprise.enterprise_name},
                                 status=200)
        except  Exception as e:
            return make_response(code=1, msg=_("验证失败: ") + str(e), status=500)
    
    def get(self, request):
        # 获得受邀企业信息
        try:
            invite_code = request.data.get('invite_code', "")
            if invite_code == "" or not RedisUtil.get(f"invite_code:{invite_code}"):
                return make_response(code=RetCode.INVITE_CODE_NOT_EXIST, msg=_("邀请码不存在"), status=400)
            enterprise_id = RedisUtil.get(f"invite_code:{invite_code}")
            if enterprise_id is None:
                return make_response(code=RetCode.INVITE_CODE_NOT_EXIST, msg=_("邀请码不存在"), status=400)
            enterprise = Enterprise.objects.filter(id=enterprise_id).first()
            if enterprise is None:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            return make_response(code=0, msg=_("获取成功"), data={"enterprise_id": enterprise_id, "enterprise_name": enterprise.enterprise_name}, status=200)
        except  Exception as e:
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("获取失败: ") + str(e), status=500)

class EnterpriseMemberAlterAPIView(APIViewSafe):
    def post(self, request):
        """修改企业成员权限"""
        # 调整权限
        try:
            user_id = request.user_id;
            member_id = int(request.data.get('member_id', 0))
            if member_id == 0:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            if member_id == user_id:
                return make_response(code=RetCode.ENTERPRISE_MEMBER_NOT_ALLOW_SELF, msg=_("不能修改自己的权限"), status=400)
            account_type = request.data.get('account_type')
            if not account_type or account_type not in CXZUser.AccountTypeChoices.values:
                return make_response(code=RetCode.ENTERPRISE_MEMBER_INVALID_ACCOUNT_TYPE, msg=_("无效的权限"), status=400)
            log.info(f"user:{user_id} 修改企业成员权限: {member_id} {account_type}")
            platform_account = CXZUser.objects.filter(id=user_id).first()   
            if platform_account is None:
                return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
            if platform_account.account_type != CXZUser.AccountTypeChoices.ADMIN:
                return make_response(code=RetCode.ACCOUNT_NOT_ADMIN, msg=_("需要有管理员权限"), status=400)
            enterprise_id = platform_account.enterprise_id;
            if not enterprise_id:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            member = CXZUser.objects.filter(id=member_id).first()
            if member is None or member.enterprise_id != enterprise_id:
                return make_response(code=RetCode.ENTERPRISE_MEMBER_NOT_EXIST, msg=_("企业成员不存在"), status=400)

            member.account_type = account_type;
            member.save()
            return make_response(code=0, msg=_("修改成功"), status=200)
        except  Exception as e:
            log.error(f"修改企业成员权限失败: {str(e)}")
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("修改失败: "), status=500)
        
    def get(self, request):
        """获取企业成员列表"""
        try:
            user_id = request.user_id;
            log.info(f"user:{user_id} 获取企业成员列表")
            platform_account = CXZUser.objects.filter(id=user_id).first()   
            if platform_account is None:
                return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
            if platform_account.account_type != CXZUser.AccountTypeChoices.ADMIN:
                return make_response(code=RetCode.ACCOUNT_NOT_ADMIN, msg=_("需要有管理员权限"), status=400)
            enterprise_id = platform_account.enterprise_id;
            if not enterprise_id:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            members = CXZUser.objects.filter(enterprise_id=enterprise_id).all();
            member_list = []
            for member in members:
                member_list.append({
                    "id": member.id,
                    "name": member.nick_name,
                    "account_type": member.account_type
                })
            return make_response(code=0, msg=_("获取成功"), data={"members": member_list}, status=200)
        except  Exception as e:
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("获取失败: ") + str(e), status=500)
        
    def delete(self, request):
        """删除企业成员"""
        try:
            user_id = request.user_id;
            member_id = int(request.data.get('member_id', 0))
            if member_id == 0:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            if member_id == user_id:
                return make_response(code=RetCode.ENTERPRISE_MEMBER_NOT_ALLOW_SELF, msg=_("不能删除自己的账号"), status=400)    
            platform_account = CXZUser.objects.filter(id=user_id).first()   
            if platform_account is None:
                return make_response(code=RetCode.ACCOUNT_NOT_LOGIN, msg=_("请先登录"), status=400)
            if platform_account.account_type != CXZUser.AccountTypeChoices.ADMIN:
                return make_response(code=RetCode.ACCOUNT_NOT_ADMIN, msg=_("需要有管理员权限"), status=400)
            enterprise_id = platform_account.enterprise_id;
            if not enterprise_id:
                return make_response(code=RetCode.ENTERPRISE_NOT_EXIST, msg=_("企业不存在"), status=400)
            member = CXZUser.objects.filter(id=member_id).first()
            if member is None or member.enterprise_id != enterprise_id:
                return make_response(code=RetCode.ENTERPRISE_MEMBER_NOT_EXIST, msg=_("企业成员不存在"), status=400)
            member.enterprise = None;
            if member.owner_enterprise_id == enterprise_id:
                #自己创建的企业数据，但是被人赶出去了
                member.account_type = CXZUser.AccountTypeChoices.MEMBER;
                member.owner_enterprise = None;
            else:
                member.account_type = CXZUser.AccountTypeChoices.ADMIN;
                member.enterprise = member.owner_enterprise;
            member.save()
            platform_account.enterprise.member_count -= 1
            platform_account.enterprise.save()
            #顺便删除账套上的授权数据
            enterprise_permissions = StatusPermission.objects.filter(platform_account_id=member_id ).all()
            for permission in enterprise_permissions:
                permission.delete()
            return make_response(code=0, msg=_("删除成功"), status=200)
        except  Exception as e:
            return make_response(code=RetCode.UNKOWN_ERROR, msg=_("删除失败: ") + str(e), status=500)