#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASR WebSocket客户端示例
演示如何向Django WebSocket服务发送音频数据进行语音识别
"""

import websocket
import json
import base64
import time
import ssl

status = "init"
def on_message(ws, message):
    """处理服务器返回的消息"""
    global status
    try:
        data = json.loads(message)
        msg_type = data.get('type', '')
        
        if msg_type == 'connection':
            status = "start"
            print(f"连接状态: {status} {data.get('message', '')}")
        # elif msg_type == 'start':
        #     status = "start"
        #     print(f"服务状态: {status} {data.get('message', '')}")
        elif msg_type == 'result':

            print(f"识别结果: {data.get('text', '')}")
        elif msg_type == 'error':
            status = "error"
            print(f"错误信息: {status} {data.get('message', '')}")
        else:
            print(f"收到消息: {msg_type} {message}")
            
    except Exception as e:
        print(f"解析消息失败: {e}")

def on_error(ws, error):
    """处理连接错误"""
    print(f"WebSocket错误: {error}")

def on_close(ws, close_status_code, close_msg):
    """处理连接关闭"""
    print("WebSocket连接已关闭")

def on_open(ws):
    """连接建立后的处理"""
    print("WebSocket连接已建立")
    
    # 发送开始识别信号
    start_msg = {
        'type': 'start'
    }
    ws.send(json.dumps(start_msg))
    
    # 模拟发送音频数据
    def send_audio_data():
        try:
            # 这里应该是真实的音频文件，示例中使用测试音频文件
            audio_file_path = "./name1.wav"  # 请替换为实际的音频文件路径
            
            # 如果有音频文件，读取并发送
            try:
                with open(audio_file_path, "rb") as f:
                    frame_size = 8000  # 每帧8000字节
                    frame_count = 0
                    
                    while True:
                        if status == "init":
                            time.sleep(0.04)
                            print("远端准备开始")
                            continue
                        if status == "end":
                            print("远端关闭")
                            break
                        if status == "error":
                            print("远端错误")
                            break;
                        audio_chunk = f.read(frame_size)
                        if not audio_chunk:
                            break
                            
                        frame_count += 1
                        
                        # 将音频数据编码为base64
                        audio_b64 = base64.b64encode(audio_chunk).decode('utf-8')
                        
                        # 发送音频数据
                        audio_msg = {
                            'type': 'audio',
                            'audio': str(audio_b64),
                            'is_end': False
                        }
                        ws.send(json.dumps(audio_msg))
                        
                        print(f"发送第{frame_count}帧音频数据")
                        time.sleep(0.04)  # 模拟实时音频流间隔
                        
                    # 发送结束信号
                    end_msg = {
                        'type': 'end'
                    }
                    ws.send(json.dumps(end_msg))
                    print("音频发送完毕")
                    
            except FileNotFoundError:
                print(f"音频文件 {audio_file_path} 不存在")
                # 发送测试数据
                test_msg = {
                    'type': 'audio',
                    'audio': '',  # 空音频数据仅用于测试连接
                    'is_end': True
                }
                ws.send(json.dumps(test_msg))
                print("发送测试数据")
                
        except Exception as e:
            print(f"发送音频数据失败: {e}")
    
    # 在新线程中发送音频数据
    import _thread as thread
    thread.start_new_thread(send_audio_data, ())

def test_asr_websocket():
    """测试ASR WebSocket服务"""
    # WebSocket服务地址，请根据实际情况修改
    ws_url = "ws://localhost:8001/ws/asr/"  # 请替换为实际的WebSocket地址
    
    websocket.enableTrace(False)
    ws = websocket.WebSocketApp(
        ws_url,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close,
        on_open=on_open
    )
    
    # 运行WebSocket客户端
    ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})

if __name__ == "__main__":
    print("ASR WebSocket客户端示例")
    print("确保Django服务已启动并配置了WebSocket路由")
    print("开始连接...")
    
    test_asr_websocket()
