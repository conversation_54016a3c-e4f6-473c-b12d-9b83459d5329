# 根据不同行业创建初始数据
from WmsCore.data.default_data import DefaultData
from django_multitenant.utils import get_current_tenant, set_current_tenant, unset_current_tenant
from common.logger import logger


def create_initial_data(industry : str):
    if industry == 'wujin':
        create_wujin_initial_data()
    else:
        create_default_initial_data()

def create_wujin_initial_data():
    create_default_initial_data()
    pass

def create_default_initial_data():
    default_data = DefaultData(save_to_db = False)
    default_data.generate_default_data()

    #default_data.save_model_data_to_db()
    default_data.save_model_data_to_sqlfile("model_data.sql")

def make_default_data_to_db( tenant :str):
    from AccessGateway.models import Ledger
    ledger = Ledger.objects.get(ledger_name=tenant)
    logger.debug(f"make default with ledger: {ledger} id:{ledger.id} name:{ledger.ledger_name}")
    current_tenant = get_current_tenant()
    set_current_tenant(ledger)
    default_data = DefaultData(save_to_db = True)
    default_data.generate_default_data()
    default_data.save_model_data_to_db()
    if current_tenant is not None:
        set_current_tenant(current_tenant)
    else:
        unset_current_tenant()