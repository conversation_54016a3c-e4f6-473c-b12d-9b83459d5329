from django.db import models
from django_multitenant.utils import get_current_tenant
from django.core.exceptions import ImproperlyConfigured
from common.logger import logger as log


class TenantModelManager(models.Manager):
    """
    多租户模型管理器，确保所有查询都自动包含租户过滤条件
    """
    
    def get_queryset(self):
        """
        重写 get_queryset 方法，自动添加租户过滤条件
        """
        queryset = super().get_queryset()
        
        # 检查模型是否是多租户模型
        if hasattr(self.model, 'TenantMeta'):
            # 获取当前租户
            current_tenant = get_current_tenant()
            if current_tenant is None:
                # log.error(f"当前租户上下文未设置，无法查询 {self.model.__name__}")
                # raise ImproperlyConfigured("租户上下文未设置")
                return queryset
            
            # 获取租户字段名
            tenant_field = getattr(self.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 构建租户过滤条件
            tenant_filter = {tenant_field: current_tenant.id}
            
            # 应用租户过滤
            queryset = queryset.filter(**tenant_filter)
            
            #log.debug(f"TenantModelManager 应用租户过滤: {self.model.__name__} - {tenant_filter}")
        
        return queryset
    
    def create(self, **kwargs):
        """
        重写 create 方法，自动设置租户ID
        """
        # 检查模型是否是多租户模型
        if hasattr(self.model, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error(f"当前租户上下文未设置，无法创建 {self.model.__name__}")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(self.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 如果未提供租户ID，自动设置
            if tenant_field not in kwargs:
                kwargs[tenant_field] = current_tenant.id
                log.debug(f"TenantModelManager 自动设置租户ID: {self.model.__name__} - {tenant_field}={current_tenant.id}")
        
        return super().create(**kwargs)
    
    def get_or_create(self, defaults=None, **kwargs):
        """
        重写 get_or_create 方法，确保租户隔离
        """
        # 检查模型是否是多租户模型
        if hasattr(self.model, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error(f"当前租户上下文未设置，无法执行 get_or_create {self.model.__name__}")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(self.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 确保查询条件包含租户ID
            if tenant_field not in kwargs:
                kwargs[tenant_field] = current_tenant.id
            
            # 确保默认值包含租户ID
            if defaults is None:
                defaults = {}
            if tenant_field not in defaults:
                defaults[tenant_field] = current_tenant.id
        
        return super().get_or_create(defaults=defaults, **kwargs)
    
    def update_or_create(self, defaults=None, **kwargs):
        """
        重写 update_or_create 方法，确保租户隔离
        """
        # 检查模型是否是多租户模型
        if hasattr(self.model, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error(f"当前租户上下文未设置，无法执行 update_or_create {self.model.__name__}")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(self.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 确保查询条件包含租户ID
            if tenant_field not in kwargs:
                kwargs[tenant_field] = current_tenant.id
            
            # 确保默认值包含租户ID
            if defaults is None:
                defaults = {}
            if tenant_field not in defaults:
                defaults[tenant_field] = current_tenant.id
        
        return super().update_or_create(defaults=defaults, **kwargs)

    def bulk_create(self, objs, batch_size=None, ignore_conflicts=False, update_conflicts=False, update_fields=None, unique_fields=None):
        """
        重写 bulk_create 方法，自动设置租户ID
        """
        # 检查模型是否是多租户模型
        if hasattr(self.model, 'TenantMeta'):
            current_tenant = get_current_tenant()
            if current_tenant is None:
                log.error(f"当前租户上下文未设置，无法批量创建 {self.model.__name__}")
                raise ImproperlyConfigured("租户上下文未设置")
            
            # 获取租户字段名
            tenant_field = getattr(self.model.TenantMeta, 'tenant_field_name', 'ledger_id')
            
            # 为每个对象自动设置租户ID
            for obj in objs:
                if not hasattr(obj, tenant_field) or getattr(obj, tenant_field) is None:
                    setattr(obj, tenant_field, current_tenant.id)
                    log.debug(f"TenantModelManager 批量创建时自动设置租户ID: {self.model.__name__} - {tenant_field}={current_tenant.id}")
        
        return super().bulk_create(objs, batch_size=batch_size, ignore_conflicts=ignore_conflicts, 
                                  update_conflicts=update_conflicts, update_fields=update_fields, 
                                  unique_fields=unique_fields)


class TenantModelMixin:
    """
    多租户模型混入类，提供租户相关的通用方法
    """
    
    @classmethod
    def get_current_tenant_id(cls):
        """
        获取当前租户ID
        """
        current_tenant = get_current_tenant()
        if current_tenant is None:
            raise ImproperlyConfigured("租户上下文未设置")
        return current_tenant.id
    
    @classmethod
    def get_tenant_field_name(cls):
        """
        获取租户字段名
        """
        if hasattr(cls, 'TenantMeta'):
            return getattr(cls.TenantMeta, 'tenant_field_name', 'ledger_id')
        return 'ledger_id'
    
    def belongs_to_current_tenant(self):
        """
        检查当前实例是否属于当前租户
        """
        if not hasattr(self.__class__, 'TenantMeta'):
            return True
        
        current_tenant = get_current_tenant()
        if current_tenant is None:
            return False
        
        tenant_field = self.get_tenant_field_name()
        instance_tenant_id = getattr(self, tenant_field)
        return instance_tenant_id == current_tenant.id 



