# Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};

    location /api {
      proxy_pass http://wms_app:8000;
      include proxy.conf;
    }

    location /ztx {
      proxy_pass http://wms_app:8000;
      include proxy.conf;
    }

    location /media {
      proxy_pass http://wms_app:8000;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://wms_app:8000;
      include proxy.conf;
    }

    location / {
      proxy_pass http://wms_app:8000;
      include proxy.conf;
    }

    # placeholder for acme challenge location
    ${ACME_CHALLENGE_LOCATION}

    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
