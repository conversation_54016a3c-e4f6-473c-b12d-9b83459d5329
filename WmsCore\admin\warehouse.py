import datetime
from WmsCore.models import Warehouse, Item, Stock
from django.db.models import Sum
from common.logger import logger as log


class WarehouseAdmin():
    @staticmethod
    def UpdateItemQuantity(item: Item, warehouse: Warehouse):
        # 更新库存物品数量信息到物品表中，方便销售或者开单时，快速提取
        # 1. 获取库存物品 数量总和，剩余数量总和 均摊费用总和
        stocks = Stock.objects.filter(item=item, remaining_quantity__gt=0).aggregate(
            total_remaining_quantity=Sum('remaining_quantity'),
            total_cost=Sum('actual_cost'),
            total_allocated_cost=Sum('allocated_cost')
        )
        # log.debug(f"stocks: {stocks} item id: {item.id} item name: {item.name} item unit: {item.unit} item :{item}")

        # 更新物品表中的数量信息

        item.total_stock = stocks['total_remaining_quantity'] or 0
        item.total_cost = (stocks['total_cost'] or 0) + (stocks['total_allocated_cost'] or 0)
        if item.expiry_days > 0:
            # 有效期只剩下1/3时，开始计算临期或者过期商品数量
            expiry_date = datetime.datetime.now() - datetime.timedelta(days=int(item.expiry_days * 2 / 3))
            expiry_stocks = Stock.objects.filter(item=item, remaining_quantity__gt=0,
                                                 in_date__lte=expiry_date).aggregate(
                total_expiry_quantity=Sum('remaining_quantity')
            )
            item.expiry_quantity = expiry_stocks['total_expiry_quantity'] or 0
        # 计算已下单未提货的商品数量，提前锁定库存需求

        item.save()
