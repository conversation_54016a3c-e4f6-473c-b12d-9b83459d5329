import datetime
from channels.generic.websocket import WebsocketConsumer
from channels.exceptions import StopConsumer
import datetime
import hashlib
import base64
import hmac
from urllib.parse import urlencode
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import json
import websocket
import ssl
import _thread as thread
import time

class IFlyAsr():
    def __init__(self, ws_cnt: WebsocketConsumer ):
        self.APPID = '67359bdf'
        self.APIKey = '9c707bb744cff48a2ad2c22ae0401589'
        self.APISecret = 'MGU4ZGRiZTQ1MTc3NjYyNWQ0Y2Q4MWRj'

        # 公共参数(common)
        self.CommonArgs = {"app_id": self.APPID}
        # 业务参数(business)，更多个性化参数可在官网查看
        self.BusinessArgs = {"domain": "iat", "language": "zh_cn", "accent": "mandarin", "vinfo":1,"vad_eos":10000}
        self.remote_ws = None
        self.ws_cnt = ws_cnt
        self.audio_status = 0  # 0: 第一帧, 1: 中间帧, 2: 最后一帧

    # 生成url
    def create_url(self):
        url = 'wss://ws-api.xfyun.cn/v2/iat'
        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))

        # 拼接字符串
        signature_origin = "host: " + "ws-api.xfyun.cn" + "\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET " + "/v2/iat " + "HTTP/1.1"
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(self.APISecret.encode('utf-8'), signature_origin.encode('utf-8'),
                                 digestmod=hashlib.sha256).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')

        authorization_origin = "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
            self.APIKey, "hmac-sha256", "host date request-line", signature_sha)
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": "ws-api.xfyun.cn"
        }
        # 拼接鉴权参数，生成url
        url = url + '?' + urlencode(v)
        return url
    
    def close(self):
        if self.remote_ws:
            self.remote_ws.close()
    
    def connect_to(self):
        """连接到远端语音识别服务"""
        try:
            ws_url = self.create_url()
            websocket.enableTrace(False)
            self.remote_ws = websocket.WebSocketApp(
                ws_url,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            
            # 在新线程中运行远端websocket连接
            def run_ws():
                self.remote_ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
            
            thread.start_new_thread(run_ws, ())
            
        except Exception as e:
            print(f"连接远端服务失败: {e}")
            self.ws_cnt.send(text_data=json.dumps({
                'type': 'error',
                'message': f'语音识别服务失败: {str(e)}'
            }))
    
    def on_open(self, ws):
        """远端websocket连接建立"""
        print("与讯飞语音识别服务连接建立")
        self.ws_cnt.send(text_data=json.dumps({
            'type': 'succ',
            'message': '语音识别服务连接成功'
        }))
    
    def on_message(self, ws, message):
        """处理远端websocket返回的识别结果"""
        try:
            result = json.loads(message)
            code = result.get("code", 0)
            sid = result.get("sid", "")
            
            if code != 0:
                error_msg = result.get("message", "未知错误")
                print(f"识别错误 sid:{sid}, error:{error_msg}, code:{code}")
                self.ws_cnt.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'识别错误: {error_msg}'
                }))
            else:
                # 解析识别结果
                data = result.get("data", {}).get("result", {}).get("ws", [])
                text_result = ""
                for item in data:
                    for word in item.get("cw", []):
                        text_result += word.get("w", "")
                
                print(f"识别结果 sid:{sid}, text:{result}")
                if text_result:
                    print(f"识别结果 sid:{sid}, text:{text_result}")
                    self.ws_cnt.send(text_data=json.dumps({
                        'type': 'result',
                        'text': text_result,
                        'sid': sid
                    }))
                    
        except Exception as e:
            print(f"131:解析识别结果异常: {e} message:{message}")
            self.ws_cnt.send(text_data=json.dumps({
                'type': 'error',
                'message': f'解析结果失败: {str(e)}'
            }))
    
    def on_error(self, ws, error):
        """远端websocket错误处理"""
        print(f"远端websocket错误: {error}")
        self.ws_cnt.send(text_data=json.dumps({
            'type': 'error',
            'message': f'语音识别服务错误: {str(error)}'
        }))
    
    def on_close(self, ws, close_status_code, close_msg):
        """远端websocket关闭处理"""
        print("与讯飞语音识别服务连接关闭")
        self.ws_cnt.send(text_data=json.dumps({
            'type': 'end',
            'message': '语音识别服务结束'
        }))
        
    def send_audio(self, audio_data, is_end=False):
        """向远端发送音频数据"""
        if not self.remote_ws:
            print("远端连接未建立 发送失败")
            return

        print(f"发送音频数据: audio_data length:{len(audio_data)}, is_end: {is_end} status:{self.audio_status}")
        try:
            if self.audio_status == 0:  # 第一帧
                frame_data = {
                    "common": self.CommonArgs,
                    "business": self.BusinessArgs,
                    "data": {
                        "status": 0,
                        "format": "audio/L16;rate=8000",
                        "audio": audio_data,
                        "encoding": "raw"
                    }
                }
                self.audio_status = 1
            elif is_end:  # 最后一帧
                frame_data = {
                    "data": {
                        "status": 2,
                        "format": "audio/L16;rate=8000",
                        "audio": audio_data,
                        "encoding": "raw"
                    }
                }
                self.audio_status = 2
            else:  # 中间帧
                frame_data = {
                    "data": {
                        "status": 1,
                        "format": "audio/L16;rate=8000",
                        "audio": audio_data,
                        "encoding": "raw"
                    }
                }
            
            self.remote_ws.send(json.dumps(frame_data))
            
        except Exception as e:
            print(f"发送音频数据到远端失败: {e}")
    
    def send_binary_audio(self, audio_bytes, is_end=False):
        """向远端发送二进制音频数据"""
        if not self.remote_ws:
            print("远端连接未建立 发送失败")
            return
            
        try:
            # 将二进制数据转换为base64
            audio_b64 = base64.b64encode(audio_bytes).decode('utf-8')
            self.send_audio(str(audio_b64), is_end)
            
        except Exception as e:
            print(f"发送二进制音频数据失败: {e}")

    