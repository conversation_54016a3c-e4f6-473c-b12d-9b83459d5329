#!/bin/bash

# PostgreSQL 创建数据库和用户脚本
# 用途：创建名为 wms_db 的数据库，用户名为 pst_wms，密码为 flyso

# 配置信息
DB_NAME="wms_db"
DB_USER="pst_wms"
DB_PASSWORD="flyso"
PG_HOST="localhost"
PG_PORT="5432"
PG_SUPERUSER="flyso_wms"  # 需要有创建数据库权限的超级用户

# 检查 psql 命令是否存在
if ! command -v psql &> /dev/null; then
    echo "错误：未找到 psql 命令。请确保已安装 PostgreSQL 客户端。"
    exit 1
fi

# 检查是否能连接到 PostgreSQL
echo "正在尝试连接到 PostgreSQL 服务器..."
if ! psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "postgres" -c "\q" 2>/dev/null; then
    echo "错误：无法连接到 PostgreSQL 服务器。请检查以下内容："
    echo "- 服务器是否正在运行"
    echo "- 主机 ($PG_HOST) 和端口 ($PG_PORT) 是否正确"
    echo "- 用户 ($PG_SUPERUSER) 是否有访问权限"
    exit 1
fi

echo "成功连接到 PostgreSQL 服务器。"

# 创建用户（如果不存在）
echo "检查用户 $DB_USER 是否存在..."
if ! psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "postgres" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'" | grep -q 1; then
    echo "创建用户 $DB_USER..."
    psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "postgres" -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
    echo "用户 $DB_USER 创建成功。"
else
    echo "用户 $DB_USER 已存在，跳过创建。"
fi

# 创建数据库（如果不存在）
echo "检查数据库 $DB_NAME 是否存在..."
if ! psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "postgres" -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'" | grep -q 1; then
    echo "创建数据库 $DB_NAME..."
    psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "postgres" -c "CREATE DATABASE $DB_NAME OWNER $DB_USER ENCODING 'UTF8';"
    
    # 授予用户对数据库的全部权限
    echo "授予用户 $DB_USER 对数据库 $DB_NAME 的全部权限..."
    psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
    
    # 授予用户对 public schema 的全部权限
    echo "授予用户 $DB_USER 对 public schema 的全部权限..."
    psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_SUPERUSER" -d "$DB_NAME" -c "GRANT ALL ON SCHEMA public TO $DB_USER;"
    
    echo "数据库 $DB_NAME 创建成功。"
else
    echo "数据库 $DB_NAME 已存在，跳过创建。"
fi

echo "====================================="
echo "操作完成！"
echo "数据库信息："
echo "- 数据库名：$DB_NAME"
echo "- 用户名：$DB_USER"
echo "- 密码：$DB_PASSWORD"
echo "- 主机：$PG_HOST"
echo "- 端口：$PG_PORT"
echo "====================================="