


from decimal import Decimal
from WmsCore.models import AccountsPayable, AccountsReceivable, Supplier, ZTUser, Customer
from WmsCore.utils.counter import Counter

class AccountsAdmin:
    #创建应付账单
    @staticmethod
    def create_payable(supplier: Supplier, order_amount: Decimal, payable_amount: Decimal, remaining_amount: Decimal, handler: <PERSON><PERSON><PERSON>, source_type: str, source_order_id:str, remark:str,customer:Customer, **foreign_order):
        no = Counter.SysDayCounter("AccountsPayable");
        payable_obj = AccountsPayable.objects.create(
            payable_no=no,
            source_order_no=source_order_id,
            source_type=source_type,
            handler = handler,
            payment_status = AccountsPayable.PaymentStatusChoices.UNPAID,
            supplier = supplier,
            payable_amount = payable_amount,
            order_amount = order_amount,
            remaining_amount = remaining_amount,
            remark = remark,
            customer=customer,
            **foreign_order
        )
        return payable_obj

    #创建应收账单
    @staticmethod
    def create_receivable(customer, order_amount: Decimal, receivable_amount: Decimal, remaining_amount: Decimal, handler: <PERSON><PERSON><PERSON>, source_type: str, source_order_id:str, remark:str, **foreign_order):
        no = Counter.SysDayCounter("AccountsReceivable");
        receivable_obj = AccountsReceivable.objects.create(
            receivable_no=no,
            source_order_no=source_order_id,
            source_type=source_type,
            handler = handler,
            payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID,
            customer = customer,
            receivable_amount = receivable_amount,
            order_amount = order_amount,
            remaining_amount = remaining_amount,
            remark = remark,
            **foreign_order
        )
        return receivable_obj;


    pass