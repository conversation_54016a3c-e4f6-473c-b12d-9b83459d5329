# Django项目编码规范

## 1. 总则
- 本规范基于PEP 8和Django官方文档制定，旨在保证代码一致性和可维护性
- 所有Python代码必须符合PEP 8标准，Django特定部分遵循本规范补充条款
- 使用4个空格缩进，禁用Tab键
- 每行代码不超过120个字符
- 文件编码统一为UTF-8

## 2. 命名规范
### 2.1 通用命名
- 类名：使用CamelCase，如`SalesOrderView`
- 函数/方法名：使用snake_case，如`perform_create`
- 变量名：使用snake_case，如`order_type`
- 常量：使用全大写SNAKE_CASE，如`MAX_ORDER_ITEMS`
- 模块/文件名：使用snake_case，如`sales_order.py`

### 2.2 Django特定命名
- 模型类：使用单数形式CamelCase，如`Item`而非`Items`
- 模型外键：使用相关模型名小写，如`customer = models.ForeignKey(Customer)`
- 迁移文件：使用Django自动生成的命名，不手动修改
- URL名称：使用snake_case并添加应用前缀，如`wms_core:sales_order_list`

## 3. 模型设计
- 所有模型必须继承自`BaseModel`（如项目中已定义）或`models.Model`
- 主键使用默认自增ID，无需显式定义
- 字段定义顺序：主键 → 核心业务字段 → 关系字段 → 时间戳字段
- 必须为每个字段添加`verbose_name`
- 字符串字段必须指定`max_length`
- 合理使用`choices`枚举，如订单状态：

```python
  class OrderStatusChoices(models.TextChoices):
      PENDING = 'pending', _('预定单')
      COMPLETED = 'completed', _('全部出库')
```

- 时间字段统一使用`auto_now_add`和`auto_now`：

```python
  create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
  update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
```

## 4. 视图与控制器
- API视图优先使用Django REST Framework的`APIView`或视图集
- 自定义视图基类放在`common/APIViewSafe.py`
- 业务逻辑应放在`control`目录下，遵循单一职责原则
- 视图方法中避免复杂逻辑，超过10行的业务逻辑应抽象为服务层函数
- 权限控制使用`permission_classes`装饰器或类属性
- 分页：列表接口必须实现分页，使用`PageNumberPagination`
- 过滤：使用`django-filter`实现复杂查询

## 5. 序列化器
- 序列化器文件放在对应应用的`serializers.py`
- 为每个模型创建基础序列化器和详细序列化器，如`ItemSerializer`和`ItemDetailSerializer`
- 验证逻辑应在`validate_<field>`方法或`validate`方法中实现
- 嵌套序列化使用`depth`参数或显式定义嵌套序列化器
- 只读字段使用`read_only_fields`元属性

## 6. 异常处理
- 所有业务异常必须继承`BusinessException`
- 异常错误码使用`RetCode`枚举，如`RetCode.UNKOWN_ERROR`
- 异常消息使用国际化`_()`函数，如`_('库存不足')`
- 不在视图中直接捕获异常，统一由`custom_exception_handler`处理
- 数据库操作必须使用事务`@transaction.atomic`确保数据一致性

## 7. URL配置
- 主URL配置在项目根目录`urls.py`，应用URL在各自应用的`urls.py`
- URL路径使用kebab-case，如`/sales-orders/`而非`/sales_orders/`
- 版本控制：API路径包含版本号，如`/api/v1/sales-orders/`
- 使用`include`函数拆分URL配置，避免主URL文件过大
- URL名称必须全局唯一，建议格式：`{app_name}:{view_name}`

## 8. 查询优化
- 列表查询使用`select_related`和`prefetch_related`减少数据库查询
- 避免在循环中执行数据库查询
- 使用`count()`、`exists()`代替`len()`进行数量判断
- 批量操作使用`bulk_create`、`bulk_update`提高性能
- 复杂查询使用`Q`对象和`F`表达式

## 9. 注释规范
- 类、方法、函数必须添加文档字符串，使用Google风格
- 复杂逻辑必须添加行内注释
- 注释应说明"为什么做"而非"做了什么"
- 临时注释使用`# TODO:`标记，并指明负责人
- 示例：

```python
  def perform_create(self, serializer):
      """创建销售订单并处理库存

      根据订单类型执行不同逻辑：
      - 零售订单：检查并扣减库存
      - 批发订单：更新物品需求量
      """
```

## 10. 测试要求
- 所有模型和业务逻辑必须编写单元测试
- API接口必须编写集成测试
- 测试文件放在应用的`tests.py`或`tests`目录
- 使用`pytest`作为测试框架
- 测试覆盖率目标：核心业务逻辑≥80%

## 11. 其他规范
- 导入顺序：标准库 → 第三方库 → 项目内部模块，使用空行分隔
- 避免使用`*`通配符导入
- 魔术方法（如`__str__`）必须实现
- 数据库迁移文件必须纳入版本控制
- 敏感配置使用环境变量，不硬编码在代码中