

# 默认使用中文回答

# 如果需要编辑代码，打印信息使用中文，注释信息使用中文，错误信息使用中文

## 编程规范
- 使用类型提示
- 函数和类添加中文注释
- 错误处理要完善

## 项目介绍
项目是一个用django实现的多租户系统，公共模块在AccessGateway中实现，common存放一些通用的实现功能，wms是 django 的初始模块，WmsCore 是多租户的核心实现，租户的具体功能逻辑在这里实现
项目使用的主要组件是 django-tenants和djangorestframework 

## 项目结构

项目目录/
├── AccessGateway/           # 公共模块功能，提供登录及用户、企业信息、多租户(账套)信息的分配
├── common             # 通用功能模块
├── requirements.txt # 依赖包
├── config          # 从 settings.py 文件中抽离出来的 yaml 配置文件
├── logs          # 日志输出文件
├── wms          # django主模块文件夹
├── WmsCore          # 多租户系统中，租户具体功能逻辑实现
    ├── admin          # 租户功能系统中部分全局资源管理模块
    ├── control          # 实现跟客户端交互的 DRF相关处理逻辑
    ├── model          # 定义 django 模型 model 文件，也可以在 models.py 中定义
    ├── utils          # 一些公共引用工具
    ├── models.py          # django 的模型定义文件
    ├── serializers.py          # 实现DRF功能的rest_framework，所需要的 serializers 定义，也可以直接在 control 具体模块中实现
└── GEMINI.md        # 本配置文件