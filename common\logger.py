import logging
import json
import logging.handlers
import os
import time
from pathlib import Path
from threading import local
thread_local = local()

class LevelFilter(logging.Filter):
    def __init__(self, level):
        self.level = level

    def filter(self, record):
        return record.levelno == self.level

def set_current_user(user):
    thread_local.user = user

class OperInfoFilter(logging.Filter):
    def filter(self, record):
        request = getattr(thread_local, 'request', None)
        if request:
            user = getattr(request, 'user_id', getattr(thread_local, 'user', None))
            enterprise = getattr(request, 'ledger_enterprise', None)
            ledger = getattr(request, 'tenant_name', None)
            oper_info = ''
            #print(f"record: {record.levelname}, user:{user} enterprise:{enterprise} ledger:{ledger}, request:{request} user:{request.user_id}")
            if ledger:
                oper_info = f"@{ledger}"
            if enterprise:
                oper_info = f'user:{user}({enterprise.id}){oper_info}|'
            elif user:
                oper_info = f'user:{user}{oper_info}|'
        else:
            oper_info = ''
        record.operator = oper_info

        return True

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

# 创建日志目录
LOG_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 获取当前日期
CURRENT_DATE = time.strftime('%Y%m%d', time.localtime(time.time()))

# 配置日志
logger = logging.getLogger('wms')
logger.setLevel(logging.DEBUG)

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)

# format  loglevel|time|message
time_rotating_handler = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(LOG_DIR, f'app-{CURRENT_DATE}.log'),
    encoding='utf-8',
    when='D',
    interval=1,
    backupCount=2
);
# 设置后缀格式（会自动替换 %Y%m%d 为实际日期）
time_rotating_handler.suffix = '%Y%m%d.log'
time_rotating_handler.extMatch = r'^\d{8}\.log$'  # 正则匹配日期格式
info_formatter = logging.Formatter('%(levelname)s|%(operator)s%(asctime)s|%(message)s')
time_rotating_handler.setFormatter(info_formatter)
time_rotating_handler.addFilter(OperInfoFilter())
time_rotating_handler.setLevel(logging.INFO)

# format  loglevel|time|filename|function:line|message
debug_handler = logging.handlers.RotatingFileHandler(
    filename=os.path.join(LOG_DIR, f'debug-{CURRENT_DATE}.log'),
    encoding='utf-8',
    maxBytes=1024*1024*100, # 100M
    backupCount=5
)
debug_formatter = logging.Formatter('%(levelname)s|%(asctime)s|%(filename)s|%(funcName)s:%(lineno)d|%(message)s')
debug_handler.setFormatter(debug_formatter)
debug_handler.addFilter(LevelFilter(logging.DEBUG))
debug_handler.setLevel(logging.DEBUG)

# # 文件处理器
# file_handler = logging.FileHandler(
#     filename=os.path.join(LOG_DIR, f'app-{CURRENT_DATE}.log'),
#     encoding='utf-8'
# )
# file_handler.setLevel(logging.INFO)

# # 错误文件处理器
# error_handler = logging.FileHandler(
#     filename=os.path.join(LOG_DIR, f'error-{CURRENT_DATE}.log'),
#     encoding='utf-8'
# )
# error_handler.setLevel(logging.ERROR)

# 设置格式
#formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] - %(message)s')
console_handler.setFormatter(debug_formatter)
#error_handler.setFormatter(formatter)
#file_handler.setFormatter(formatter)

# 添加处理器
logger.addHandler(console_handler)
#logger.addHandler(error_handler)
#logger.addHandler(file_handler)
logger.addHandler(time_rotating_handler)
logger.addHandler(debug_handler)


def debug(message):
    """记录调试信息"""
    logger.debug(message)

def info(message):
    """记录普通信息"""
    logger.info(message)

def warning(message):
    """记录警告信息"""
    logger.warning(message)

def error(message, exc_info=None):
    """记录错误信息"""
    logger.error(message, exc_info=exc_info)

def critical(message, exc_info=None):
    """记录严重错误信息"""
    logger.critical(message, exc_info=exc_info)

def log_exception(message, exc_info=True, **kwargs):
    """记录带上下文的异常信息"""
    if kwargs:
        context_str = json.dumps(kwargs, ensure_ascii=False)
        message = f"{message} - Context: {context_str}"
    logger.error(message, exc_info=exc_info) 

#logger.debug("test debug")
#logger.info("test info")