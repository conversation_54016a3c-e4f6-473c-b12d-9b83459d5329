# import requests
# import os
#
# item_id = 2  # 替换为实际物品ID
# # 新的图片上传API地址
# base_url = "http://192.168.1.211:8000/ztx/3vxUJFifK5/images/"
#
# headers = {
#     'Authorization': 'Bearer o8mjoeBKU7nRJwyiU3hjfMog'
# }
#
# # 真实图片路径
# real_image_path = r"C:\Users\<USER>\Desktop\2408.jpg_wh860.jpg"
#
# # 上传图片
# if not os.path.exists(real_image_path):
#     print("图片文件不存在，请检查路径！")
#     exit(1)
#
# with open(real_image_path, 'rb') as img:
#     files = {
#         'image': img,
#     }
#     response = requests.post(base_url, headers=headers, files=files)
#     print("上传图片：", response.status_code)
#     try:
#         resp_json = response.json()
#         if resp_json.get('code') == 0:
#             print("上传成功！")
#             print("MD5:", resp_json['data'].get('md5'))
#             print("图片文件名:", resp_json['data'].get('image'))
#             filename = resp_json['data'].get('image')
#             # 假设 schema_name 为 'public'，实际使用时需要根据情况修改
#             schema_name = 'public'
#             img_url = f"http://192.168.1.211:8000/media/{schema_name}/images/img/{filename[0:2]}/{filename[2:4]}/{filename}"
#             thumb_url = f"http://192.168.1.211:8000/media/{schema_name}/images/thumb/{filename[0:2]}/{filename[2:4]}/{filename}"
#             print("图片URL:", img_url)
#             print("缩略图URL:", thumb_url)
#             print(resp_json)
#         else:
#             print("上传失败：", resp_json.get('msg'))
#             print("详细信息：", resp_json.get('data'))
#     except Exception as e:
#         print("解析返回内容失败：", str(e))
#         print(response.text)
#
