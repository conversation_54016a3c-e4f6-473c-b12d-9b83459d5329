# 销售退货重构总结

## 重构目标
明确区分零售退货和批发退货，各自有不同的更新和创建方法，在总的create和update方法中通过订单类型选择不同的处理方法。

## 重构后的架构

### 1. 统一入口方法

#### create 方法
```python
def create(self, request, *args, **kwargs):
    # 根据订单类型选择不同的创建方法
    order_type = serializer.validated_data.get('order_type', 'wholesale')
    if order_type == 'retail':
        return self.create_retail_return(serializer, request)
    else:
        return self.create_wholesale_return(serializer, request)
```

#### update 方法
```python
def update(self, request, pk=None, **kwargs):
    # 根据订单类型选择不同的更新方法
    order_type = sales_return.order_type or 'wholesale'
    if order_type == 'retail':
        # 零售退货不支持草稿状态
        raise WmsException("零售退货不支持草稿状态，无法更新")
    else:
        return self.update_wholesale_return(serializer, request)
```

### 2. 零售退货处理方法

#### create_retail_return
- **特点**: 直接创建正式单据，不支持草稿
- **验证**: 必须指定付款方式
- **处理**: 立即创建应付账本记录，设置为已付款状态
- **库存**: 立即处理库存入库

#### 零售退货创建流程
```
1. 验证必填字段（仓库、付款方式）
2. 验证退货数量和物品
3. 创建正式退货单（is_draft=False）
4. 创建应付账本记录（立即付款）
5. 创建退货物品并处理库存
6. 返回完整数据
```

### 3. 批发退货处理方法

#### create_wholesale_return
- **特点**: 支持草稿和正式两种状态
- **草稿**: 不创建应付账本，不处理库存
- **正式**: 创建应付账本记录，处理库存

#### 批发退货创建流程

**草稿状态**:
```
1. 验证基本字段（仓库）
2. 创建草稿退货单（is_draft=True）
3. 创建退货物品（不处理库存）
4. 返回退货单数据
```

**正式状态**:
```
1. 验证必填字段（仓库、付款方式）
2. 创建正式退货单（is_draft=False）
3. 创建应付账本记录（可能有账期）
4. 创建退货物品并处理库存
5. 返回完整数据
```

#### update_wholesale_return
- **草稿更新**: 只更新退货单和物品信息
- **草稿转正式**: 创建应付账本记录并处理库存

### 4. 业务规则差异

| 特性 | 零售退货 | 批发退货 |
|------|----------|----------|
| 草稿支持 | ❌ 不支持 | ✅ 支持 |
| 付款方式 | 必填 | 正式单必填 |
| 付款状态 | 立即付款(PAID) | 可能有账期(UNPAID) |
| 库存处理 | 立即处理 | 正式单才处理 |
| 应付金额 | remaining_amount=0 | remaining_amount=payable_amount |

### 5. 方法映射表

| 操作 | 零售退货 | 批发退货 |
|------|----------|----------|
| 创建正式单 | `retail_return_create` | `wholesale_return_create_formal` |
| 创建草稿单 | ❌ 不支持 | `wholesale_return_create_draft` |
| 更新草稿 | ❌ 不支持 | `wholesale_return_update_draft` |
| 草稿转正式 | ❌ 不支持 | `convert_wholesale_draft_to_formal` |

### 6. API 返回数据结构

#### 零售退货创建/更新
```json
{
    "sales_return": {...},
    "sales_return_items": [...],
    "accounts_payable": {...}  // 总是存在
}
```

#### 批发退货草稿创建/更新
```json
{
    "sales_return": {...},
    "sales_return_items": [...]
    // 无 accounts_payable
}
```

#### 批发退货正式创建/草稿转正式
```json
{
    "sales_return": {...},
    "sales_return_items": [...],
    "accounts_payable": {...}  // 正式单才有
}
```

### 7. 错误处理

#### 零售退货特有错误
- "零售退货不支持草稿状态"
- "零售退货必须指定付款方式"
- "零售退货不支持草稿状态，无法更新"

#### 批发退货特有错误
- "批发退货正式单必须指定付款方式"
- "转为正式退货单必须指定付款方式"

### 8. 数据库设计

#### SalesReturn 模型字段
- `order_type`: 订单类型（'retail' | 'wholesale'）
- `is_draft`: 是否草稿状态
- 其他业务字段保持不变

#### AccountsPayable 关联
- 零售退货: 立即创建，状态为 PAID
- 批发退货: 正式单才创建，状态为 UNPAID

### 9. 前端适配建议

#### 创建退货单
```javascript
// 零售退货
{
    order_type: 'retail',
    is_draft: false,  // 固定为false
    payment_method: required,  // 必填
    // 其他字段
}

// 批发退货草稿
{
    order_type: 'wholesale',
    is_draft: true,
    payment_method: optional,  // 可选
    // 其他字段
}

// 批发退货正式
{
    order_type: 'wholesale',
    is_draft: false,
    payment_method: required,  // 必填
    // 其他字段
}
```

#### 更新退货单
- 零售退货: 不支持更新操作
- 批发退货: 只能更新草稿状态的单据

### 10. 测试用例建议

1. **零售退货测试**:
   - 创建零售退货（必须正式）
   - 验证应付账本立即创建且为已付款状态
   - 验证库存立即处理

2. **批发退货测试**:
   - 创建批发退货草稿
   - 更新批发退货草稿
   - 草稿转正式
   - 直接创建批发退货正式单

3. **错误处理测试**:
   - 零售退货创建草稿（应报错）
   - 零售退货更新（应报错）
   - 批发退货正式单缺少付款方式（应报错）

## 总结

重构后的销售退货模块实现了零售退货和批发退货的明确区分，各自有独立的处理逻辑，通过统一的入口方法根据订单类型进行分发。这样的设计提高了代码的可维护性和业务逻辑的清晰度。
