
import django
from datetime import date, datetime


class ModelData:
    def __init__(self, save_to_db = True):
        self.model_data = {}
        self.save_to_db = save_to_db

    def add_model_data(self, *args):
        for value in args:
            arr = self.model_data.get(value.__class__.__name__, [])
            arr.append(value)
            self.model_data[value.__class__.__name__] = arr
            if self.save_to_db:
                value.save()

    def get_model_data_by_id( self, model_name, id):
        arr = self.model_data.get(model_name, [])
        for item in arr:
            if item.id == id:
                return item
        return None

    def save_model_data_to_db(self):
        # for key, value in self.model_data.items():
        #     for item in value:
        #         print(f"save {item}")
        #         item.save()
        pass

    def save_model_data_to_sqlfile(self, file_name:str):
        with open(file_name, 'w') as f:
            for key, value in self.model_data.items():
                for item in value:
                    f.write(self.generate_sql_for_instance(item))

    def generate_sql_for_instance(self, instance):
        """
        为给定的模型实例生成INSERT语句
        """
        # 获取模型类
        model = type(instance)
        # 获取表名
        table_name = model._meta.db_table

        # 获取字段列表（不包括多对多字段，因为它们存储在另外的表中）
        fields = []
        values = []
        for field in model._meta.fields:
            # 获取字段名
            column = field.column
            # 获取字段值
            value = getattr(instance, field.attname)

            # 处理特殊值：None, 字符串转义，日期时间等
            if value is None:
                value = 'NULL'
            elif isinstance(value, (int, float)):
                value = str(value)
            elif isinstance(value, (bool)):
                value = "TRUE" if value else "FALSE"
            elif isinstance(value, (str)):
                # 转义单引号
                value = "'" + value.replace("'", "''") + "'"
            elif isinstance(value, (django.utils.timezone.datetime)):
                # 将时区转换为字符串
                # 注意：数据库格式可能要求，我们使用ISO格式
                # 转换为数据库识别的格式：YYYY-MM-DD HH:MM:SS[.ffffff]
                value = "'" + value.strftime("%Y-%m-%d %H:%M:%S") + "'"
            elif isinstance(value, date):
                value = "'" + value.strftime("%Y-%m-%d") + "'"
            else:
                # 其他类型，如二进制？这里简化为字符串，但可能不安全
                value = "'" + str(value).replace("'", "''") + "'"

            fields.append(column)
            values.append(value)

        # 构建SQL
        sql = "INSERT INTO {table} ({columns}) VALUES ({values});\n".format(
            table=table_name,
            columns=', '.join(fields),
            values=', '.join(values)
        )
        return sql