import hashlib
import json
from functools import wraps
import collections.abc
from django.core.files.uploadedfile import UploadedFile, InMemoryUploadedFile

from rest_framework import status

from AccessGateway.control.auth import make_response
from common.db.Redis import RedisUtil


def prevent_duplicate_submission(timeout=5, methods=['POST', 'PUT', 'PATCH'], exclude_fields=None):
    """
    防止重复提交的装饰器

    :param timeout: 锁超时时间（秒）
    :param methods: 需要防重的请求方法列表
    :param exclude_fields: 请求数据中需要排除的字段列表
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            # 只对指定的请求方法进行防重处理
            if request.method not in methods:
                return func(self, request, *args, **kwargs)

            # 生成请求的唯一标识
            request_hash = _generate_request_hash(request, exclude_fields)
            lock_key = f"duplicate_submission_{request_hash}"

            # 获取用户ID（用于验证锁的所有者）
            user_id = request.user_id
            lock_value = f"user:{user_id}"
            stored_value = RedisUtil.get(lock_key)
            if stored_value == lock_value:
                return make_response(
                    code=1,
                    msg='请勿重复提交',
                    status=status.HTTP_409_CONFLICT
                )
            else:
                RedisUtil.set(lock_key, lock_value, timeout=timeout)


            try:
                # 执行原函数
                response = func(self, request, *args, **kwargs)
                return response
            except Exception as e:
                # 发生异常时释放锁
                if RedisUtil.get(lock_key) == lock_value:
                    RedisUtil.delete(lock_key)
                raise e

        return wrapper

    return decorator


def _filter_non_json(obj):
    if isinstance(obj, (UploadedFile, InMemoryUploadedFile)):
        return f"<file:{getattr(obj, 'name', 'unknown')}>"
    elif isinstance(obj, bytes):
        return "<bytes>"
    elif isinstance(obj, dict):
        return {k: _filter_non_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_filter_non_json(v) for v in obj]
    else:
        return obj

def _generate_request_hash(request, exclude_fields=None):
    """生成请求的唯一标识，排除指定字段"""
    exclude_fields = exclude_fields or []

    # 获取请求数据
    if request.method in ['POST', 'PUT', 'PATCH']:
        data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
        # 排除指定字段
        for field in exclude_fields:
            data.pop(field, None)
    else:
        data = dict(request.query_params)

    # 新增：过滤不可序列化对象
    data = _filter_non_json(data)

    # 获取用户ID
    user_id = request.user_id if hasattr(request, 'user_id') else 'anonymous'

    # 生成hash
    content = f"{request.method}:{request.path}:{user_id}:{json.dumps(data, sort_keys=True)}"
    return hashlib.sha256(content.encode()).hexdigest()  # 使用更安全的哈希算法