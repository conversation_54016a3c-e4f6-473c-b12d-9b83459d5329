import random
import string

from django.http import HttpResponse, JsonResponse
# 在视图文件开头添加以下导入
from django.db import transaction

#from AccessGateway.permissions import IsPlatformAccount
#from common.auth.jwt_auth import JWTAuthentication
from AccessGateway.models import Enterprise
from common.APIViewSafe import APIViewSafe
# 导入新的日志工具
from common.logger import logger as log
from WmsCore.utils.counter import Counter
from WmsCore.utils.jwt_auth import JWTWmsCoreAuthentication
from WmsCore.utils.permission import IsZTUser, IsLedgerPermission

# Create your views here.
class Resp:
    code = 0;
    msg = "";
    data = {};

class IndexAPI(APIViewSafe):
    authentication_classes = [JWTWmsCoreAuthentication]
    permission_classes = [IsZTUser, IsLedgerPermission]

    def get(self, request):
        return JsonResponse({
            'message': 'GET method is supported'
        })

    @transaction.atomic
    def post(self, request):
        #from WmsCore.data.make_datas import create_initial_data, make_default_data_to_db
        #industry = request.data.get('industry', 'wujin')

        #create_initial_data(industry)
        #tenant = "9ADnRPe5pt";
        #make_default_data_to_db(tenant);
        from AccessGateway.control.tenant import TenantMgr
        TenantMgr.RequestToCheckTenantCapacity();
        return JsonResponse({
            'message': 'POST method is supported',
        })

class Model2XlsAPI(APIViewSafe):
    authentication_classes = [];
    permission_classes = [];
    def get(self, request):
        from AccessGateway.models import Ledger;
        from django_multitenant.utils import set_current_tenant;
        ledger = Ledger.objects.all().first();
        #随便设置一个吧，反正内部测试用的
        set_current_tenant(ledger);

        from WmsCore.utils.serializer_details import SerializerDetailMgr
        model = request.query_params.get('model', "")
        if model == "":
            model_names = SerializerDetailMgr.get_serializer_classes()
            return JsonResponse({
                'message': 'model is required',
                'model_names': model_names
            })
        gen_test = request.query_params.get('gen_test', "0")
        log.debug(f"model: {model} gen_test: {gen_test}")

        if gen_test == "1":
            test_data = SerializerDetailMgr.get_test_data(model)
            return JsonResponse({
                'message': 'GET method is supported',
                'test_data': test_data
            })

        details = SerializerDetailMgr.get_serializer_details(model)
        return JsonResponse({
            'message': 'GET method is supported',
            'details': details
        })

def index(request ):
    log.info("访问WmsCore首页")
    resp = Resp();
    resp.code = 0;
    resp.msg = "Hello, world. You're at the WmsCore index.";
    return JsonResponse(resp.__dict__);

# def get_tenant_list(request):
#     log.info("获取租户列表")
#     return HttpResponse(f"Hello, world. You're at the WmsCore index.")

# def AllocTenantDB( pre_db_label = None):
#     # 根据企业名称，或者前置 db，分配新的数据库
#     if pre_db_label is None:
#         #TODO
#         pre_db_label = "最新的 lab 就好";
#         #else 旧的存在，就先用的久的，以后再检测负荷
#     log.debug(f"分配数据库标签: {pre_db_label}")
#     return pre_db_label

# def AllocTenantUniqName():
#     # 生产唯一id，长度为10，由数字和字母组成,检查是否存在
#     tenant_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
#     log.debug(f"生成租户ID: {tenant_id}")
#     while Domain.objects.filter(domain=tenant_id).exists():
#         tenant_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
#         log.debug(f"重新生成租户ID: {tenant_id}")
#     return tenant_id

# def AllocTenantModel(schema_name, db_label):
#     #TODO
#     # 给指定 label 的 db 中新建schema 并创建相应的表
#     # 新建一个临时租户，并创建schema,不保存
#     log.info(f"分配租户模型: schema_name={schema_name}, db_label={db_label}")
#     tmp_tenant = TenantLedger(schema_name=schema_name)
#     tmp_tenant.create_schema()
#     log.info(f"创建schema成功: {schema_name}")
#     #with connection.schema_context(tmp_tenant):
#     #    pass
#     pass

# def CreateTenant(request):
#     log.info("创建租户请求")
#     schema_name = request.data.get('schema_name')
#     log.info(f"创建租户: schema_name={schema_name}")
    
#     # 生产唯一id，长度为10，由数字和字母组成,检查是否存在
#     # 要先取用户对应公司已分配的 db_label 如果没有就空着新分配
#     db_label = None
#     db_label = AllocTenantDB(db_label)
#     tenant_id = AllocTenantUniqName()

#     # create your first real tenant
#     tenant = TenantLedger(schema_name=tenant_id,
#                 name='Fonzy Tenant',
#                 db_label=db_label)
#     tenant.auto_create_schema = False
#     tenant.save() # migrate_schemas automatically called, your tenant is ready to be used!
#     log.info(f"创建租户成功: id={tenant.id}, name={tenant.name}")
    
#     AllocTenantModel(tenant_id, db_label)
#     tenant.alloc_model_flag = True
#     tenant.save()

#     # Add one or more domains for the tenant
#     domain = Domain()
#     domain.domain = tenant_id
#     domain.tenant = tenant
#     domain.is_primary = True
#     domain.save()
#     log.info(f"创建域名成功: domain={tenant_id}")
    
# # CreateTenant('tenant1')



class TenantAPI(APIViewSafe):
    """租户API"""
    #authentication_classes = [JWTAuthentication]
    #permission_classes = [IsPlatformAccount]
    authentication_classes = [JWTWmsCoreAuthentication]
    permission_classes = [IsZTUser, IsLedgerPermission]

    def post(self, request, *args, **kwargs):
        try:
            # 先创建企业
            enterprise = Enterprise.objects.create(
                enterprise_name='测试企业',
                enterprise_type='测试类型'
            )
            
            # 创建公司并关联企业
            company = Enterprise.objects.create(
                name='Company1',
                enterprise=enterprise
            )
            
            return JsonResponse({
                'message': '创建成功',
                'company': company.enterprise_name,
                'enterprise': enterprise.enterprise_name
            })
        except Exception as e:
            log.error(f"创建失败: {str(e)}")
            return JsonResponse({
                'error': str(e)
            }, status=500)

    def get(self, request, *args, **kwargs):
        return JsonResponse({
            'message': 'GET method is supported'
        })

