# 从 Django 配置文件中导入设置
from django.conf import settings
# 从 Django 核心异常模块中导入禁止访问主机异常
from django.core.exceptions import DisallowedHost
# 从 Django 数据库模块中导入数据库连接对象
from django.db import connection
# 从 Django HTTP 模块中导入 404 错误响应
from django.http import Http404
# 从 Django URL 模块中导入设置 URL 配置的函数
from django.urls import set_urlconf
# 从 Django 工具模块中导入动态导入模块的函数
from django.utils.module_loading import import_string
# 从 Django 中间件工具模块中导入中间件基类
from django.utils.deprecation import MiddlewareMixin
from common.logger import logger as log
from AccessGateway.models import Ledger
from django_multitenant.utils import set_current_tenant, unset_current_tenant

# 从 django-tenants 工具模块中导入相关函数
# from django_tenants.utils import remove_www, get_public_schema_name, get_tenant_types, \
#     has_multi_type_tenants, get_tenant_domain_model, get_public_schema_urlconf


# class TenantMainMiddlewareX(MiddlewareMixin):
#     # 定义租户未找到时抛出的异常类型，默认为 404 错误
#     TENANT_NOT_FOUND_EXCEPTION = Http404
#     """
#     此中间件应放置在中间件堆栈的最顶部。
#     它会根据请求的主机名选择合适的数据库模式。在各种情况下都可能失败，但这总比损坏或泄露数据要好。
#     """

#     @staticmethod
#     def get_path_info(request) -> tuple[str, str | None]:
#         """
#         获取请求中的 URL 路径，取出路径中的第一个字段。
#         如果路径中只有一个字段，则返回空字符串和 None。
#         :param request: 请求对象
#         :return: 路径的第一个字段和第二个字段（如果存在）
#         """
#         # 将请求的路径信息按斜杠分割成列表
#         path_list = request.path_info.split('/');
#         #log.debug(f"path_list: {path_list} path_info: {request.path_info}");
#         # 忽略列表中的第一个元素，因为它是根路径
#         if len(path_list) > 2:
#             return path_list[1], path_list[2];
#         elif len(path_list) == 2:
#             return path_list[1], None;
#         else:
#             return "", None;

#     def get_tenant(self, domain_model, tenant_name):
#         """
#         根据域名模型和租户名称获取租户对象。
#         :param domain_model: 域名模型类
#         :param tenant_name: 租户名称
#         :return: 租户对象
#         """
#         # 通过域名模型查询对应的域名对象，并关联查询其租户对象
#         domain = domain_model.objects.select_related('tenant').get(domain=tenant_name)
#         return domain.tenant

#     def process_view(self, request, view_func, view_args, view_kwargs):
#         # 从 view_kwargs 中获取并处理 schema_name
#         if 'schema_name' in view_kwargs:
#             if request.tenant_name is not None and request.tenant_name != "":
#                 schema_name = view_kwargs.pop('schema_name')  # 移除参数
#                 request.schema_name = schema_name  # 存储到 request 中
#             else:
#                 log.debug(f"request.tenant_name is None but get schema_name == {view_kwargs.get('schema_name')} from view_kwargs");
#         return None  # 继续正常处理视图

#     def process_request(self, request):
#         """
#         处理每个传入的请求，根据请求路径选择合适的租户和数据库模式。
#         :param request: 请求对象
#         :return: 响应对象（如果需要）
#         """
#         # 首先将数据库连接设置为公共模式，因为租户元数据存储在公共模式中
#         connection.set_schema_to_public()
#         try:
#             # 从请求中获取路径的主要部分和租户名称
#             main_path, tenant_name = self.get_path_info(request);
#             #log.debug(f"main_path: {main_path}, tenant_name: {tenant_name}");
#             # 如果主要路径为空，则调用默认路由处理方法
#             if main_path is None or main_path == "" or main_path != settings.TENANT_PATH_PREFIX:
#                 return self.go_default_router(request);
#             # 如果是租户路径，检查租户名称是否存在
#             if tenant_name is None or tenant_name == "":
#                 return self.goto_tenant_default_view(request)
#         except DisallowedHost:
#             # 如果请求的主机名不被允许，返回 404 错误响应
#             from django.http import HttpResponseNotFound
#             return HttpResponseNotFound()

#         # 获取租户域名模型类
#         domain_model = get_tenant_domain_model()
#         try:
#             # 根据租户名称获取租户对象
#             #from AccessGateway.models import TenantLedger, Domain;
#             tenant = self.get_tenant(domain_model, tenant_name)
#             if tenant.name is None or tenant.enterprise is None:
#                 # 如果租户信息不存在，或者为分配给任何企业，则认为是一个不存在的租户，抛出异常
#                 log.debug(f"get tenant.name: {tenant.schema_name}({tenant.id}) but tenant.enterprise: {tenant.enterprise}");
#                 raise domain_model.DoesNotExist;
#             log.debug(f"get_tenant {tenant_name}  success");
#         except domain_model.DoesNotExist:
#             log.debug("domain_model.DoesNotExist");
#             # 如果租户不存在，调用租户未找到处理方法
#             default_tenant = self.no_tenant_found(request, tenant_name)
#             return default_tenant

#         # 设置租户的域名 URL
#         tenant.domain_url = tenant_name
#         # 将租户对象添加到请求对象中
#         request.ledger = tenant
#         request.ledger_enterprise = tenant.enterprise
#         request.ledger_enterprise_id = tenant.enterprise.id
#         # 将租户的企业 ID 添加到请求对象中
#         #request.company_id = tenant.enterprise.id
#         request.tenant_name = tenant_name
#         # 将数据库连接切换到该租户的模式
#         connection.set_tenant(request.ledger)
#         # 设置正确的 URL 路由配置
#         self.setup_url_routing(request)

#     def go_default_router(self, request):
#         """
#         当请求路径不符合租户路径规则时，调用此方法。
#         :param request: 请求对象
#         """
#         log.debug("go_default_router");
#         pass;

#     def goto_tenant_default_view(self, request):
#         """
#         当请求路径中没有提供租户名称时，调用此方法。
#         :param request: 请求对象
#         """
#         log.debug("goto_tenant_default_view");
#         pass;

#     def no_tenant_found(self, request, hostname):
#         """
#         当找不到对应的租户时，根据配置进行不同的处理。
#         :param request: 请求对象
#         :param hostname: 主机名
#         :return: 响应对象（如果需要）
#         """
#         """ 当找不到租户时应执行的操作。
#         如果你想覆盖默认行为，这会更方便 """
#         # 检查配置中是否设置了默认的租户未找到视图
#         if hasattr(settings, 'DEFAULT_NOT_FOUND_TENANT_VIEW'):
#             # 获取视图的路径
#             view_path = settings.DEFAULT_NOT_FOUND_TENANT_VIEW
#             # 动态导入视图函数或类
#             view = import_string(view_path)
#             if hasattr(view, 'as_view'):
#                 # 如果视图是类视图，调用 as_view 方法生成视图函数并处理请求
#                 response = view.as_view()(request)
#             else:
#                 # 如果视图是函数视图，直接调用处理请求
#                 response = view(request)
#             if hasattr(response, 'render'):
#                 # 如果响应对象有 render 方法，调用该方法进行渲染
#                 response.render()
#             return response
#         # 检查配置中是否设置了在找不到租户时显示公共模式
#         elif hasattr(settings, 'SHOW_PUBLIC_IF_NO_TENANT_FOUND') and settings.SHOW_PUBLIC_IF_NO_TENANT_FOUND:
#             # 强制使用公共模式的 URL 路由配置
#             self.setup_url_routing(request=request, force_public=True)
#         else:
#             # 抛出租户未找到的异常
#             raise self.TENANT_NOT_FOUND_EXCEPTION('No tenant for hostname "%s"' % hostname)

#     @staticmethod
#     def setup_url_routing(request, force_public=False):
#         """
#         根据租户信息设置正确的 URL 配置。
#         :param request: 请求对象
#         :param force_public: 是否强制使用公共模式的 URL 配置
#         TODO: 没有登陆的回到登陆界面，已登陆的，回到账套选择界面(直接返回账套选择界面就好了，做那边检查登陆状态)
#         """
#         # 获取公共模式的名称
#         public_schema_name = get_public_schema_name()
#         #log.debug(f"schema_name: {public_schema_name} has_multi_type_tenants: {has_multi_type_tenants()} have a public schema urlconf: { (hasattr(settings, 'PUBLIC_SCHEMA_URLCONF') and (force_public or request.tenant.schema_name == get_public_schema_name()))}");
#         # 检查是否有多种类型的租户
#         if has_multi_type_tenants():
#             # 获取所有租户类型的配置信息
#             tenant_types = get_tenant_types()
#             if (not hasattr(request, 'tenant') or
#                     ((force_public or request.tenant.schema_name == get_public_schema_name()) and
#                      'URLCONF' in tenant_types[public_schema_name])):
#                 # 如果请求对象没有租户信息，或者需要强制使用公共模式的 URL 配置，则设置为公共模式的 URL 配置
#                 request.urlconf = get_public_schema_urlconf()
#             else:
#                 # 获取当前租户的类型
#                 tenant_type = request.tenant.get_tenant_type()
#                 # 设置为该租户类型对应的 URL 配置
#                 request.urlconf = tenant_types[tenant_type]['URLCONF']
#             # 设置全局的 URL 配置
#             set_urlconf(request.urlconf)

#         else:
#             # 检查是否有公共模式的特定 URL 配置
#             if (hasattr(settings, 'PUBLIC_SCHEMA_URLCONF') and
#                     (force_public or request.tenant.schema_name == get_public_schema_name())):
#                 # 设置为公共模式的 URL 配置
#                 request.urlconf = settings.PUBLIC_SCHEMA_URLCONF


class TenantMainMiddleware(MiddlewareMixin):
    def __init__(self, get_response):
        self.get_response = get_response

    def process_view(self, request, view_func, view_args, view_kwargs):
        # 从 view_kwargs 中获取并处理 schema_name
        if 'schema_name' in view_kwargs:
            if getattr(request, 'tenant_name', None) is not None and request.tenant_name != "":
                schema_name = view_kwargs.pop('schema_name')  # 移除参数
                request.schema_name = schema_name  # 存储到 request 中
                #log.debug(f"request.schema_name: {request.schema_name}");
            else:
                log.error(f"request.tenant_name is None but get schema_name == {view_kwargs.get('schema_name')} from view_kwargs");
        return None  # 继续正常处理视图

    @staticmethod
    def get_path_info(request) -> tuple[str, str | None]:
        # 将请求的路径信息按斜杠分割成列表
        path_list = request.path_info.split('/');
        if len(path_list) > 2:
            return path_list[1], path_list[2];
        elif len(path_list) == 2:
            return path_list[1], None;
        else:
            return "", None;

    @staticmethod
    def get_tenant_id(request) -> str:
        # http header 中X-Tenant-ID参数提取
        tenant_id = request.META.get('HTTP_X_TENANT_ID', None);
        return tenant_id;

    def get_tenant(self, tenant_name):
        # 通过域名模型查询对应的域名对象，并关联查询其租户对象
        ledger = Ledger.objects.filter(ledger_name=tenant_name).first();
        return ledger;

    def get_tenant_from_request(self, request)->tuple[Ledger, str]:
        #log.debug(f"get_tenant_id: {self.get_tenant_id(request)} ");
        # 从请求中获取路径的主要部分和租户名称
        main_path, tenant_name = self.get_path_info(request);
        #log.debug(f"main_path: {main_path}, tenant_name: {tenant_name}");
        # 如果主要路径为空，则调用默认路由处理方法
        if main_path is None or main_path == "" or main_path != settings.TENANT_PATH_PREFIX:
            # default router
            return None, None;
        # 如果是租户路径，检查租户名称是否存在
        if tenant_name is None or tenant_name == "":
            # default view
            return None, None;
        tenant = self.get_tenant(tenant_name)
        if tenant is None or tenant.name is None or tenant.enterprise is None:
            # 如果租户信息不存在，或者为分配给任何企业，则认为是一个不存在的租户，抛出异常
            if tenant is None:
                log.error(f"get tenant.name: {tenant_name} but tenant is None");
            else:
                log.error(f"get tenant.name: {tenant.ledger_name}({tenant.id}) but tenant.enterprise: {tenant.enterprise}");
            raise Ledger.DoesNotExist;
        log.debug(f"get_tenant {tenant_name} ledger{tenant.id} success");
        return tenant, tenant_name;

    def __call__(self, request):
        try:
            tenant, tenant_name = self.get_tenant_from_request(request);
        except DisallowedHost:
            # 如果请求的主机名不被允许，返回 404 错误响应
            from django.http import HttpResponseNotFound
            return HttpResponseNotFound()

        if tenant is not None:
            set_current_tenant(tenant)
            request.ledger = tenant
            request.ledger_enterprise = tenant.enterprise
            request.ledger_enterprise_id = tenant.enterprise.id
            request.tenant_name = tenant_name

        response = self.get_response(request)

        if tenant is not None:
            unset_current_tenant()

        return response