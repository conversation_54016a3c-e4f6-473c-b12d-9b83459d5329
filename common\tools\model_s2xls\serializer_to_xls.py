
import json
import requests
import openpyxl
from openpyxl.styles import Font, Alignment

# 定义 URL
# 注意：请将下面的 url1 和 url2 替换为实际的 URL
url1 = "http://localhost:8000/ztx/CtVSPL9a3r/s2x/"  # 获取模型名称列表的 URL
url2_base = "http://localhost:8000/ztx/CtVSPL9a3r/s2x/"  # 获取模型字段信息的基础 URL
filename = "slizer_info.xlsx"

def get_model_names(url):
    """
    从指定的 URL 获取模型名称列表。

    Args:
        url (str): 获取模型名称列表的 URL。

    Returns:
        list: 模型名称的列表，如果请求失败则返回空列表。
    """
    try:
        response = requests.get(url)
        response.raise_for_status()  # 如果请求失败则抛出异常
        rsp = response.json();
        return rsp.get('model_names', [])
    except requests.exceptions.RequestException as e:
        print(f"获取模型名称列表失败: {e}")
        return []

def get_model_field_info(base_url, model_name):
    """
    从指定的 URL 获取单个模型的字段信息。

    Args:
        base_url (str): 获取模型字段信息的基础 URL。
        model_name (str): 模型名称。

    Returns:
        list: 模型的字段信息列表，如果请求失败则返回空列表。
    """
    try:
        response = requests.get(f"{base_url}?model={model_name}")
        response.raise_for_status()  # 如果请求失败则抛出异常
        rsp = response.json()
        return rsp.get('details', [])
    except requests.exceptions.RequestException as e:
        print(f"获取模型 '{model_name}' 的字段信息失败: {e}")
        return []

def write_to_excel(model_data, filename="model_fields_info.xlsx"):
    """
    将模型字段信息写入 Excel 文件，每个模型一个 sheet。

    Args:
        model_data (dict): 包含所有模型及其字段信息的字典。
        filename (str): 输出的 Excel 文件名。
    """
    workbook = openpyxl.Workbook()
    workbook.remove(workbook.active)  # 移除默认创建的 sheet

    field_type_map = {
        "CharField": "char",
        "IntegerField": "int",
        "DecimalField": "float",
        "BooleanField": "bool",
        "DateTimeField": "datetime",
        "DateField": "date",
        "TimeField": "time",
        "FileField": "file",
        "ChoiceField": "choice",
        "PrimaryKeyRelatedField": "key_related",
    }
    # 设置表头样式
    field_header_font = Font(bold=True)
    alignment = Alignment(horizontal='left', vertical='center')

    for model_name, model_info in model_data.items():
        alias = model_info["alias"]
        title_name = model_name.replace("Serializer", "");
        if alias != "":
            title_name = f"{alias}({title_name})"
        print(f"title_name: {title_name} len:{len(title_name)}")
        sheet = workbook.create_sheet(title=title_name)
        field_info_list = model_info["field_info"]
        
        # 写入字段信息的表头
        headers = ["字段名", "类型", "标签", "只读", "选项 (Choices)"]
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = field_header_font
            cell.alignment = alignment

        # 写入每个模型的字段信息
        if field_info_list:
            for row_idx, field_info in enumerate(field_info_list, 2):
                sheet.cell(row=row_idx, column=1, value=field_info.get("field_name")).alignment = alignment
                
                field_type = field_info.get("field_type")
                sheet.cell(row=row_idx, column=2, value=field_type_map.get(field_type, field_type)).alignment = alignment
                
                sheet.cell(row=row_idx, column=3, value=field_info.get("label")).alignment = alignment
                #sheet.cell(row=row_idx, column=4, value=field_info.get("help_text")).alignment = alignment
                sheet.cell(row=row_idx, column=4, value="是" if field_info.get("read_only") else "否").alignment = alignment

                # 如果是 ChoiceField，则写入 choices
                if "ChoiceField" in field_type and "choices" in field_info:
                    choices_js = field_info["choices"];
                    choices = {};
                    for choice in choices_js:
                        choices[choice["value"]] = choice["display_name"];
                    choices_str = json.dumps(choices, ensure_ascii=False )
                    sheet.cell(row=row_idx, column=5, value=choices_str).alignment = alignment

        else:
            sheet.cell(row=2, column=1, value="未能获取到该模型的字段信息。").alignment = alignment
        
    try:
        workbook.save(filename)
        print(f"成功将数据写入到 '{filename}'")
    except Exception as e:
        print(f"保存 Excel 文件失败: {e}")

def main():
    """
    主函数，执行整个流程。
    """
    model_names = get_model_names(url1)
    print(f"model_names: {model_names}")
    if not model_names:
        print("未能获取到任何模型名称，程序退出。")
        return

    all_model_data = {}
    for model_info in model_names:
        name = model_info["name"]
        alias = model_info["alias"]
        print(f"正在获取模型 '{name}' len:{len(name)} 的字段信息...")
        field_info = get_model_field_info(url2_base, name )
        #print(f"field_info: {field_info}")
        all_model_data[name] = {"alias": alias, "field_info": field_info}
    #print(f"all_model_data: {all_model_data}")
    write_to_excel(all_model_data, filename)

if __name__ == "__main__":
    main()
