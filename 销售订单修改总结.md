# 销售订单修改总结

## 修改目标
收款单弃用，改为使用应收账本表，但收款记录表还要用。修改 `WmsCore/control/SalesOrder.py` 统一使用应收账本表。

## 主要修改内容

### 1. 导入修改
- 添加了 `AccountsAdmin` 和 `AccountsReceivable` 的导入
- 移除了 `Receipt` 的导入（因为不再使用收款单）

### 2. 序列化器方法修改

#### get_settlement_account 方法
- **原来**: 通过 `Receipt` 收款单查找收款记录
- **现在**: 通过 `AccountsReceivable` 应收账本查找收款记录
- 查询条件改为：`source_order_no`、`source_type`、`sales_order`

#### get_receipt_status 方法  
- **原来**: 查询收款单的状态，返回 `Receipt.ReceiptStatusChoices`
- **现在**: 查询应收账本的收款状态，返回 `AccountsReceivable.PaymentStatusChoices`

#### get_received_amount 方法
- **原来**: 汇总所有收款单的 `received_amount`
- **现在**: 直接返回应收账本记录的 `received_amount`

### 3. 业务逻辑修改

#### 创建销售订单时
- **原来**: 创建 `Receipt` 收款单和 `ReceiptRecord` 收款记录
- **现在**: 
  1. 使用 `AccountsAdmin.create_receivable()` 创建应收账本记录
  2. 设置正确的收款状态（UNPAID/PARTIAL/PAID）
  3. 创建 `ReceiptRecord` 收款记录，关联到应收账本

#### 更新销售订单时
- **原来**: 查找和更新 `Receipt` 收款单
- **现在**: 
  1. 查找和更新 `AccountsReceivable` 应收账本记录
  2. 更新金额字段：`order_amount`、`receivable_amount`、`received_amount`、`remaining_amount`
  3. 更新收款状态
  4. 如果收款金额增加，创建新的收款记录

#### 删除逻辑
- **原来**: 删除收款单和相关收款记录
- **现在**: 删除应收账本记录和相关收款记录

### 4. 修复的问题

#### AccountsAdmin.create_receivable 方法
- **问题**: 原方法使用了错误的参数 `supplier`
- **修复**: 改为使用正确的参数 `customer`
- **修复**: 修正了参数展开语法 `*foreign_order` → `**foreign_order`

### 5. 移除的代码
- 移除了 `ReceiptSerializer` 类（不再需要）
- 移除了对 `Receipt` 模型的所有引用

## 数据流变化

### 原来的数据流
```
SalesOrder → Receipt → ReceiptRecord
```

### 现在的数据流  
```
SalesOrder → AccountsReceivable → ReceiptRecord
```

## 字段映射

| 原收款单字段 | 应收账本字段 | 说明 |
|-------------|-------------|------|
| amount | receivable_amount | 应收金额 |
| received_amount | received_amount | 已收金额 |
| receipt_status | payment_status | 收款状态 |
| discount | - | 折扣在计算应收金额时处理 |
| order_id | source_order_no | 来源订单号 |
| order_type | source_type | 来源类型 |

## 注意事项

1. **收款记录表保留**: `ReceiptRecord` 表继续使用，但关联字段从 `receipt` 改为 `accounts_receivable`

2. **状态枚举变化**: 
   - 收款单状态: `UNPAID/PARTIAL/FULL/CANCELED`
   - 应收账本状态: `UNPAID/PARTIAL/PAID`

3. **业务逻辑保持一致**: 虽然底层数据结构改变，但业务逻辑（创建、更新、查询）保持一致

4. **向后兼容**: 序列化器的字段名保持不变，前端无需修改

## 测试建议

1. 测试销售订单的创建流程
2. 测试销售订单的更新流程  
3. 测试收款记录的创建
4. 测试应收账本数据的正确性
5. 测试前端显示是否正常
