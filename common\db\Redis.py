import json

from django.core.cache import cache
from django_redis import get_redis_connection

from common.logger import logger as log


class RedisUtil:
    """Redis工具类，封装常用的Redis操作"""

    @staticmethod
    def get_connection(alias='default'):
        """
        获取Redis连接
        :param alias: 缓存别名，对应settings.CACHES中的配置
        :return: Redis连接
        """
        return get_redis_connection(alias)

    @staticmethod
    def set(key, value, timeout=None, nx=False, xx=False, version=None):
        """
        设置缓存
        :param key: 键
        :param value: 值
        :param timeout: 过期时间(秒)，None表示永不过期
        :param nx: 如果设置为True，则只有在key不存在时才设置值
        :param xx: 如果设置为True，则只有在key已存在时才设置值
        :param version: 缓存版本
        :return: 是否设置成功
        """
        try:
            return cache.set(key, value, timeout=timeout, nx=nx, xx=xx, version=version)
        except Exception as e:
            log.debug(f"Error setting cache - key: {key} error: {e}")
            return False

    @staticmethod
    def get(key, default=None, version=None):
        """
        获取缓存
        :param key: 键
        :param default: 默认值，当键不存在时返回
        :param version: 缓存版本
        :return: 值或默认值
        """
        try:
            return cache.get(key, default=default, version=version)
        except Exception as e:
            log.debug(f"Error getting cache - key: {key} error: {e}")
            return default

    @staticmethod
    def delete(key, version=None):
        """
        删除缓存
        :param key: 键
        :param version: 缓存版本
        :return: 是否删除成功
        """
        try:
            return cache.delete(key, version=version)
        except Exception as e:
            log.debug(f"Error deleting cache - key: {key}")
            return False

    @staticmethod
    def has_key(key, version=None):
        """
        检查键是否存在
        :param key: 键
        :param version: 缓存版本
        :return: 是否存在
        """
        try:
            return cache.has_key(key, version=version)
        except Exception as e:
            log.debug(f"Error checking cache key - key: {key}")
            return False

    @staticmethod
    def incr(key, delta=1, version=None):
        """
        自增键值
        :param key: 键
        :param delta: 增量
        :param version: 缓存版本
        :return: 自增后的值
        """
        try:
            return cache.incr(key, delta=delta, version=version)
        except Exception as e:
            log.debug(f"Error incrementing cache - key: {key}, delta: {delta}")
            raise

    @staticmethod
    def decr(key, delta=1, version=None):
        """
        自减键值
        :param key: 键
        :param delta: 减量
        :param version: 缓存版本
        :return: 自减后的值
        """
        try:
            return cache.decr(key, delta=delta, version=version)
        except Exception as e:
            log.debug(f"Error decrementing cache - key: {key}, delta: {delta}")
            raise

    @staticmethod
    def expire(key, timeout):
        """
        设置过期时间
        :param key: 键
        :param timeout: 过期时间(秒)
        :return: 是否设置成功
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.expire(key, timeout)
        except Exception as e:
            log.debug(f"Error setting cache expiration - key: {key}, timeout: {timeout}")
            return False

    @staticmethod
    def ttl(key):
        """
        获取剩余过期时间
        :param key: 键
        :return: 剩余过期时间(秒)，-1表示永不过期，-2表示键不存在
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.ttl(key)
        except Exception as e:
            log.debug(f"Error getting cache TTL - key: {key}")
            return -2

    @staticmethod
    def set_json(key, value, timeout=None):
        """
        设置JSON数据
        :param key: 键
        :param value: 值(dict或list等可JSON序列化对象)
        :param timeout: 过期时间(秒)
        :return: 是否设置成功
        """
        try:
            json_value = json.dumps(value)
            return RedisUtil.set(key, json_value, timeout=timeout)
        except Exception as e:
            log.debug(f"Error setting JSON cache - key: {key}")
            return False

    @staticmethod
    def get_json(key, default=None):
        """
        获取JSON数据
        :param key: 键
        :param default: 默认值
        :return: 反序列化后的Python对象
        """
        try:
            value = RedisUtil.get(key, default=default)
            if value is None or value == default:
                return default
            return json.loads(value)
        except Exception as e:
            log.debug(f"Error getting JSON cache - key: {key}")
            return default

    @staticmethod
    def hset(name, key, value):
        """
        设置哈希表字段值
        :param name: 哈希表名
        :param key: 字段名
        :param value: 字段值
        :return: 是否新建字段(1=新建，0=更新)
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.hset(name, key, value)
        except Exception as e:
            log.debug(f"Error setting hash field - name: {name}, key: {key}")
            raise

    @staticmethod
    def hget(name, key):
        """
        获取哈希表字段值
        :param name: 哈希表名
        :param key: 字段名
        :return: 字段值或None
        """
        try:
            conn = RedisUtil.get_connection()
            value = conn.hget(name, key)
            return value.decode('utf-8') if value is not None else None
        except Exception as e:
            log.debug(f"Error getting hash field - name: {name}, key: {key}")
            return None

    @staticmethod
    def hgetall(name):
        """
        获取哈希表所有字段和值
        :param name: 哈希表名
        :return: 包含所有字段和值的字典
        """
        try:
            conn = RedisUtil.get_connection()
            result = conn.hgetall(name)
            return {k.decode('utf-8'): v.decode('utf-8') for k, v in result.items()}
        except Exception as e:
            log.warning(f"Error getting all hash fields - name: {name}")
            return {}

    @staticmethod
    def hdel(name, *keys):
        """
        删除哈希表字段
        :param name: 哈希表名
        :param keys: 字段名列表
        :return: 成功删除的字段数量
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.hdel(name, *keys)
        except Exception as e:
            log.warning(f"Error deleting hash fields - name: {name}, keys: {keys}")
            raise

    @staticmethod
    def lpush(name, *values):
        """
        将值推入列表左侧
        :param name: 列表名
        :param values: 值列表
        :return: 推入后的列表长度
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.lpush(name, *values)
        except Exception as e:
            log.warning(f"Error pushing to list left - name: {name}")
            raise

    @staticmethod
    def rpush(name, *values):
        """
        将值推入列表右侧
        :param name: 列表名
        :param values: 值列表
        :return: 推入后的列表长度
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.rpush(name, *values)
        except Exception as e:
            log.warning(f"Error pushing to list right - name: {name}")
            raise

    @staticmethod
    def lpop(name):
        """
        弹出列表最左侧的值
        :param name: 列表名
        :return: 弹出的值或None
        """
        try:
            conn = RedisUtil.get_connection()
            value = conn.lpop(name)
            return value.decode('utf-8') if value is not None else None
        except Exception as e:
            log.warning(f"Error popping from list left - name: {name}")
            return None

    @staticmethod
    def rpop(name):
        """
        弹出列表最右侧的值
        :param name: 列表名
        :return: 弹出的值或None
        """
        try:
            conn = RedisUtil.get_connection()
            value = conn.rpop(name)
            return value.decode('utf-8') if value is not None else None
        except Exception as e:
            log.warning(f"Error popping from list right - name: {name}")
            return None

    @staticmethod
    def lrange(name, start, end):
        """
        获取列表指定范围内的元素
        :param name: 列表名
        :param start: 起始索引
        :param end: 结束索引
        :return: 元素列表
        """
        try:
            conn = RedisUtil.get_connection()
            values = conn.lrange(name, start, end)
            return [v.decode('utf-8') for v in values]
        except Exception as e:
            log.warning(f"Error getting list range - name: {name}, start: {start}, end: {end}")
            return []

    @staticmethod
    def sadd(name, *values):
        """
        向集合添加成员
        :param name: 集合名
        :param values: 成员列表
        :return: 添加成功的成员数量
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.sadd(name, *values)
        except Exception as e:
            log.warning(f"Error adding to set - name: {name}")
            raise

    @staticmethod
    def smembers(name):
        """
        获取集合所有成员
        :param name: 集合名
        :return: 成员集合
        """
        try:
            conn = RedisUtil.get_connection()
            values = conn.smembers(name)
            return {v.decode('utf-8') for v in values}
        except Exception as e:
            log.warning(f"Error getting set members - name: {name}")
            return set()

    @staticmethod
    def srem(name, *values):
        """
        移除集合成员
        :param name: 集合名
        :param values: 成员列表
        :return: 移除成功的成员数量
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.srem(name, *values)
        except Exception as e:
            log.warning(f"Error removing from set - name: {name}, values: {values}")
            raise

    @staticmethod
    def sismember(name, value):
        """
        判断成员是否在集合中
        :param name: 集合名
        :param value: 成员
        :return: 是否存在
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.sismember(name, value)
        except Exception as e:
            log.warning(f"Error checking set membership - name: {name}, value: {value}")
            return False

    @staticmethod
    def lock(name, timeout=10, blocking_timeout=5):
        """
        获取分布式锁
        :param name: 锁名称
        :param timeout: 锁的超时时间(秒)
        :param blocking_timeout: 获取锁的超时时间(秒)
        :return: 锁对象
        """
        try:
            conn = RedisUtil.get_connection()
            return conn.lock(name, timeout=timeout, blocking_timeout=blocking_timeout)
        except Exception as e:
            log.warning(f"Error creating lock - name: {name}")
            raise
