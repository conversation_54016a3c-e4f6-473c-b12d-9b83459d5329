from django.db import models, transaction
from rest_framework import status
from rest_framework.decorators import action

from AccessGateway.control.auth import make_response
from WmsCore.control.base import SafeModelViewSet
from WmsCore.models import PurchasePayment
from WmsCore.serializers import PurchasePaymentSerializer
from WmsCore.utils.submission import prevent_duplicate_submission

class PurchasePaymentViewSet(SafeModelViewSet):
    """
    采购付款单的增删改查接口
    """
    queryset = PurchasePayment.objects.all()
    serializer_class = PurchasePaymentSerializer

    def list(self, request, *args, **kwargs):
        """获取采购付款单列表"""
        try:
            page = int(request.POST.get('page', 1))
            page_size = int(request.POST.get('page_size', 20))
            queryset = self.get_queryset().order_by('-id').all()
            offset = (page - 1) * page_size
            queryset = queryset[offset:offset + page_size]
            serializer = self.get_serializer(queryset, many=True)
            return make_response(code=0, msg='获取成功', data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return make_response(code=1, msg=f'获取失败: {str(e)}', status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新采购付款单"""
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return make_response(code=0, msg='创建成功', status=status.HTTP_201_CREATED, data=serializer.data)
            return make_response(code=1, msg='参数验证失败', status=status.HTTP_400_BAD_REQUEST, data=serializer.errors)
        except Exception as e:
            return make_response(code=1, msg=f'创建失败: {str(e)}', status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个采购付款单详情"""
        try:
            payment = self.get_object()
            serializer = self.get_serializer(payment)
            return make_response(code=0, msg='获取成功', status=status.HTTP_200_OK, data=serializer.data)
        except PurchasePayment.DoesNotExist:
            return make_response(code=1, msg='采购付款单不存在', status=status.HTTP_404_NOT_FOUND)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新采购付款单信息"""
        try:
            try:
                payment = PurchasePayment.objects.select_for_update().get(pk=pk)
                serializer = self.get_serializer(payment, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()
                    return make_response(code=0, msg='更新成功', status=status.HTTP_200_OK, data=serializer.data)
                return make_response(code=1, msg='更新失败', status=status.HTTP_400_BAD_REQUEST, data=serializer.errors)
            except PurchasePayment.DoesNotExist:
                return make_response(code=1, msg='采购付款单不存在', status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return make_response(code=1, msg=f'更新失败: {str(e)}', status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def destroy(self, request, pk=None, **kwargs):
        """删除采购付款单"""
        try:
            payment = self.get_object()
            payment.delete()
            return make_response(code=0, msg='删除成功', status=status.HTTP_200_OK)
        except PurchasePayment.DoesNotExist:
            return make_response(code=1, msg='采购付款单不存在', status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索采购付款单"""
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=1, msg='请输入搜索关键词', status=status.HTTP_400_BAD_REQUEST)
        payments = PurchasePayment.objects.filter(
            models.Q(handler__icontains=query) |
            models.Q(remark__icontains=query)
        )
        serializer = self.get_serializer(payments, many=True)
        return make_response(code=0, msg='搜索成功', status=status.HTTP_200_OK, data=serializer.data)