from common.exception import ErrorCode

class RetCode(ErrorCode):
    #SUCCESS = 0
    #UNKOWN_ERROR = 1
    WECHAT_LOGIN_CHECK_FAILED = 10001  # 微信登录密钥校验失败
    WECHAT_LOGIN_CODE_EMPTY = 10002  # 微信登录密钥不能为空
    WECHAT_LOGIN_PHONE_EXIST = 10003  # 账号已经绑定了手机号
    WECHAT_LOGIN_PHONE_ALREADY_BIND = 10004  # 手机号已经绑定了其他账号



    ACCOUNT_NOT_LOGIN = 20001  # 账号没有登录
    ACCOUNT_NOT_BIND_ENTERPRISE = 20002  # 账号没有绑定企业
    ACCOUNT_NOT_BIND_PHONE = 20003  # 账号没有绑定手机号
    ENTERPRISE_NOT_EXIST = 20004  # 账号不存在企业
    INVITE_CODE_NOT_EXIST = 20005  # 邀请码不存在
    LEDGER_NOT_AVAILABLE = 20006  # 账套不可用
    REGISTER_COUNT_LIMIT = 20007  # 注册上限限制
    ENTERPRISE_ALREADY_JOIN = 20008  # 企业已经加入
    ACCOUNT_NOT_ADMIN = 20009  # 账号不是管理员
    ENTERPRISE_MEMBER_NOT_EXIST = 20010  # 企业成员不存在
    ENTERPRISE_MEMBER_INVALID_ACCOUNT_TYPE = 20011  # 企业成员无效的账号类型
    ENTERPRISE_MEMBER_NOT_ALLOW_SELF = 20012  # 企业成员不能修改自己的权限
