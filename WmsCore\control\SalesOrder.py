from decimal import Decimal
from typing import List

from django.db import models, transaction
from django.db.models import Prefetch
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import status, serializers
from rest_framework.decorators import action

from WmsCore.admin.sales_order import SalesOrderAdmin as SOA
from WmsCore.admin.Accounts import AccountsAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import SalesOrder, SalesOrderItem, Warehouse, SalesOut, SalesOutItem, Customer, \
    PaymentMethod, ReceiptRecord, AccountsReceivable
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.exception import WmsException
from common.make_response import make_response
from common.logger import logger as log
from common.translation import _T
from WmsCore.utils.tools import CommonTools


class SalesOrderItemSerializer(serializers.ModelSerializer):
    """销售订单物品序列化器"""
    id = serializers.IntegerField(required=False, allow_null=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True)
    conversion_rate = serializers.DecimalField(max_digits=30, decimal_places=8, source='unit.conversion_rate')
    price = serializers.DecimalField(max_digits=30, decimal_places=8, source='sale_price')
    amount = serializers.SerializerMethodField()
    out_quantity = serializers.DecimalField(max_digits=30, decimal_places=8, read_only=True)
    discount=serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True)
    stock_quantity = serializers.SerializerMethodField()
    
    class Meta:
        model = SalesOrderItem
        fields = ['id', 'item', 'item_name', 'unit', 'unit_name', 'quantity','discount', 'out_quantity', 'price', 'amount', 'stock_quantity', 'conversion_rate']
        read_only_fields = ['amount', 'out_quantity', 'stock_quantity', 'conversion_rate']
    
    def get_stock_quantity(self, obj):
        """获取物品在指定仓库的库存数量，未指定仓库时返回所有仓库的库存总和"""
        # 从上下文中获取仓库ID
        warehouse_id = self.context.get('warehouse_id')
        if warehouse_id:
            try:
                from WmsCore.models import Warehouse
                warehouse = Warehouse.objects.get(id=warehouse_id)
                stock_quantity, _ = SOA.count_stock_quantity(obj.item, obj.unit, warehouse)
                return stock_quantity
            except Warehouse.DoesNotExist:
                return None
        else:
            # 未指定仓库时，返回所有仓库的库存总和
            stock_quantity, _ = SOA.count_stock_quantity(obj.item, obj.unit, None)
            return stock_quantity

    def get_amount(self, obj):
        """动态计算单个物品的总价"""
        return obj.quantity * obj.sale_price

    def validate(self, attrs):
        item = attrs.get('item')
        unit = attrs.get('unit')
        if unit.item_id != item.id:
            raise WmsException(_("单位不属于该物品，请重新选择单位"))
        return attrs

# 支付方式序列化器
class PaymentMethodSerializer(serializers.ModelSerializer):
    account_type_display = serializers.CharField(source='get_account_type_display', read_only=True)
    settlement_type_display = serializers.CharField(source='get_default_settlement_display', read_only=True)
    name = serializers.CharField(source='account_name', read_only=True)
    payment_method_name=serializers.CharField(source='account_name', read_only=True)
    class Meta:
        model = PaymentMethod
        fields = ['id', 'name', 'account_type_display', 'settlement_type_display', 'payment_method_name']

@SerializerCollector(alias="销售订单")
class SalesOrderSerializer(serializers.ModelSerializer):
    """销售订单序列化器"""
    items = SalesOrderItemSerializer(many=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    order_id = serializers.CharField(read_only=True)
    expected_delivery_date=serializers.DateField(required=False, allow_null=True)
    is_draft = serializers.BooleanField(required=True)
    payment_method=serializers.PrimaryKeyRelatedField(
        queryset=PaymentMethod.objects.all(),
        required=False,
        allow_null=True
    )
    settlement_account = PaymentMethodSerializer(source='payment_method', read_only=True)
    pay_amount = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True)
    
    payment_method_discount=serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True)
    discount = serializers.DecimalField(max_digits=30, decimal_places=8,required=False)
    id = serializers.IntegerField(read_only=True)
    receipt_status = serializers.SerializerMethodField()
    receivable_amount = serializers.SerializerMethodField()
    received_amount = serializers.SerializerMethodField()
    unpaid_amount = serializers.SerializerMethodField()
    handler_name = serializers.CharField(source='controller.name', read_only=True)
    prepayment=serializers.DecimalField(read_only=True,max_digits=30, decimal_places=8)
    short_desc = serializers.CharField(read_only=True)
    warehouse_id = serializers.SerializerMethodField()
    warehouse_name = serializers.SerializerMethodField()
    payment_method_name=serializers.CharField(source='payment_method.account_name', read_only=True)
    class Meta:
        model = SalesOrder
        fields = [
            'id', 'order_id', 'customer', 'customer_name','pay_amount','handler_name',
            'order_date', 'total_amount', 'handler', 'order_status', 'items', 'payment_method',
            'expected_delivery_date', 'payment_method_discount', 'discount','is_draft',
            'receipt_status', 'receivable_amount', 'received_amount', 'unpaid_amount',
            'settlement_account','prepayment','short_desc', 'warehouse_id', 'warehouse_name',
            'payment_method_name'
        ]
        read_only_fields = ['total_amount', 'order_status', 'order_id', 'warehouse_name', 
                            'handler','prepayment','short_desc', 'settlement_account', 'warehouse_id']
    
    def get_warehouse_id(self, obj):
        # 从上下文中获取仓库ID
        warehouse_id = self.context.get('warehouse_id')
        if warehouse_id:
            return warehouse_id
        # 如果上下文中没有仓库ID，尝试从关联的销售出库单获取
        sales_out = SalesOut.objects.filter(sales_order=obj).first()
        if sales_out:
            return sales_out.warehouse.id
        return None
    
    def get_warehouse_name(self, obj):
        # 从上下文中获取仓库ID
        warehouse_id = self.context.get('warehouse_id')
        if warehouse_id:
            try:
                warehouse = Warehouse.objects.get(id=warehouse_id)
                return warehouse.name
            except Warehouse.DoesNotExist:
                pass
        # 如果上下文中没有仓库ID，尝试从关联的销售出库单获取
        sales_out = SalesOut.objects.filter(sales_order=obj).first()
        if sales_out:
            return sales_out.warehouse.name
        return None

    def get_receipt_status(self, obj):
        # 获取该订单的应收账本记录
        accounts_receivable = AccountsReceivable.objects.filter(
            source_order_no=str(obj.id),
            source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
            sales_order=obj
        ).first()

        if not accounts_receivable:
            return AccountsReceivable.PaymentStatusChoices.UNPAID

        return accounts_receivable.payment_status

    def get_receivable_amount(self, obj):
        # 应收金额 = 订单金额 - 订单折扣
        return obj.total_amount - obj.discount

    def get_received_amount(self, obj):
        # 获取该订单的应收账本记录的已收款金额
        accounts_receivable = AccountsReceivable.objects.filter(
            source_order_no=str(obj.id),
            source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
            sales_order=obj
        ).first()

        if not accounts_receivable:
            return Decimal('0')

        return accounts_receivable.received_amount

    def get_unpaid_amount(self, obj):
        # 未收金额 = 应收金额 - 已收金额
        receivable = self.get_receivable_amount(obj)
        received = self.get_received_amount(obj)
        return receivable - received

    def validate_customer(self, value):
        if not Customer.objects.filter(pk=value.pk).exists():
            raise WmsException(_("客户不存在，请重新选择客户"))
        return value



class SalesOutSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesOut
        fields = '__all__'


class SalesOutItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesOutItem
        fields = '__all__'

class ReceiptRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReceiptRecord
        fields = '__all__'

class SalesOrderViewSet(SafeModelViewSet):
    """
    销售订单的增删改查接口
    """
    # 保留静态queryset属性用于DRF自动确定basename
    queryset = SalesOrder.objects.prefetch_related(
        Prefetch('items', queryset=SalesOrderItem.objects.select_related('item', 'unit__unit_type'))
    ).select_related('customer').all()
    
    def get_queryset(self):
        """
        重写 get_queryset 方法，添加过滤条件
        默认不返回已取消的订单，除非请求中指定 include_cancelled=true
        支持通过 document_type 参数筛选零售订单和批发订单
        """
        queryset = super().get_queryset()
        
        # 获取是否包含已取消订单的参数
        include_cancelled = self.request.query_params.get('include_cancelled', 'false').lower()
        include_cancelled = include_cancelled in ['true', '1', 'yes']
        
        # 获取订单类型参数
        document_type = self.request.query_params.get('document_type', None)
        
        # 默认不返回已取消的订单
        if not include_cancelled:
            queryset = queryset.exclude(order_status=SalesOrder.OrderStatusChoices.CANCELLED)
        
        # 根据订单类型筛选
        if document_type in ['retail', 'wholesale']:
            # 查找关联的销售出库单，根据document_type过滤
            from WmsCore.models import SalesOut
            sales_out_orders = SalesOut.objects.filter(document_type=document_type).values_list('sales_order_id', flat=True)
            queryset = queryset.filter(id__in=sales_out_orders)
            
        return queryset

    serializer_class = SalesOrderSerializer
    pagination_class = StandardResultsSetPagination

    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        # 对于写操作，不需要预取关系
        if self.action in ['create', 'update', 'partial_update']:
            return super().get_serializer(*args, **kwargs)

        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()

        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            try:
                instance = self.get_queryset().get(id=instance.id)
                kwargs['instance'] = instance
            except SalesOrder.DoesNotExist:
                # 在某些情况下（如删除后），实例可能不存在，这时保持原样
                pass

        return super().get_serializer(*args, **kwargs)

    def _validate_items_not_empty(self, items_data):
        if not items_data:
            raise WmsException(_("销售订单必须包含至少一个物品"))

    def _validate_stock(self, items_data):
        item_tips = []
        for item_data in items_data:
            stock_quantity, stock_quantity_main = SOA.count_stock_quantity(item_data['item'], item_data['unit'])
            if stock_quantity_main - item_data['item'].order_demand - item_data['quantity'] * item_data['item'].unit.conversion_rate < 0:
                item_tips.append(
                    {
                        "item": item_data['item'].id,
                        "name": item_data['item'].name,
                        "msg": _T("物品 '{item}' 可用库存量不足，需要 {qty} {unit}", 
                                  item=item_data['item'].name, 
                                  qty=item_data['quantity'], 
                                  unit=item_data['unit'].unit_type.name)
                    }
                )
        return item_tips

    def _calc_total_amount(self, items_data, total_discount):
        total_amount = Decimal('0')
        for item_data in items_data:
            total_amount += Decimal(item_data['quantity']) * Decimal(item_data['sale_price']) - Decimal(item_data.get('discount', '0'))
        return total_amount - total_discount

    def _validate_total_amount(self, request, total_amount):
        total_amount__ = request.data.get('total_amount', Decimal('0'))
        if total_amount__ != total_amount:
            raise WmsException(_("前端传入的金额与服务器计算的金额不一致"))

    def _validate_expected_delivery_date(self, order_data):
        if not order_data.get('expected_delivery_date'):
            raise WmsException(_("必须指定预计交货日期"))

    def _update_main_fields(self, sales_order, serializer, handler, total_amount):
        for field, value in serializer.validated_data.items():
            setattr(sales_order, field, value)
        if handler:
            sales_order.handler = handler
        sales_order.total_amount = total_amount
        sales_order.save()

    def _update_items(self, sales_order, items_data):
        """更新销售订单的物品项"""
        log.info(f"更新销售订单 {sales_order.id} 的物品项，共 {len(items_data)} 项")
        
        # 获取现有物品项
        existing_items = list(SalesOrderItem.objects.filter(sales_order=sales_order))
        existing_item_ids = {item.id for item in existing_items}
        log.debug(f"销售订单 {sales_order.id} 现有物品项 IDs: {existing_item_ids}")
        
        # 跟踪要保留的物品项 ID
        keep_item_ids = set()
        
        # 处理每个物品项
        for item_data in items_data:
            item_id = item_data.get('id')
            
            if item_id and item_id in existing_item_ids:
                # 更新现有物品项
                existing_item = next((item for item in existing_items if item.id == item_id), None)
                if existing_item:
                    log.debug(f"更新现有物品项 ID: {item_id}, 物品: {item_data.get('item').name if 'item' in item_data else '未知'}")
                    
                    # 如果物品已经有部分出库，不允许减少数量
                    if existing_item.out_quantity > 0 and 'quantity' in item_data and item_data['quantity'] < existing_item.quantity:
                        raise WmsException(_T(f"物品 '{existing_item.item.name}' 已有 {existing_item.out_quantity} 出库，不能减少订单数量"))
                    
                    # 更新物品项属性
                    for field, value in item_data.items():
                        if field != 'id' and hasattr(existing_item, field):
                            setattr(existing_item, field, value)
                    
                    existing_item.save()
                    keep_item_ids.add(item_id)
            else:
                # 创建新物品项
                log.debug(f"创建新物品项, 物品: {item_data.get('item').name if 'item' in item_data else '未知'}")
                new_item = SalesOrderItem(
                    sales_order=sales_order,
                    item=item_data['item'],
                    quantity=item_data['quantity'],
                    out_quantity=item_data.get('out_quantity', 0),
                    sale_price=item_data['sale_price'],
                    discount=item_data.get('discount', 0),
                    unit=item_data['unit'],
                )
                new_item.save()
                
                # 如果不是草稿状态，更新物品的需求量
                if not sales_order.is_draft:
                    item_data['item'].order_demand += item_data['quantity'] * item_data['item'].unit.conversion_rate
                    item_data['item'].save()
        
        # 删除不再需要的物品项
        items_to_delete = [item for item in existing_items if item.id not in keep_item_ids]
        for item in items_to_delete:
            # 检查物品是否已经有部分出库
            if item.out_quantity > 0:
                raise WmsException(_T(f"物品 '{item.item.name}' 已有 {item.out_quantity} 出库，不能从订单中移除"))
            
            # 如果不是草稿状态，减少物品的需求量
            if not sales_order.is_draft:
                item.item.order_demand -= item.quantity * item.item.unit.conversion_rate
                item.item.save()
            
            log.debug(f"删除物品项 ID: {item.id}, 物品: {item.item.name}")
            item.delete()
        
        # 更新订单的物品数量
        sales_order.item_count = SalesOrderItem.objects.filter(sales_order=sales_order).count()
        sales_order.save()
        
        log.info(f"销售订单 {sales_order.id} 物品项更新完成，保留 {len(keep_item_ids)} 项，删除 {len(items_to_delete)} 项，新增 {len(items_data) - len(keep_item_ids)} 项")

    def _make_response(self, sales_order, item_tips=None):
        sales_order.refresh_from_db()
        sales_order_data = SalesOrderSerializer(sales_order).data
        sales_order_items_data = SalesOrderItemSerializer(sales_order.items.all(), many=True).data
        return make_response(
            code=0,
            msg=_("操作成功"),
            data={
                "is_insufficient_stock": bool(item_tips),
                "item_tips": item_tips if item_tips else None,
                "sales_order": sales_order_data,
                "sales_order_items": sales_order_items_data
            },
            status=status.HTTP_201_CREATED
        )

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return self._create_or_update(serializer, request, is_update=False)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT', 'PATCH'])
    def update(self, request, *args, **kwargs):
        sales_order = self.get_object()
        
        # 检查是否有关联的销售出库单
        has_sales_out = SalesOut.objects.filter(sales_order=sales_order).exists()
        
        # 只有没有关联销售出库单的订单才能更新
        if has_sales_out:
            raise WmsException(_T("该销售订单已有关联的销售出库单，不能进行任何更新"))
            
        # 如果是草稿单，允许更新
        serializer = self.get_serializer(sales_order, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        # 禁止将正式单变为草稿
        is_draft_new = serializer.validated_data.get('is_draft', sales_order.is_draft)
        if not is_draft_new and sales_order.is_draft:
            # 允许草稿变正式
            pass
        elif is_draft_new and not sales_order.is_draft:
            # 禁止正式单变草稿
            raise WmsException(_("正式单不能变为草稿单"))
            
        return self._create_or_update(serializer, request, is_update=True)

    def _create_or_update(self, serializer, request, is_update):
        items_data = serializer.validated_data.pop('items', [])
        serializer.validated_data.pop('warehouse', None)
        total_discount = serializer.validated_data.pop('discount', Decimal('0'))
        handler = request.data.get('handler', request.zt_user)
        is_draft = serializer.validated_data.get('is_draft', False)
        # 不要pop掉payment_method，保留在订单中
        payment_method = serializer.validated_data.get('payment_method', None)
        payment_method_discount = serializer.validated_data.pop('payment_method_discount', None)
        serializer.validated_data.pop('images', None)
        receipt_amount = serializer.validated_data.pop('pay_amount', Decimal('0'))
        
        # 草稿单不需要支付信息，正式单必须校验
        # if is_draft:
        #     serializer.validated_data['payment_method'] = None
        #     serializer.validated_data['payment_method_discount'] = None
        # else:
        #     if not payment_method:
        #         raise WmsException(_T("正式单必须填写支付方式"), code=1003)
        #     if payment_method_discount is None:
        #         raise WmsException(_T("正式单必须填写支付折扣"), code=1003)
        
        self._validate_items_not_empty(items_data)
        if not is_draft:
            item_tips = self._validate_stock(items_data)
            self._validate_expected_delivery_date(serializer.validated_data)
        else:
            item_tips = []
        
        total_amount = self._calc_total_amount(items_data, total_discount)
        self._validate_total_amount(request, total_amount)
        order_data = dict(serializer.validated_data)
        order_data.pop('warehouse', None)
        order_data['total_amount'] = total_amount

        order_data.pop('order_id', None)
        order_data.pop('id', None)
        
        # 使用 CommonTools 生成简要描述
        order_data['short_desc'] = CommonTools.generate_short_desc(items_data)
        
        if is_update:
            sales_order = serializer.instance
            self._update_main_fields(sales_order, serializer, handler, total_amount)
            sales_order.short_desc = order_data['short_desc']
            sales_order.save()
            self._update_items(sales_order, items_data)
            
            # 如果是正式单且有支付方式，更新应收账本和收款记录
            if not is_draft:
                # 检查是否有关联的销售出库单
                has_sales_out = SalesOut.objects.filter(sales_order=sales_order).exists()

                # 查找现有的应收账本记录
                accounts_receivable = AccountsReceivable.objects.filter(
                    source_order_no=str(sales_order.id),
                    source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                    sales_order=sales_order
                ).first()

                if payment_method:
                    # 有支付方式，更新或创建应收账本记录
                    if accounts_receivable:
                        # 更新现有应收账本
                        accounts_receivable.order_amount = total_amount
                        accounts_receivable.receivable_amount = total_amount - (payment_method_discount or Decimal('0'))

                        # 如果收款金额有变化
                        if receipt_amount != accounts_receivable.received_amount:
                            old_received_amount = accounts_receivable.received_amount
                            accounts_receivable.received_amount = receipt_amount
                            accounts_receivable.remaining_amount = accounts_receivable.receivable_amount - receipt_amount

                            # 更新收款状态
                            if receipt_amount == 0:
                                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID
                            elif receipt_amount < accounts_receivable.receivable_amount:
                                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PARTIAL
                            else:
                                accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PAID

                            accounts_receivable.save()

                            # 如果收款金额增加，创建新的收款记录
                            if receipt_amount > old_received_amount:
                                additional_amount = receipt_amount - old_received_amount
                                ReceiptRecord.objects.create(
                                    accounts_receivable=accounts_receivable,
                                    payment_method=payment_method,
                                    amount=additional_amount,
                                    handler=request.zt_user,
                                    payment_date=timezone.now(),
                                    controller=request.zt_user,
                                )
                        else:
                            accounts_receivable.save()
                    else:
                        # 创建新的应收账本记录
                        receivable_amount = total_amount - (payment_method_discount or Decimal('0'))
                        accounts_receivable = AccountsAdmin.create_receivable(
                            customer=sales_order.customer,
                            order_amount=total_amount,
                            receivable_amount=receivable_amount,
                            remaining_amount=receivable_amount - receipt_amount,
                            handler=request.zt_user,
                            source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                            source_order_id=str(sales_order.id),
                            remark=f"销售订单 {sales_order.order_id}",
                            sales_order=sales_order
                        )

                        # 设置收款状态
                        if receipt_amount == 0:
                            accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID
                        elif receipt_amount < receivable_amount:
                            accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PARTIAL
                        else:
                            accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PAID

                        accounts_receivable.received_amount = receipt_amount
                        accounts_receivable.save()

                        if receipt_amount > 0:
                            # 创建收款记录
                            ReceiptRecord.objects.create(
                                accounts_receivable=accounts_receivable,
                                payment_method=payment_method,
                                amount=receipt_amount,
                                handler=request.zt_user,
                                payment_date=timezone.now(),
                                controller=request.zt_user,
                            )
                else:
                    # 没有支付方式，需要删除之前的应收账本记录
                    if accounts_receivable and not has_sales_out:
                        # 如果没有关联的销售出库单，则可以删除收款记录
                        # 先删除收款记录
                        ReceiptRecord.objects.filter(accounts_receivable=accounts_receivable).delete()
                        # 再删除应收账本记录
                        accounts_receivable.delete()
                        log.info(f"销售订单 {sales_order.id} 更新时删除了应收账本和收款记录")
                    elif accounts_receivable and has_sales_out:
                        # 如果有关联的销售出库单，不能删除收款记录，给出警告
                        log.warning(f"销售订单 {sales_order.id} 已有关联的销售出库单，不能删除收款记录")
                
                # 更新销售订单的付款状态
                if payment_method:
                    if receipt_amount == 0:
                        sales_order.payment_status = SalesOrder.PaymentStatusChoices.UNPAID
                    elif receipt_amount < total_amount - (payment_method_discount or Decimal('0')):
                        sales_order.payment_status = SalesOrder.PaymentStatusChoices.PARTIAL
                    else:
                        sales_order.payment_status = SalesOrder.PaymentStatusChoices.PAID
                    
                    sales_order.prepayment = receipt_amount
                else:
                    # 没有支付方式，设置为未付款状态
                    sales_order.payment_status = SalesOrder.PaymentStatusChoices.UNPAID
                    sales_order.prepayment = Decimal('0')
                
                sales_order.save()
                
        else:
            # 生成订单编号
            order_data['order_id'] = Counter.DayCounter("SO")
            # 不要pop掉payment_method
            # order_data.pop('payment_method',None)
            order_data.pop('payment_method_discount',None)
            sales_order = SalesOrder.objects.create(**order_data, controller=request.zt_user,handler=request.zt_user,discount=total_discount,prepayment=receipt_amount)
            if receipt_amount >0:
                sales_order.order_status = SalesOrder.OrderStatusChoices.PARTIAL
                sales_order.save()
            if receipt_amount == total_amount:
                sales_order.order_status = SalesOrder.OrderStatusChoices.COMPLETED
                sales_order.save()
            order_items = []
            for item in items_data:
                order_items.append(
                    SalesOrderItem(
                        sales_order=sales_order,
                        item=item['item'],
                        quantity=item['quantity'],
                        out_quantity=item.get('out_quantity', 0),
                        sale_price=item['sale_price'],
                        discount=item.get('discount', 0),
                        unit=item['unit'],
                    )
                )
                if not is_draft:
                    item['item'].order_demand += item['quantity'] * item['item'].unit.conversion_rate
                    item['item'].save()
            SalesOrderItem.objects.bulk_create(order_items)
            
            # 如果不是草稿状态且有支付方式，创建应收账本和收款记录
            if not is_draft and payment_method:
                # 创建应收账本记录
                receivable_amount = total_amount - (payment_method_discount or Decimal('0'))
                accounts_receivable = AccountsAdmin.create_receivable(
                    customer=sales_order.customer,
                    order_amount=total_amount,
                    receivable_amount=receivable_amount,
                    remaining_amount=receivable_amount - receipt_amount,
                    handler=request.zt_user,
                    source_type=AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE,
                    source_order_id=str(sales_order.id),
                    remark=f"销售订单 {sales_order.order_id}",
                    sales_order=sales_order
                )

                # 设置收款状态
                if receipt_amount == 0:
                    accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.UNPAID
                elif receipt_amount < receivable_amount:
                    accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PARTIAL
                else:
                    accounts_receivable.payment_status = AccountsReceivable.PaymentStatusChoices.PAID

                accounts_receivable.received_amount = receipt_amount
                accounts_receivable.save()

                if receipt_amount > 0:
                    # 创建收款记录
                    ReceiptRecord.objects.create(
                        accounts_receivable=accounts_receivable,
                        payment_method=payment_method,
                        amount=receipt_amount,
                        handler=request.zt_user,
                        payment_date=timezone.now(),
                        controller=request.zt_user,
                    )
        
        return self._make_response(sales_order, item_tips)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索销售订单，支持订单编号、经办人、前三个物品名称模糊查询"""
        query = request.query_params.get('query', '').strip()
        qs = self.get_queryset()
        if query:
            # 订单编号、经办人模糊查找
            qs = qs.filter(
                models.Q(order_id__icontains=query) |
                models.Q(handler__name__icontains=query) |
                models.Q(items__item__name__icontains=query)|
                models.Q(customer__name__icontains=query)
            ).distinct()
        serializer = self.get_serializer(qs, many=True)
        return make_response(
            code=0,
            msg=_('搜索成功'),
            data=serializer.data,
            status=status.HTTP_200_OK
        )
    
    def list(self, request, *args, **kwargs):
        """获取销售出库列表，支持按客户ID筛选"""
        customer_id = request.query_params.get('customer_id', None)
        exclude_draft = request.query_params.get('exclude_draft', None)
        queryset = self.get_queryset()
        # 处理exclude_draft参数，过滤草稿状态的订单
        if exclude_draft and exclude_draft.lower() in ['true', '1', 'yes']:
            queryset = queryset.filter(is_draft=False)

        # 处理customer_id参数
        if customer_id:
            try:
                customer_id = int(customer_id)
                queryset = queryset.filter(customer_id=customer_id)
                # 如果没有找到对应客户ID的记录，返回空列表
                if not queryset.exists():
                    return make_response(
                        code=0,
                        msg='获取成功',
                        data={'count': 0, 'next': None, 'previous': None, 'results': []},
                        status=status.HTTP_200_OK
                    )
            except ValueError:
                # 如果customer_id无效（不能转为整数），返回空列表
                return make_response(
                    code=0,
                    msg='获取成功',
                    data={'count': 0, 'next': None, 'previous': None, 'results': []},
                    status=status.HTTP_200_OK
                )
        
        # 使用父类的list方法处理分页等逻辑
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return make_response(
            code=0,
            msg='获取成功',
            data=serializer.data,
            status=status.HTTP_200_OK
        )
    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        sales_order = self.get_object()
        if not sales_order.is_draft and sales_order.order_status != SalesOrder.OrderStatusChoices.PENDING:
            raise WmsException(_T("只有草稿单或预定单状态的订单可以取消"))
            
        # 检查是否有关联的销售出库单
        from WmsCore.models import SalesOut
        if SalesOut.objects.filter(sales_order=sales_order).exists():
            raise WmsException(_T("该销售订单已经产生销售出库单，无法取消"))
        
        # 修改销售订单状态为已取消，而非删除
        sales_order.order_status = SalesOrder.OrderStatusChoices.CANCELLED
        sales_order.save()
        
        return make_response(code=0, msg=_T("销售订单已取消"), data={}, status=status.HTTP_200_OK)
    
    def retrieve(self, request, *args, **kwargs):
        """获取单个销售订单详情，并根据指定仓库ID计算每个物品的库存"""
        # 获取仓库ID参数
        warehouse_id = request.query_params.get('warehouse_id', None)
        
        # 获取销售订单实例
        instance = self.get_object()
        
        # 准备序列化器上下文
        serializer_context = self.get_serializer_context()
        if warehouse_id:
            serializer_context['warehouse_id'] = warehouse_id
        
        # 使用带上下文的序列化器
        serializer = self.get_serializer(instance, context=serializer_context)
        
        return make_response(
            code=0,
            msg=_('获取成功'),
            data=serializer.data,
            status=status.HTTP_200_OK
        )
