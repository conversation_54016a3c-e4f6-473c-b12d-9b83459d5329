version: '3.1'

include:
  - env_file: ./.env

services:
  master:
    container_name: "${PROJ_NAME:-wms}_citus"
    image: "dreg.fatea.net/dhub/citusdata/citus:13.0.3"
    ports: ["${POSTGRES_PORT:-5432}:5432"]
    environment: &AUTH
      POSTGRES_USER: "${POSTGRES_USER:-postgres}"
      POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"
      PGUSER: "${POSTGRES_USER:-postgres}"
      PGPASSWORD: "${POSTGRES_PASSWORD}"
      POSTGRES_HOST_AUTH_METHOD: "${POSTGRES_HOST_AUTH_METHOD:-trust}"
    volumes: 
      - ${WORK_DIR:-.}/volumes/citus/data:/var/lib/postgresql/data
    restart: always

  redis:
    image: "dreg.fatea.net/dhub/library/redis:6-alpine"
    container_name: "${PROJ_NAME:-wms}_redis"
    restart: always
    ports:
      - "6379:6379"
    command:
      - /usr/local/etc/redis/redis.conf
    volumes: 
      - ${WORK_DIR:-.}/volumes/redis/data:/redis/data 
      - ${WORK_DIR:-.}/volumes/redis/conf:/usr/local/etc/redis 


