import threading

import requests
from conf.config import cnf
from utils.jw_auth import JWTServiceAuth

class GM():
    def __init__(self ):
        pass

    # @staticmethod
    # def RequestToCheckTenantCapacity():
    #     new_code = JWTServiceAuth.NewToken(cnf.CHECK_KEY);
    #     #起线程异步发起请求，发起后不用等待回包
    #     #thread = threading.Thread(target=GM.RequestToMalloc, args=(new_code,))
    #     #thread.start()
    #     return GM.RequestToMalloc(new_code)

    # @staticmethod
    # def RequestToMalloc(code:str):
    #     try:
    #         response = requests.post(cnf.PRE_MALLOC_TENANT_URL, json={'code': code})
    #         rsp = response.json()
    #     except Exception as e:
    #         return 0;
