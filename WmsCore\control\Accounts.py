import datetime
from rest_framework import serializers
from WmsCore.models import AccountsPayable, AccountsReceivable, PurchaseIn, SalesOut
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q, Sum, Count
from django.utils import timezone
from django.utils.decorators import method_decorator
from common.logger import logger as log
from django.db import transaction
from django.views.decorators.csrf import csrf_exempt

class AccountsPayableSerializer(serializers.ModelSerializer):
    """应付账本序列化器"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    handler_name = serializers.CharField(source='handler.name', read_only=True)
    controller_name = serializers.CharField(source='controller.name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    payment_status_display = serializers.Char<PERSON>ield(source='get_payment_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    overdue_days = serializers.IntegerField(read_only=True)

    class Meta:
        model = AccountsPayable
        fields = [
            'id', 'payable_no', 'source_order_no', 'source_type', 'source_type_display',
            'supplier', 'supplier_name', 'payable_amount', 'order_amount', 'remaining_amount',
            'payment_status', 'payment_status_display', 'due_date', 'create_date', 'update_date',
            'purchase_order', 'purchase_in', 'stock_return', 'cost_allocation',
            'handler', 'handler_name', 'remark','controller_name',
            'is_overdue', 'overdue_days'
        ]
        read_only_fields = ['order_amount', 'remaining_amount', 'payment_status', 'create_date', 'update_date', 'is_overdue', 'overdue_days']

    def validate(self, attrs):
        # 验证应付金额必须大于0
        payable_amount = attrs.get('payable_amount')
        if payable_amount and payable_amount <= 0:
            raise serializers.ValidationError({'payable_amount': '应付金额必须大于0'})
        
        # 验证已付款金额不能超过应付金额
        paid_amount = attrs.get('paid_amount', 0)
        if payable_amount and paid_amount > payable_amount:
            raise serializers.ValidationError({'paid_amount': '已付款金额不能超过应付金额'})
        
        # 验证来源类型与关联字段的一致性
        source_type = attrs.get('source_type')
        if source_type == AccountsPayable.SourceTypeChoices.PURCHASE_PAYABLE:
            if not attrs.get('purchase_order') and not attrs.get('purchase_in'):
                raise serializers.ValidationError('进货应付必须关联采购订单或采购入库')
        elif source_type == AccountsPayable.SourceTypeChoices.RETURN_PAYABLE:
            if not attrs.get('stock_return'):
                raise serializers.ValidationError('退货应付必须关联库存退货')
        elif source_type == AccountsPayable.SourceTypeChoices.COST_ALLOCATION_PAYABLE:
            if not attrs.get('cost_allocation'):
                raise serializers.ValidationError('分摊费用应付必须关联费用分摊')
        
        return attrs


class AccountsPayableListSerializer(serializers.ModelSerializer):
    """应付账本列表序列化器（简化版）"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    overdue_days = serializers.IntegerField(read_only=True)

    class Meta:
        model = AccountsPayable
        fields = [
            'id', 'payable_no', 'source_order_no', 'source_type', 'source_type_display',
            'supplier_name', 'payable_amount', 'order_amount', 'remaining_amount',
            'payment_status', 'payment_status_display', 'due_date', 'create_date',
            'is_overdue', 'overdue_days'
        ]

class AccountsReceivableSerializer(serializers.ModelSerializer):
    """应收账本序列化器"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    handler_name = serializers.CharField(source='handler.name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    overdue_days = serializers.IntegerField(read_only=True)

    class Meta:
        model = AccountsReceivable
        fields = [
            'id', 'receivable_no', 'source_order_no', 'source_type', 'source_type_display',
            'customer', 'customer_name', 'receivable_amount', 'order_amount', 'remaining_amount',
            'payment_status', 'payment_status_display', 'due_date', 'create_date', 'update_date',
            'sales_order', 'sales_out', 'sales_return',
            'handler', 'handler_name', 'remark',
            'is_overdue', 'overdue_days'
        ]
        read_only_fields = ['remaining_amount', 'payment_status', 'create_date', 'update_date', 'is_overdue', 'overdue_days']

    def validate(self, attrs):
        # 验证应收金额必须大于0
        receivable_amount = attrs.get('receivable_amount')
        if receivable_amount and receivable_amount <= 0:
            raise serializers.ValidationError({'receivable_amount': '应收金额必须大于0'})
        
        # 验证已收款金额不能超过应收金额
        received_amount = attrs.get('received_amount', 0)
        if receivable_amount and received_amount > receivable_amount:
            raise serializers.ValidationError({'received_amount': '已收款金额不能超过应收金额'})
        
        # 验证来源类型与关联字段的一致性
        source_type = attrs.get('source_type')
        if source_type == AccountsReceivable.SourceTypeChoices.SALES_RECEIVABLE:
            if not attrs.get('sales_order') and not attrs.get('sales_out'):
                raise serializers.ValidationError('销售应收必须关联销售订单或销售出库')
        elif source_type == AccountsReceivable.SourceTypeChoices.RETURN_RECEIVABLE:
            if not attrs.get('sales_return'):
                raise serializers.ValidationError('退货应收必须关联销售退货')
        
        return attrs


class AccountsReceivableListSerializer(serializers.ModelSerializer):
    """应收账本列表序列化器（简化版）"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    overdue_days = serializers.IntegerField(read_only=True)

    class Meta:
        model = AccountsReceivable
        fields = [
            'id', 'receivable_no', 'source_order_no', 'source_type', 'source_type_display',
            'customer_name', 'receivable_amount', 'order_amount', 'remaining_amount',
            'payment_status', 'payment_status_display', 'due_date', 'create_date',
            'is_overdue', 'overdue_days'
        ]

@method_decorator(csrf_exempt, name='dispatch')
class AccountsPayableViewSet(SafeModelViewSet):
    """应付账本视图集"""
    queryset = AccountsPayable.objects.all()
    serializer_class = AccountsPayableSerializer
    pagination_class = StandardResultsSetPagination

    def get_serializer_class(self):
        """根据操作类型选择序列化器"""
        if self.action == 'list':
            return AccountsPayableListSerializer
        return AccountsPayableSerializer

    def get_queryset(self):
        """重写查询集，支持过滤和搜索"""
        queryset = super().get_queryset()
        
        # 获取查询参数
        supplier_id = self.request.query_params.get('supplier_id')
        source_type = self.request.query_params.get('source_type')
        payment_status = self.request.query_params.get('payment_status')
        search = self.request.query_params.get('search')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        overdue_only = self.request.query_params.get('overdue_only', 'false').lower() == 'true'
        
        # 应用过滤条件
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
        
        if source_type:
            queryset = queryset.filter(source_type=source_type)
        
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)
        
        if search:
            queryset = queryset.filter(
                Q(payable_no__icontains=search) |
                Q(source_order_no__icontains=search) |
                Q(supplier__name__icontains=search) |
                Q(remark__icontains=search)
            )
        
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(create_date__date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(create_date__date__lte=end_date)
            except ValueError:
                pass
        
        if overdue_only:
            today = timezone.now().date()
            queryset = queryset.filter(
                Q(due_date__lt=today) & 
                Q(remaining_amount__gt=0)
            )
        
        return queryset.select_related('supplier', 'handler', 'controller')

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取应付账本统计信息"""
        queryset = self.get_queryset()
        
        # 基础统计
        total_payable = queryset.aggregate(
            total=Sum('payable_amount'),
            paid=Sum('paid_amount'),
            remaining=Sum('remaining_amount')
        )
        
        # 按状态统计
        status_stats = queryset.values('payment_status').annotate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 按来源类型统计
        source_stats = queryset.values('source_type').annotate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 逾期统计
        today = timezone.now().date()
        overdue_queryset = queryset.filter(
            Q(due_date__lt=today) & 
            Q(remaining_amount__gt=0)
        )
        overdue_stats = overdue_queryset.aggregate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 按供应商统计
        supplier_stats = queryset.values('supplier__name').annotate(
            count=Count('id'),
            payable_amount=Sum('payable_amount'),
            paid_amount=Sum('paid_amount'),
            remaining_amount=Sum('remaining_amount')
        ).order_by('-remaining_amount')[:10]
        
        return Response({
            'total_statistics': {
                'total_payable': total_payable['total'] or 0,
                'total_paid': total_payable['paid'] or 0,
                'total_remaining': total_payable['remaining'] or 0,
            },
            'status_statistics': list(status_stats),
            'source_statistics': list(source_stats),
            'overdue_statistics': {
                'count': overdue_stats['count'] or 0,
                'amount': overdue_stats['amount'] or 0,
            },
            'supplier_statistics': list(supplier_stats),
        })

    @action(detail=False, methods=['get'])
    def export(self, request):
        """导出应付账本数据"""
        from django.http import HttpResponse
        import csv
        
        queryset = self.get_queryset()
        
        response = HttpResponse(content_type='text/csv; charset=utf-8-sig')
        response['Content-Disposition'] = 'attachment; filename="accounts_payable.csv"'
        
        writer = csv.writer(response)
        writer.writerow([
            '应付单号', '来源单号', '来源类型', '供应商', '应付金额', 
            '已付款金额', '欠款金额', '付款状态', '到期日期', '创建时间', '备注'
        ])
        
        for payable in queryset:
            writer.writerow([
                payable.payable_no,
                payable.source_order_no,
                payable.get_source_type_display(),
                payable.supplier.name,
                payable.payable_amount,
                payable.paid_amount,
                payable.remaining_amount,
                payable.get_payment_status_display(),
                payable.due_date.strftime('%Y-%m-%d') if payable.due_date else '',
                payable.create_date.strftime('%Y-%m-%d %H:%M:%S'),
                payable.remark or ''
            ])
        
        return response 
    

@method_decorator(csrf_exempt, name='dispatch')
class AccountsReceivableViewSet(SafeModelViewSet):
    """应收账本视图集"""
    queryset = AccountsReceivable.objects.all()
    serializer_class = AccountsReceivableSerializer
    pagination_class = StandardResultsSetPagination

    def get_serializer_class(self):
        """根据操作类型选择序列化器"""
        if self.action == 'list':
            return AccountsReceivableListSerializer
        return AccountsReceivableSerializer

    def get_queryset(self):
        """重写查询集，支持过滤和搜索"""
        queryset = super().get_queryset()
        
        # 获取查询参数
        customer_id = self.request.query_params.get('customer_id')
        source_type = self.request.query_params.get('source_type')
        payment_status = self.request.query_params.get('payment_status')
        search = self.request.query_params.get('search')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        overdue_only = self.request.query_params.get('overdue_only', 'false').lower() == 'true'
        
        # 应用过滤条件
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        if source_type:
            queryset = queryset.filter(source_type=source_type)
        
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)
        
        if search:
            queryset = queryset.filter(
                Q(receivable_no__icontains=search) |
                Q(source_order_no__icontains=search) |
                Q(customer__name__icontains=search) |
                Q(remark__icontains=search)
            )
        
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(create_date__date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(create_date__date__lte=end_date)
            except ValueError:
                pass
        
        if overdue_only:
            today = timezone.now().date()
            queryset = queryset.filter(
                Q(due_date__lt=today) & 
                Q(remaining_amount__gt=0)
            )
        
        return queryset.select_related('customer', 'handler', 'controller')

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取应收账本统计信息"""
        queryset = self.get_queryset()
        
        # 基础统计
        total_receivable = queryset.aggregate(
            total=Sum('receivable_amount'),
            received=Sum('received_amount'),
            remaining=Sum('remaining_amount')
        )
        
        # 按状态统计
        status_stats = queryset.values('payment_status').annotate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 按来源类型统计
        source_stats = queryset.values('source_type').annotate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 逾期统计
        today = timezone.now().date()
        overdue_queryset = queryset.filter(
            Q(due_date__lt=today) & 
            Q(remaining_amount__gt=0)
        )
        overdue_stats = overdue_queryset.aggregate(
            count=Count('id'),
            amount=Sum('remaining_amount')
        )
        
        # 按客户统计
        customer_stats = queryset.values('customer__name').annotate(
            count=Count('id'),
            receivable_amount=Sum('receivable_amount'),
            received_amount=Sum('received_amount'),
            remaining_amount=Sum('remaining_amount')
        ).order_by('-remaining_amount')[:10]
        
        return Response({
            'total_statistics': {
                'total_receivable': total_receivable['total'] or 0,
                'total_received': total_receivable['received'] or 0,
                'total_remaining': total_receivable['remaining'] or 0,
            },
            'status_statistics': list(status_stats),
            'source_statistics': list(source_stats),
            'overdue_statistics': {
                'count': overdue_stats['count'] or 0,
                'amount': overdue_stats['amount'] or 0,
            },
            'customer_statistics': list(customer_stats),
        })
 