from django.http import JsonResponse
from common.APIViewSafe import APIViewSafe
from WmsCore.utils.counter import Counter
from AccessGateway.control.auth import make_response
from common.logger import logger
from WmsCore.utils.jwt_auth import JWTWmsCoreAuthentication
from WmsCore.utils.permission import IsZTUser, IsLedgerPermission

class CounterViewSet(APIViewSafe):
    authentication_classes = [JWTWmsCoreAuthentication]
    permission_classes = [IsZTUser, IsLedgerPermission]

    def get(self, request):
        name = request.query_params.get('name', "")
        if name == "":
            return make_response(1, 'name is required')
        is_global = request.query_params.get('global', 0)
        if is_global == 1:
            counter = Counter.SysCounterInt(name, prefetch=True)
        else:
            counter = Counter.SysDayCounter(name, prefetch=True)
        
        return make_response(0, '', data={
            'counter': counter
        })
