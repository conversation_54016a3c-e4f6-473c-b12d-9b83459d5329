from rest_framework import permissions
from common.logger import logger as log;


class IsLedgerPermission(permissions.BasePermission):
    """
    自定义权限类，检查用户是否是账套权限
    """
    def has_permission(self, request, view):
        # 我的 enterprise_id 跟 请求的 enterprise_id 要一致
        if request.user_enterprise.id != request.ledger_enterprise.id:
            log.error(f"user: {request.user_id} enterprise: {request.user_enterprise} ledger_enterprise: {request.ledger_enterprise}")
            return False;
        return True;