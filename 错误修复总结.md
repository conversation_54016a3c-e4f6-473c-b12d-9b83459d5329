# 错误修复总结

## 错误描述
```
The field 'controller_name' was declared on serializer AccountsReceivableSerializer, but has not been included in the 'fields' option.
```

## 错误原因
1. `AccountsReceivableSerializer` 序列化器中声明了 `controller_name` 字段
2. 但在 `fields` 列表中没有包含 `controller_name`
3. 同时 `AccountsReceivable` 模型中缺少 `controller` 字段

## 修复方案

### 1. 添加 controller 字段到 AccountsReceivable 模型
在 `WmsCore/models.py` 中的 `AccountsReceivable` 模型添加：
```python
controller = TenantForeignKey('ZTUser', on_delete=models.PROTECT, verbose_name='操作者', related_name='accounts_receivable_controller')
```

### 2. 更新序列化器字段列表
在 `WmsCore/control/Accounts.py` 中的 `AccountsReceivableSerializer` 的 `fields` 列表添加 `'controller_name'`

### 3. 更新 AccountsAdmin.create_receivable 方法
添加 `controller` 参数，默认值为 `handler`：
```python
def create_receivable(customer, order_amount, receivable_amount, remaining_amount, handler, source_type, source_order_id, remark, controller=None, **foreign_order):
    # controller = controller or handler
```

### 4. 更新所有调用 create_receivable 的地方
在以下文件中添加 `controller=request.zt_user` 参数：
- `WmsCore/control/SalesOrder.py` (2处)
- `WmsCore/control/SalesOut.py` (3处)

## 修复后的一致性
现在 `AccountsReceivable` 模型与其他模型保持一致，都有：
- `handler`: 经办人
- `controller`: 操作者

## 需要的数据库迁移
由于添加了新字段 `controller`，需要创建数据库迁移：
```bash
python manage.py makemigrations
python manage.py migrate
```

## 测试建议
1. 测试销售订单的创建和更新
2. 测试销售出库的创建（零售和批发）
3. 确认应收账本记录正确创建
4. 确认序列化器返回数据包含 `controller_name` 字段

## 注意事项
- 新添加的 `controller` 字段是必填的，在迁移时需要为现有记录提供默认值
- 建议在迁移时将现有记录的 `controller` 设置为对应的 `handler` 值
