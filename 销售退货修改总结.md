# 销售退货修改总结

## 修改目标
销售退货表弃用，改用应付表（AccountsPayable），同时注意销售退货表字段也发生了更改（添加了customer字段）。

## 主要修改内容

### 1. 模型层修改

#### AccountsPayable 模型增强
- 添加了 `sales_return` 关联字段：
  ```python
  sales_return = TenantForeignKey('SalesReturn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联销售退货')
  ```
- 根据用户diff，应付表现在支持：
  - `supplier` 字段可为空（销售退货不涉及供应商）
  - `customer` 字段（用于记录退货的客户）

#### AccountsAdmin.create_payable 方法修复
- 修复了参数展开语法：`*foreign_order` → `**foreign_order`

### 2. 序列化器修改

#### SalesReturnListSerializer 增强
添加了应付账本相关字段：
- `payable_status`: 付款状态（通过应付账本查询）
- `payable_amount`: 应付金额（通过应付账本查询）

这些字段通过 `SerializerMethodField` 实现，查询逻辑：
```python
AccountsPayable.objects.filter(
    source_order_no=str(obj.id),
    source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
    sales_return=obj
).first()
```

### 3. 业务逻辑修改

#### 正式退货单创建
- **原来**: 只创建 `SalesReturn` 和 `SalesReturnItem`
- **现在**: 
  1. 创建 `SalesReturn` 和 `SalesReturnItem`
  2. 创建应付账本记录：
     ```python
     AccountsAdmin.create_payable(
         supplier=None,  # 销售退货不涉及供应商
         customer=customer,  # 使用customer字段记录客户
         source_type=AccountsPayable.SourceTypeChoices.RETURN_PAYABLE,
         payment_status=AccountsPayable.PaymentStatusChoices.PAID  # 立即付款
     )
     ```

#### 草稿退货单创建
- **保持不变**: 只创建 `SalesReturn` 和 `SalesReturnItem`，不创建应付账本记录

#### 草稿转正式功能（新增）
添加了 `convert_draft_to_formal` 方法：
1. 更新退货单状态：`is_draft = False`
2. 创建应付账本记录
3. 处理库存（为退货物品创建库存记录）
4. 返回完整的数据（包括应付账本信息）

#### 详情查询增强
- **原来**: 只返回退货单数据
- **现在**: 返回退货单数据 + 应付账本数据
  ```python
  {
      "sales_return": sales_return_data,
      "accounts_payable": accounts_payable_data
  }
  ```

#### 删除逻辑增强
- **原来**: 删除退货单和退货物品
- **现在**: 删除退货单、退货物品 + 关联的应付账本记录

### 4. 数据流变化

#### 原来的数据流
```
SalesReturn → SalesReturnItem
```

#### 现在的数据流
```
SalesReturn → SalesReturnItem
     ↓
AccountsPayable (应付账本)
```

### 5. 字段映射

| 退货单字段 | 应付账本字段 | 说明 |
|-----------|-------------|------|
| total_price | payable_amount | 应付金额 |
| customer | customer | 客户（新增字段） |
| - | supplier | 供应商（设为null） |
| id | source_order_no | 来源订单号 |
| - | source_type | 来源类型（RETURN_PAYABLE） |

### 6. API 变化

#### 列表接口
新增返回字段：
- `payable_status`: 付款状态
- `payable_amount`: 应付金额

#### 详情接口
返回数据结构变化：
```python
# 原来
{
    "sales_return": {...}
}

# 现在
{
    "sales_return": {...},
    "accounts_payable": {...}
}
```

#### 创建接口
返回数据增加应付账本信息：
```python
{
    "sales_return": {...},
    "sales_return_items": [...],
    "accounts_payable": {...}  # 新增
}
```

#### 更新接口
支持草稿转正式：
- 当 `is_draft=false` 且当前为草稿时，自动转为正式并创建应付账本

### 7. 业务规则

#### 付款状态
- 销售退货默认为立即付款状态（`PAID`）
- `remaining_amount = 0`（无欠款）

#### 供应商vs客户
- 销售退货中，客户是退货方，记录在 `customer` 字段
- `supplier` 字段设为 `null`（不涉及供应商）

#### 草稿vs正式
- **草稿**: 不创建应付账本记录，不处理库存
- **正式**: 创建应付账本记录，处理库存入库

### 8. 注意事项

1. **数据库迁移**: 需要为 `AccountsPayable` 添加 `sales_return` 字段
2. **向后兼容**: 前端需要适应新的返回数据结构
3. **权限控制**: 草稿转正式可能需要特殊权限
4. **库存处理**: 正式退货会影响库存，需要谨慎操作

## 测试建议

1. **功能测试**:
   - 测试草稿退货单的创建和修改
   - 测试正式退货单的创建
   - 测试草稿转正式的流程
   - 测试退货单的删除

2. **数据完整性测试**:
   - 确认应付账本记录正确创建
   - 确认库存处理正确
   - 确认删除时关联数据正确清理

3. **API测试**:
   - 测试列表接口的新字段
   - 测试详情接口的新数据结构
   - 测试创建和更新接口的返回数据
