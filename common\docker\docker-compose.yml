version: '3.1'

include:
  - env_file: ./.env

services:
  wms_app:
    image: dreg.fatea.net/images/wms_app:latest
    container_name: ${PROJ_NAME:-wms}_app
    environment:
      - DJANGO_ENV=${DJANGO_ENV:-prod}
    ports:
      - "8000:8000"
    volumes:
      - ${WORK_DIR:-.}/config:/app/server/config
      - ${WORK_DIR:-.}/static:/app/server/static
      - ${WORK_DIR:-.}/logs:/app/server/logs
    networks:
      - wms_network
    restart: always

  # Certbot service
  # use `docker compose --profile certbot up` to start the certbot service.
  certbot:
    image: dreg.fatea.net/images/certbot
    container_name: ${PROJ_NAME:-wms}_certbot
    profiles:
      - certbot
    volumes:
      - ${WORK_DIR:-.}/proxy/certbot/conf:/etc/letsencrypt
      - ${WORK_DIR:-.}/proxy/certbot/www:/var/www/html
      - ${WORK_DIR:-.}/proxy/certbot/logs:/var/log/letsencrypt
      - ${WORK_DIR:-.}/proxy/certbot/conf/live:/etc/letsencrypt/live
      - ${WORK_DIR:-.}/proxy/certbot/update-cert.template.txt:/update-cert.template.txt
      - ${WORK_DIR:-.}/proxy/certbot/docker-entrypoint.sh:/docker-entrypoint.sh
    environment:
      - CERTBOT_EMAIL=${CERTBOT_EMAIL}
      - CERTBOT_DOMAIN=${CERTBOT_DOMAIN}
      - CERTBOT_OPTIONS=${CERTBOT_OPTIONS:-}
    entrypoint: [ '/docker-entrypoint.sh' ]
    command: [ 'tail', '-f', '/dev/null' ]
    networks:
      - wms_network

  # The nginx reverse proxy.
  # used for reverse proxying the API service and Web service.
  nginx:
    image: dreg.fatea.net/images/nginx:latest
    container_name: ${PROJ_NAME:-wms}_nginx
    restart: always
    volumes:
      - ${WORK_DIR:-.}/proxy/nginx/nginx.conf.template:/etc/nginx/nginx.conf.template
      - ${WORK_DIR:-.}/proxy/nginx/proxy.conf.template:/etc/nginx/proxy.conf.template
      - ${WORK_DIR:-.}/proxy/nginx/https.conf.template:/etc/nginx/https.conf.template
      - ${WORK_DIR:-.}/proxy/nginx/conf.d:/etc/nginx/conf.d
      - ${WORK_DIR:-.}/proxy/nginx/docker-entrypoint.sh:/docker-entrypoint-mount.sh
      - ${WORK_DIR:-.}/proxy/nginx/ssl:/etc/ssl # cert dir (legacy)
      - ${WORK_DIR:-.}/proxy/certbot/conf/live:/etc/letsencrypt/live # cert dir (with certbot container)
      - ${WORK_DIR:-.}/proxy/certbot/conf:/etc/letsencrypt
      - ${WORK_DIR:-.}/proxy/certbot/www:/var/www/html
    entrypoint: [ 'sh', '-c', "cp /docker-entrypoint-mount.sh /docker-entrypoint.sh && sed -i 's/\r$$//' /docker-entrypoint.sh && chmod +x /docker-entrypoint.sh && /docker-entrypoint.sh" ]
    environment:
      NGINX_SERVER_NAME: ${NGINX_SERVER_NAME:-_}
      NGINX_HTTPS_ENABLED: ${NGINX_HTTPS_ENABLED:-false}
      NGINX_SSL_PORT: ${NGINX_SSL_PORT:-443}
      NGINX_PORT: ${NGINX_PORT:-80}
      # You're required to add your own SSL certificates/keys to the `./nginx/ssl` directory
      # and modify the env vars below in .env if HTTPS_ENABLED is true.
      NGINX_SSL_CERT_FILENAME: ${NGINX_SSL_CERT_FILENAME:-cxz.crt}
      NGINX_SSL_CERT_KEY_FILENAME: ${NGINX_SSL_CERT_KEY_FILENAME:-cxz.key}
      NGINX_SSL_PROTOCOLS: ${NGINX_SSL_PROTOCOLS:-TLSv1.1 TLSv1.2 TLSv1.3}
      NGINX_WORKER_PROCESSES: ${NGINX_WORKER_PROCESSES:-auto}
      NGINX_CLIENT_MAX_BODY_SIZE: ${NGINX_CLIENT_MAX_BODY_SIZE:-15M}
      NGINX_KEEPALIVE_TIMEOUT: ${NGINX_KEEPALIVE_TIMEOUT:-65}
      NGINX_PROXY_READ_TIMEOUT: ${NGINX_PROXY_READ_TIMEOUT:-3600s}
      NGINX_PROXY_SEND_TIMEOUT: ${NGINX_PROXY_SEND_TIMEOUT:-3600s}
      NGINX_ENABLE_CERTBOT_CHALLENGE: ${NGINX_ENABLE_CERTBOT_CHALLENGE:-false}
      CERTBOT_DOMAIN: ${CERTBOT_DOMAIN:-}
    depends_on:
      - wms_app
    ports:
      - '${EXPOSE_NGINX_PORT:-80}:${NGINX_PORT:-80}'
      - '${EXPOSE_NGINX_SSL_PORT:-443}:${NGINX_SSL_PORT:-443}'
    networks:
      - wms_network

networks:
  wms_network:
    driver: bridge


