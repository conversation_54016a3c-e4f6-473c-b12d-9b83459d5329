from django.db import models, transaction
from rest_framework import status
from rest_framework.decorators import action

from WmsCore.control.base import SafeModelViewSet
from WmsCore.models import Supplier, CatalogTypeTree
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from rest_framework import serializers
from WmsCore.control.base import StandardResultsSetPagination
from common.exception import WmsException
from common.make_response import make_response
from django.utils.translation import gettext_lazy as _
@SerializerCollector(alias="供应商")
class SupplierSerializer(serializers.ModelSerializer):
    supplier_type_name = serializers.CharField(source='supplier_type.name', read_only=True)
    contact_person=serializers.CharField(required=False, allow_blank=True,label=_('联系人'))
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'phone', 'address', 'supplier_type',
            'create_date', 'last_contact_date', 'last_supply_date', 'supplier_type_name'
        ]
        read_only_fields = ['create_date']
    
    def validate_supplier_type(self, value):
        """验证supplier_type字段"""
        if value is not None:
            # 验证传入的supplier_type必须是供应商类型
            if value.for_type != CatalogTypeTree.ForTypeChoices.SUPPLIER:
                raise WmsException(_('供应商类型必须是供应商分类'))
        return value
    
    def validate(self, attrs):
        """验证并设置默认的supplier_type"""
        supplier_type = attrs.get('supplier_type')
        
        # 如果没有传入supplier_type，则使用默认的第一个供应商分类
        if supplier_type is None:
            default_supplier_type = CatalogTypeTree.objects.filter(
                for_type=CatalogTypeTree.ForTypeChoices.SUPPLIER
            ).first()
            
            if default_supplier_type:
                attrs['supplier_type'] = default_supplier_type
            else:
                raise WmsException(_('系统中没有可用的供应商分类'))
        
        return attrs

class SupplierViewSet(SafeModelViewSet):
    """
    供应商的增删改查接口
    """
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    pagination_class = StandardResultsSetPagination

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新供应商"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个供应商详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新供应商信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('更新成功'), data=serializer.data, status=status.HTTP_200_OK)

    def destroy(self, request, pk=None, **kwargs):
        """删除供应商"""
        instance = self.get_object()
        instance.delete()
        return make_response(code=0, msg=_('删除成功'), data={}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索供应商"""
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=0, msg=_('获取成功'), data={}, status=status.HTTP_200_OK)
        suppliers = Supplier.objects.filter(
            models.Q(name__icontains=query) |
            models.Q(contact_person__icontains=query) |
            models.Q(phone__icontains=query) |
            models.Q(address__icontains=query)
        )
        serializer = self.get_serializer(suppliers, many=True)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)
