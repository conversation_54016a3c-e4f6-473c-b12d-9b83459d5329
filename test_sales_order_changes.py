#!/usr/bin/env python3
"""
测试销售订单修改是否正确
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正确"""
    try:
        from WmsCore.control.SalesOrder import SalesOrderViewSet, SalesOrderSerializer
        from WmsCore.models import AccountsReceivable, ReceiptRecord
        from WmsCore.admin.Accounts import AccountsAdmin
        print("✓ 所有导入都成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_serializer_fields():
    """测试序列化器字段是否正确"""
    try:
        from WmsCore.control.SalesOrder import SalesOrderSerializer
        
        # 检查序列化器是否包含正确的字段
        serializer = SalesOrderSerializer()
        fields = serializer.fields.keys()
        
        required_fields = ['receipt_status', 'receivable_amount', 'received_amount', 'unpaid_amount']
        for field in required_fields:
            if field not in fields:
                print(f"✗ 缺少字段: {field}")
                return False
        
        print("✓ 序列化器字段检查通过")
        return True
    except Exception as e:
        print(f"✗ 序列化器字段检查失败: {e}")
        return False

def test_accounts_admin():
    """测试 AccountsAdmin 的 create_receivable 方法"""
    try:
        from WmsCore.admin.Accounts import AccountsAdmin
        import inspect
        
        # 检查 create_receivable 方法的签名
        sig = inspect.signature(AccountsAdmin.create_receivable)
        params = list(sig.parameters.keys())
        
        # 第一个参数应该是 customer 而不是 supplier
        if 'customer' not in params:
            print("✗ create_receivable 方法缺少 customer 参数")
            return False
        
        if 'supplier' in params:
            print("✗ create_receivable 方法仍然包含 supplier 参数")
            return False
            
        print("✓ AccountsAdmin.create_receivable 方法签名正确")
        return True
    except Exception as e:
        print(f"✗ AccountsAdmin 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试销售订单修改...")
    
    tests = [
        test_imports,
        test_serializer_fields,
        test_accounts_admin,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("❌ 有测试失败，请检查修改")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
