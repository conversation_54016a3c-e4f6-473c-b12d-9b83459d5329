from rest_framework import serializers
from .models import SalesOrder, SalesOrderItem, SalesOut, SalesOutItem, Payment, PurchasePayment


class SalesOrderSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)

    class Meta:
        model = SalesOrder
        fields = [
            'id', 'customer', 'customer_name', 'order_date', 'item_count',
            'total_amount', 'discount', 'expected_delivery_date', 'order_status', 'payment_status'
        ]

class SalesOrderItemSerializer(serializers.ModelSerializer):
    sales_order_id = serializers.CharField(source='sales_order.id', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)

    class Meta:
        model = SalesOrderItem
        fields = [
            'id', 'sales_order', 'sales_order_id', 'item', 'item_name',
            'quantity', 'in_quantity', 'sale_price', 'discount'
        ]

class SalesOutSerializer(serializers.ModelSerializer):
    sales_order_id = serializers.CharField(source='sales_order.id', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)

    class Meta:
        model = SalesOut
        fields = [
            'id', 'sales_order', 'sales_order_id', 'handler', 'out_date',
            'warehouse', 'warehouse_name', 'total_cost'
        ]

class SalesOutItemSerializer(serializers.ModelSerializer):
    sales_out_id = serializers.CharField(source='sales_out.id', read_only=True)
    stock_id = serializers.CharField(source='stock.id', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)

    class Meta:
        model = SalesOutItem
        fields = [
            'id', 'sales_out', 'sales_out_id', 'stock', 'stock_id', 'item', 'item_name',
            'quantity', 'out_cost', 'final_cost'
        ]

class PaymentSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_order_id = serializers.CharField(source='sales_order.id', read_only=True)

    class Meta:
        model = Payment
        fields = [
            'id', 'customer', 'customer_name', 'sales_order', 'sales_order_id',
            'amount', 'handler', 'payment_date'
        ]

class PurchasePaymentSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    purchase_order_id = serializers.CharField(source='purchase_order.id', read_only=True)
    purchase_in_id = serializers.CharField(source='purchase_in.id', read_only=True)

    class Meta:
        model = PurchasePayment
        fields = [
            'id', 'supplier', 'supplier_name', 'purchase_order', 'purchase_order_id',
            'purchase_in', 'purchase_in_id', 'amount', 'handler', 'payment_date',
            'payment_type', 'remark'
        ]

    def validate(self, attrs):
        payment_type = attrs.get('payment_type')
        purchase_in = attrs.get('purchase_in')
        # 采购入库单不为空时，不能是定金
        if purchase_in and payment_type == 'deposit':
            raise serializers.ValidationError('采购入库单不为空时，不能选择定金')
        # 如果不是定金，必须要有入库单
        if payment_type != 'deposit' and not purchase_in:
            raise serializers.ValidationError('非定金类型必须选择采购入库单')
        return attrs
