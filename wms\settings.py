"""
Django settings for wms project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
import time
import yaml
from typing import Any, Dict

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

def load_yaml_config(config_path: str) -> Dict[str, Any]:
    """
    加载YAML配置文件
    :param config_path: 配置文件路径
    :return: 配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"警告: 配置文件 {config_path} 不存在，将使用默认配置")
        return {}
    except yaml.YAMLError as e:
        print(f"错误: 配置文件 {config_path} 格式错误: {e}")
        return {}

# 加载环境配置
ENV = os.getenv('DJANGO_ENV', 'dev')
CONFIG_PATH = os.path.join(BASE_DIR, f'config/{ENV}.yml')
CONFIG = load_yaml_config(CONFIG_PATH)

#开发模式
DEVELOPMENT = CONFIG.get('development', False)
# 调试模式配置
DEBUG = CONFIG.get('debug', False)
# 核心配置项
SECRET_KEY = CONFIG.get('secret_key', 'django-insecure-nlihxobw)0&po4!4#=5v&(+=vnzs2vali)$ao8q2*051r)1s6_')

# JWT配置
JWT_CONFIG = CONFIG.get('jwt', {})
JWT_SECRET_KEY = JWT_CONFIG.get('secret_key', SECRET_KEY)
JWT_ALGORITHM = JWT_CONFIG.get('algorithm', 'HS256')
JWT_EXPIRATION = JWT_CONFIG.get('expiration', 30)
JWT_REFRESH_EXPIRATION = JWT_CONFIG.get('refresh_expiration', 90)

# 第三方平台配置
WECHAT_CONFIG = CONFIG.get('wechat', {})
WECHAT_APPID = WECHAT_CONFIG.get('appid', 'your_wechat_appid')
WECHAT_SECRET = WECHAT_CONFIG.get('secret', 'your_wechat_secret')

ALIPAY_CONFIG = CONFIG.get('alipay', {})
ALIPAY_APPID = ALIPAY_CONFIG.get('appid', 'your_alipay_appid')
ALIPAY_PRIVATE_KEY = ALIPAY_CONFIG.get('private_key', 'your_alipay_private_key')
ALIPAY_PUBLIC_KEY = ALIPAY_CONFIG.get('public_key', 'your_alipay_public_key')

MANAGE_SERVER_CONFIG = CONFIG.get('manage_server', {})
MANAGE_SERVER_CHECK_KEY = MANAGE_SERVER_CONFIG.get('check_key', 'your_manage_server_check_key')
MANAGE_SERVER_CHECK_KEY2 = MANAGE_SERVER_CONFIG.get('check_key2', 'your_manage_server_check_key2')


# 允许的主机配置
ALLOWED_HOSTS = CONFIG.get('allowed_hosts', ['************', 'localhost', '127.0.0.1', '*************', '*************'])

# Application definition
INSTALLED_APPS = (
    'AccessGateway.apps.AccessgatewayConfig',
    'WmsCore.apps.WmscoreConfig',
    'django.contrib.contenttypes',
    'django.contrib.auth',
    'django.contrib.sites',
    'django.contrib.staticfiles',
)


TENANT_PATH_PREFIX = CONFIG.get('tenant', {}).get('path_prefix', 'ztx')


MIDDLEWARE = [
    'common.middleware.tenant_midware.TenantMainMiddleware',
    'common.middleware.request_user.RequestUserMiddleware',
    'common.middleware.access_log_fmt.AccessLogMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'wms.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
            ],
        },
    },
]

WSGI_APPLICATION = 'wms.wsgi.application'
ASGI_APPLICATION = 'wms.asgi.application'

# 数据库配置
DATABASE_CONFIG = CONFIG.get('database', {})
DATABASES = {
    'default': {
        'ENGINE': DATABASE_CONFIG.get('engine', 'django_multitenant.backends.postgresql'),
        'NAME': DATABASE_CONFIG.get('name', 'fs_dj'),
        'USER': DATABASE_CONFIG.get('user', 'pger'),
        'PASSWORD': DATABASE_CONFIG.get('password', 'pger_pass'),
        'HOST': DATABASE_CONFIG.get('host', '*************'),
        'PORT': DATABASE_CONFIG.get('port', '5432'),
        'SCHEMA': DATABASE_CONFIG.get('schema', 'public'),
    }
}

# Redis配置
REDIS_CONFIG = CONFIG.get('redis', {})
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_CONFIG.get('location', 'redis://127.0.0.1:6379'),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

# 若需要使用 Redis 作为会话存储
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

# AUTH_PASSWORD_VALIDATORS = [
#     {
#         'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
#     },
#     {
#         'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
#     },
#     {
#         'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
#     },
#     {
#         'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
#     },
# ]

# 国际化配置
LANGUAGE_CODE = CONFIG.get('language_code', 'en-us')
TIME_ZONE = CONFIG.get('time_zone', 'UTC')
USE_I18N = True
USE_TZ = True

# 静态文件配置
STATIC_URL = CONFIG.get('static_url', 'static/')
STATIC_ROOT = os.path.join(BASE_DIR, CONFIG.get('static_root', 'static'))

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型
AUTH_USER_MODEL = 'AccessGateway.CXZUser'

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(STATIC_ROOT, 'media')


REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'common.exception.custom_exception_handler',
}
