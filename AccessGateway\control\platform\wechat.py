
import requests

from common.db.Redis import RedisUtil
from django.conf import settings
from common.logger import logger 

class WechatPlatform:
    def login(self):
        pass

    def get_user_info(self):
        pass

    @staticmethod
    def GetOpenId(code):
        # access_token = WechatPlatform.GetAccessToken();
        # if access_token == "":
        #     return "";

        url = "https://api.weixin.qq.com/sns/jscode2session";
        url = f"{url}?appid={settings.WECHAT_APPID}&secret={settings.WECHAT_SECRET}&js_code={code}&grant_type=authorization_code"
        # params = {
        #     "js_code": code,
        #     "appid": settings.WECHAT_APPID,
        #     "secret": settings.WECHAT_SECRET,
        #     "grant_type": "authorization_code"
        # }

        response = requests.get(url )
        rsp = response.json()
        logger.debug(f"wechat GetOpenId rsp: {rsp} url: {url}")
        if rsp.get("errcode", 0) == 0:
            #succ
            openid = rsp.get("openid", "");
            logger.debug(f"wechat GetOpenId openid: {openid}")
            return openid;
        else:
            logger.error(f"wechat GetOpenId error code: {rsp.get('errcode', 0)} error: {rsp.get('errmsg', '')}")
            raise Exception(f"wechat GetOpenId error code: {rsp.get('errcode', 0)} ");

    @staticmethod
    def GetPhoneNumber(code):
        access_token = WechatPlatform.GetAccessToken();
        if access_token == "":
            return "";
        
        url = f"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}"
        data = {
            "code": code
        }
        response = requests.post(url, json=data)
        rsp = response.json()
        logger.debug(f"wechat GetPhoneNumber rsp: {rsp} code: {code}")
        if rsp.get("errcode", 0) == 0:
            phone_number = rsp.get("phone_info", {}).get("phoneNumber", "");
            logger.debug(f"wechat GetPhoneNumber phone_number: {phone_number}")
            return phone_number;
        else:
            logger.error(f"wechat GetPhoneNumber error code: {rsp.get('errcode', 0)} error: {rsp.get('errmsg', '')}")
            raise Exception(f"wechat GetPhoneNumber error code: {rsp.get('errcode', 0)} ");


    @staticmethod
    def GetAccessToken():
        #获得微信小程序的access_token
        #url = https://api.weixin.qq.com/cgi-bin/token
        access_token = RedisUtil.get("wechat_access_token");
        if access_token and access_token != "":
            return access_token;

        url = "https://api.weixin.qq.com/cgi-bin/token";
        params = {
            "grant_type": "client_credential",
            "appid": settings.WECHAT_APPID,
            "secret": settings.WECHAT_SECRET
        }

        response = requests.get(url, params=params)
        rsp = response.json()
        logger.debug(f"wechat GetAccessToken rsp: {rsp} ")

        access_token = rsp.get("access_token", "");
        expires_in = rsp.get("expires_in", 0);
        if expires_in > 300:
            #提前一点过期
            expires_in -= 300;
        if expires_in > 0:
            RedisUtil.set("wechat_access_token", access_token, expires_in);
        return access_token;