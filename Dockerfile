# 第一阶段：构建依赖层（单独镜像）
FROM dreg.fatea.net/dhub/library/python:3.12-alpine AS dependencies

# 设置工作目录
WORKDIR /app

# 仅复制 requirements.txt，用于缓存依赖安装
COPY requirements.txt .

# 安装依赖（使用阿里云镜像加速）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirements.txt

# 第二阶段：构建应用层（基于依赖层）
FROM dependencies AS runtime

# 设置工作目录
WORKDIR /app/server

# 复制应用代码（仅在代码变更时触发）
COPY . .

# 暴露服务端口（根据实际应用修改）
EXPOSE 8000

# 启动命令
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
