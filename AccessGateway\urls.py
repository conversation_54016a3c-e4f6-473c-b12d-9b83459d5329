from django.urls import path

import AccessGateway.control.enterprise
import AccessGateway.control.cxz_login
import AccessGateway.control.login
import AccessGateway.control.tenant
import AccessGateway.control.members
import AccessGateway.control.server_mgr
from WmsCore.views import Model2XlsAPI

from . import views
from .control import asr

urlpatterns = [
    path('', views.index, name='index'),
    path('asr', asr.asr_request, name='asr'),
    path('asr_bd', asr.asr_request_bytedance, name='asr_bytedance'),
    
    # 平台账号相关API
    path('platform/login/', AccessGateway.control.login.LoginView.as_view(), name='platform_login'),
    path('platform/login_bdph/', AccessGateway.control.login.LoginBindPhoneView.as_view(), name='platform_bind_phone'),
    # post delete
    path('invite/',AccessGateway.control.members.InviteCreateAPIView.as_view(), name='invite'),
    path('inviteto/',AccessGateway.control.members.InviteToAPIView.as_view(), name='invite'),
    path('member/',AccessGateway.control.members.EnterpriseMemberAlterAPIView.as_view(), name='member'),
    # 正式登陆，但是，其实就是获取账套数据信息
    path('login/',AccessGateway.control.cxz_login.CxzLoginAPIView.as_view(), name='cxz_login'),
    # 令牌相关API
    # path('token/', views.TokenView.as_view(), name='token'),
    path('token/refresh/', views.TokenView.as_view(), name='token_refresh'),


    #path('platform/info/', views.PlatformAccountInfoAPIView.as_view(), name='platform_info'),
    #path('platform/uni/', AccessGateway.control.login_register.UnifiedLoginRegistrationAPIView.as_view(), name='unified_login_register'),
    #公司相关
    #path('company/', AccessGateway.control.enterprise.EnterpriseAPI.as_view(), name='company'),
    # TODO 第三方平台验证API

    # TODO 系统账号相关API

    # 账套相关API,预分配账套
    path('tenant/', AccessGateway.control.tenant.CreateTenantAPIView.as_view(), name='tenant'),
    #TODO 测试指令，后续得删除
    # path('tenant/check/', AccessGateway.control.tenant.CheckTenantCapacityAPIView.as_view(), name='tenant_check'),
    # 服务管理
    path('server/<str:action>/', AccessGateway.control.server_mgr.ServerMgrView.as_view(), name='server_mgr'),

    #测试
    path('test/', AccessGateway.control.tenant.TenantToken.as_view(), name='test'),
    path("s2x/", Model2XlsAPI.as_view(), name="s2xls"),

]