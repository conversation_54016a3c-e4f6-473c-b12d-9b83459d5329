"""
ASGI config for wms project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from AccessGateway import ws_router

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wms.settings')

#application = get_asgi_application()

application = ProtocolTypeRouter({
    # 这里处理http路由
    "http": get_asgi_application(),
    # 这里处理WebSocket路由
    # rontings.py的websocket_urlpatterns
    "websocket": URLRouter(ws_router.websocket_urlpatterns),
})