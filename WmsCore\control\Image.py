import hashlib
import io
import os
import shutil
import tempfile

from PIL import Image
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import connection
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from django.conf import settings

from AccessGateway.control.auth import make_response
from WmsCore.utils.counter import Counter
from common.logger import logger 
from common.APIViewSafe import APIViewSafe
from common.make_response import make_response
from WmsCore.utils.jwt_auth import JWTWmsCoreAuthentication
from WmsCore.utils.permission import IsZTUser, IsLedgerPermission
from rest_framework import serializers
import json


class ImageViewSet(APIViewSafe):
    """
    上传图片返回图片路径和URL
    按照scheme分目录，生成缩略图，校验大小，md5
    """
    authentication_classes = [JWTWmsCoreAuthentication]
    permission_classes = [IsZTUser, IsLedgerPermission]

    @staticmethod
    def get_save_path(schema_name:str):
        save_path = settings.MEDIA_ROOT or default_storage.location;
        return os.path.join(save_path, schema_name, 'images');

    @staticmethod 
    def get_img_dir(md5_hash:str, schema_name:str ):
        base_img_dir = ImageViewSet.get_save_path(schema_name);
        img_dir = os.path.join(base_img_dir, 'img', md5_hash[0:2], md5_hash[2:4]);
        thumb_dir = os.path.join(base_img_dir, 'thumb', md5_hash[0:2], md5_hash[2:4]);
        return img_dir, thumb_dir;

    @staticmethod
    def get_img_uri(schema_name:str, filename:str, thumb:bool=False):
        if thumb:
            uri = f"{settings.MEDIA_URL}/{schema_name}/images/thumb/{filename[0:2]}/{filename[2:4]}/{filename}"
        else:
            uri = f"{settings.MEDIA_URL}/{schema_name}/images/img/{filename[0:2]}/{filename[2:4]}/{filename}"
        return uri;

    def post(self, request):
        image = request.FILES.get('image')
        if not image:
            return make_response(code=1, msg=_('请上传图片'), status=status.HTTP_400_BAD_REQUEST)
        if image.size > 1024 * 1024:
            return make_response(code=1, msg=_('图片不能超过1MB'), status=status.HTTP_400_BAD_REQUEST)
        if image.size <  2* 1024:
            return make_response(code=1, msg=_('图片不能小于2KB'), status=status.HTTP_400_BAD_REQUEST)
        # 生成一个临时的文件，保存图片
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        for chunk in image.chunks():
            temp_file.write(chunk)
        temp_file.seek(0)
        md5_hash = hashlib.md5(temp_file.read()).hexdigest()
        temp_file.close()
        img_path = None
        thumb_path = None
        try:
            #schema_name = getattr(connection, 'schema_name', 'public')
            schema_name = getattr(request, 'tenant_name', 'public')
            ext = os.path.splitext(image.name)[-1].lower() or '.jpg'
            #num = Counter.CounterInt("IMG")
            filename = f"{md5_hash}{ext}"
            img_dir, thumb_dir = ImageViewSet.get_img_dir(md5_hash, schema_name);
            os.makedirs(img_dir, exist_ok=True)
            os.makedirs(thumb_dir, exist_ok=True)
            img_path = os.path.join(img_dir, filename).replace('\\', '/')
            # 先存缩略图
            img = Image.open(temp_file.name)
            try:
                resample = Image.Resampling.LANCZOS
            except AttributeError:
                resample = Image.LANCZOS
            img.thumbnail((256, 256), resample)
            thumb_io = io.BytesIO()
            img_format = img.format if img.format else 'JPEG'
            img.save(thumb_io, format=img_format)
            thumb_filename = filename
            thumb_path = os.path.join(thumb_dir, thumb_filename).replace('\\', '/')
            logger.debug(f"thumb_path: {thumb_path} exists: {os.path.exists(thumb_path)} img_path: {img_path} exists: {os.path.exists(img_path)}")
            if not os.path.exists(thumb_path):
                # thumb_io 写入thumb_path
                thumb_io.seek(0)
                with open(thumb_path, 'wb') as f:
                    f.write(thumb_io.getvalue())
            img.close()
            # 存原图
            if not os.path.exists(img_path):
                # 临时文件移动到img_path
                shutil.move(temp_file.name, img_path)
            else:
                # 删除临时文件
                os.remove(temp_file.name)
            #image_url = ImageViewSet.get_img_uri(schema_name, filename)
            #thumb_url = ImageViewSet.get_img_uri(schema_name, filename, True)
            # 仅返回保存的图片文件名称，获取的时候，再动态组合图片路径
            return make_response(code=0, msg=_('上传成功'), data={
                'md5': md5_hash,
                'image': filename,
                #'thumbnail': thumb_path,
                #'image_url': image_url,
                #'thumbnail_url': thumb_url
            }, status=status.HTTP_201_CREATED)
        except Exception as e:
            if img_path and os.path.exists(img_path):
                os.remove(img_path)
            if thumb_path and os.path.exists(thumb_path):
                os.remove(thumb_path)
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)
            return make_response(code=1, msg=f"{_('图片上传失败')}: {str(e)}",
                                 status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request):
        md5 = request.query_params.get('md5')
        if not md5:
            return make_response(code=1, msg=_('md5不能为空'), status=status.HTTP_400_BAD_REQUEST)
        ext = request.query_params.get('ext', 'jpg')
        
        schema_name = connection.schema_name
        img_dir, thumb_dir = ImageViewSet.get_img_dir(md5, schema_name)
        
        # 先检查.jpg文件
        jpg_file = f"{md5}.{ext}"
        jpg_path = os.path.join(img_dir, jpg_file).replace('\\', '/')
        if os.path.exists(jpg_path):
            return make_response(code=0, msg=_('图片存在'), data={
                'md5': md5,
                'filename': jpg_file
            }, status=status.HTTP_200_OK)
        elif ext != 'jpg':
            jpg_file = f"{md5}.jpg"
            jpg_path = os.path.join(img_dir, jpg_file).replace('\\', '/')
            if os.path.exists(jpg_path):
                return make_response(code=0, msg=_('图片存在'), data={
                    'md5': md5,
                    'filename': jpg_file
                }, status=status.HTTP_200_OK)
        
        return make_response(code=1, msg=_('图片不存在'), status=status.HTTP_404_NOT_FOUND)


# 多图片列表的 json 字段
class MultiImagesField(serializers.JSONField):
    """处理图片列表的自定义字段"""
    def to_representation(self, value):
        """将数据库中的图片列表转换为带有完整URL的列表"""
        if not value:
            return []
        # 从序列化器上下文中获取 request
        request = self.context.get('request')
        if not request:
            return value
        # 从 request 中获取 schema_name
        schema_name = getattr(request, 'tenant_name', 'public')
        #通过connection获取schema_name
        # from django.db import connection
        # schema_name = getattr(connection, 'schema_name', 'public')
        
        result = []
        for image_name in value:
            if isinstance(image_name, str):
                img = ImageViewSet.get_img_uri(schema_name, image_name, False)
                thumb = ImageViewSet.get_img_uri(schema_name, image_name, True)
                img = request.build_absolute_uri(img) if request else img
                thumb = request.build_absolute_uri(thumb) if request else thumb
                result.append({
                    'name': image_name,
                    'img': img,
                    'thumb': thumb
                })
        return result

    def to_internal_value(self, data):
        """处理前端提交的图片列表数据"""
        if not data:
            return []
            
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                raise serializers.ValidationError(_('图片列表格式错误'))
        if not isinstance(data, list):
            raise serializers.ValidationError(_('图片列表必须是数组'))
        if len(data) > 4:
            raise serializers.ValidationError(_('最多只能上传4张图片'))
            
        result = []
        for item in data:
            if isinstance(item, dict):
                name = item.get('name')
                if name:
                    result.append(name)
            elif isinstance(item, str):
                result.append(item)
                
        return result

# 单张图片的char字段
class ImageField(serializers.CharField):
    """处理单张图片的自定义字段"""
    def to_representation(self, value):
        """将数据库中的图片名称转换为带有完整URL的字典"""
        if not value:
            return None
            
        # 从序列化器上下文中获取 request
        request = self.context.get('request')
        if not request:
            return value
            
        # 从 request 中获取 schema_name
        schema_name = getattr(request, 'tenant_name', 'public')
        
        # 生成图片URL
        img = ImageViewSet.get_img_uri(schema_name, value, False)
        thumb = ImageViewSet.get_img_uri(schema_name, value, True)
        img = request.build_absolute_uri(img) if request else img
        thumb = request.build_absolute_uri(thumb) if request else thumb
        
        return {
            'name': value,
            'img': img,
            'thumb': thumb
        }

    def to_internal_value(self, data):
        """处理前端提交的图片数据"""
        if not data:
            return None
            
        if isinstance(data, str):
            return data
            
        if isinstance(data, dict):
            name = data.get('name')
            if name:
                return name
                
        raise serializers.ValidationError(_('图片格式错误'))