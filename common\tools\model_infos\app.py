from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.templating import <PERSON><PERSON><PERSON><PERSON>emplates
from fastapi.staticfiles import StaticFiles
import requests
import json
from pathlib import Path

app = FastAPI()

# 设置静态文件和模板目录
BASE_DIR = Path(__file__).resolve().parent
templates = Jinja2Templates(directory=str(BASE_DIR / "templates"))
app.mount("/static", StaticFiles(directory=str(BASE_DIR / "static")), name="static")


# Host 配置
HOSTS_MAP = {
    "洁彪": "http://************:8000",
    "厚华": "http://*************:8000",
    "测试服务器": "http://*************:8000"
}
default_host_name = list(HOSTS_MAP.keys())[0]

# API 路径
LEDGER = "CtVSPL9a3r"
#API_PATH = f"/ztx/{LEDGER}/s2x/"
API_PATH = f"/api/s2x/"

@app.get("/")
async def home(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "hosts": HOSTS_MAP})

@app.get("/api/hosts")
async def get_hosts():
    return {"hosts": HOSTS_MAP}

@app.get("/api/models")
async def get_models(host: str):
    try:
        url = f"{HOSTS_MAP.get(host, HOSTS_MAP[default_host_name])}{API_PATH}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

@app.get("/api/model/{model_name}")
async def get_model_details(model_name: str, host: str):
    try:
        url = f"{HOSTS_MAP.get(host, HOSTS_MAP[default_host_name])}{API_PATH}?model={model_name}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)
