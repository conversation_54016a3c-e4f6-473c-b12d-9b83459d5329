# 开发环境配置

# 安全配置
secret_key: 'django-insecure-nlihxobw)0&po4!4#=5v&(+=vnzs2vali)$ao8q2*051r)1s6_'
development: true

# JWT配置
jwt:
  #secret_key: 'your-jwt-secret-key' 不配置默认使用secret_key
  algorithm: 'HS256'
  expiration: 30
  refresh_expiration: 90

# 微信配置
wechat:
  appid: 'your_wechat_appid'
  secret: 'your_wechat_secret'

# 支付宝配置
alipay:
  appid: 'your_alipay_appid'
  private_key: 'your_alipay_private_key'
  public_key: 'your_alipay_public_key'

# 调试模式
debug: true

# 允许的主机
allowed_hosts:
  - '************'
  - 'localhost'
  - '127.0.0.1'
  - '*************'
  - '*************'

# 租户配置
tenant:
  path_prefix: 'ztx'


# 数据库配置
database:
  engine: 'django_multitenant.backends.postgresql'
  name: 'fs_dj'
  user: 'pger'
  password: 'pger_pass'
  host: '*************'
  port: '5432'
  schema: 'public'

# Redis配置
redis:
  location: 'redis://127.0.0.1:6379'

# 国际化配置
language_code: 'zh-hans'
time_zone: 'Asia/Shanghai'

# 静态文件配置
static_url: 'static/'
static_root: 'static' 