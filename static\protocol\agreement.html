<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓小助用户服务协议</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 15px;
        }
        
        .agreement-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #4a7bff, #3366cc);
            color: white;
            padding: 20px 25px;
            text-align: center;
            position: relative;
        }
        
        .logo-container {
            margin-bottom: 15px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            letter-spacing: 1px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .logo-icon {
            background-color: white;
            color: #3366cc;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            margin: 8px 0;
        }
        
        .effective-date {
            font-size: 13px;
            opacity: 0.9;
        }
        
        .agreement-content {
            padding: 25px;
            overflow-y: auto;
            max-height: calc(100vh - 140px);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #3366cc;
            padding: 15px 0 8px;
            border-bottom: 1px solid #eaeaea;
            margin-bottom: 12px;
            position: relative;
        }
        
        .section-title:after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 2px;
            background: #3366cc;
        }
        
        .clause {
            margin-bottom: 15px;
            font-size: 15px;
        }
        
        .clause-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #444;
        }
        
        .clause-content {
            padding-left: 12px;
            text-align: justify;
        }
        
        .highlight {
            background-color: #e3f2ff;
            padding: 5px;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .warning {
            background-color: #fff8e6;
            border-left: 4px solid #ffc107;
            padding: 12px;
            margin: 15px 0;
            font-size: 14px;
        }
        
        footer {
            padding: 15px 25px;
            background-color: #f5f7fa;
            border-top: 1px solid #eaeaea;
            font-size: 13px;
            color: #666;
            text-align: center;
        }
        
        .btn-container {
            display: flex;
            gap: 12px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-agree {
            background: linear-gradient(135deg, #4a7bff, #3366cc);
            color: white;
        }
        
        .btn-disagree {
            background: white;
            color: #666;
            border: 1px solid #e0e0e0;
        }
        
        @media (max-width: 480px) {
            .agreement-content {
                padding: 20px 15px;
            }
            
            .clause-content {
                font-size: 14px;
            }
            
            .section-title {
                font-size: 17px;
            }
            
            .title {
                font-size: 18px;
            }
            
            header {
                padding: 15px;
            }
        }
        
        /* 自定义滚动条 */
        .agreement-content::-webkit-scrollbar {
            width: 6px;
        }
        
        .agreement-content::-webkit-scrollbar-thumb {
            background-color: #b8d0ff;
            border-radius: 3px;
        }
        
        .agreement-content::-webkit-scrollbar-track {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="agreement-container">
        <header>
            <div class="logo-container">
                <div class="logo">
                    <span class="logo-icon">仓</span>
                    <span>仓小助</span>
                </div>
            </div>
            <h1 class="title">用户服务协议</h1>
        </header>
        
        <div class="agreement-content">
            <div class="clause">
                <p class="clause-content">
                    本《用户服务协议》（以下简称"协议"）是您（个人或企业用户）与广州飞数方程智能科技有限公司（以下简称"本公司"）之间关于使用"仓小助"软件及相关服务（包括微信小程序、移动应用程序及关联云端服务，以下简称"本服务"）所订立的协议。
                </p>
            </div>
            
            <div class="section-title">第一条 协议的接受与修改</div>
            <div class="clause">
                <div class="clause-content">
                    <p>1.1 您在注册、登录、使用本服务前，必须<span class="highlight">完全阅读、理解且同意</span>本协议及《隐私政策》全部条款。</p>
                    <p>1.2 本协议具有<span class="highlight">合同法律效力</span>。勾选"同意"或实际使用本服务即视为您与本公司达成有效协议。</p>
                    <p>1.3 本公司保留<span class="highlight">单方修改协议的权利</span>，修改内容于官网公告7日后生效。如您继续使用服务，视为接受修改；若不同意，应立即停止使用并注销账户。</p>
                </div>
            </div>
            
            <div class="section-title">第二条 服务内容</div>
            <div class="clause">
                <div class="clause-content">
                    <p>2.1 本服务提供人工智能驱动的进销存管理系统，核心功能包括但不限于：</p>
                    <p>（a）通过图像识别、语音输入、扫码等方式实现智能化商品出入库登记；</p>
                    <p>（b）基于AI算法的库存需求预测、短缺/积压预警；</p>
                    <p>（c）智能生成采购建议、销售趋势分析及经营利润报表；</p>
                    <p>（d）多终端数据协同管理（小程序/App/Web控制台）。</p>
                    <p>2.2 服务形式为<span class="highlight">软件即服务（SaaS）</span>，您需通过互联网访问云端系统。本公司不保证服务无中断或绝对安全。</p>
                </div>
            </div>
            
            <div class="section-title">第三条 账号注册与管理</div>
            <div class="clause">
                <div class="clause-content">
                    <p>3.1 注册需提供<span class="highlight">真实有效信息</span>（个人用户：姓名+手机号；企业用户：营业执照+管理员信息）。禁止冒用他人身份或虚构信息。</p>
                    <p>3.2 <span class="highlight">账号所有权归属本公司</span>，您仅获非排他性使用权。禁止转让、出租、出借账号或用于非法目的。</p>
                    <p>3.3 您需妥善保管账号密码，凡使用该账号的操作均视为您本人行为，相关责任由您自行承担。</p>
                </div>
            </div>
            
            <div class="section-title">第四条 用户数据权利与授权</div>
            <div class="clause">
                <div class="clause-content">
                    <p>4.1 您通过本服务上传、生成的<span class="highlight">业务数据所有权归属您本人/企业</span>。</p>
                    <p>4.2 为提供AI功能，您<span class="highlight">不可撤销地授权本公司</span>：</p>
                    <p>（a）在服务范围内存储、处理、分析您的业务数据；</p>
                    <p>（b）对数据进行<span class="highlight">匿名化脱敏处理</span>后，用于算法模型训练优化；</p>
                    <p>（c）根据指令生成管理报告。</p>
                    <p>4.3 您<span class="highlight">严禁上传</span>国家秘密、个人隐私（身份证号/银行卡等）、侵犯第三方权利的数据。违者本公司有权删除数据并终止服务。</p>
                </div>
            </div>
            
            <div class="section-title">第五条 使用规范与禁止行为</div>
            <div class="clause">
                <div class="clause-content">
                    <p>您承诺不得从事以下行为：</p>
                    <p>5.1 利用本服务实施洗钱、传销、虚假交易等非法活动；</p>
                    <p>5.2 对本系统进行反向工程、破解、干扰服务器安全；</p>
                    <p>5.3 发布违法信息或病毒程序，损害系统稳定性；</p>
                    <p>5.4 滥用AI功能生成误导性报告或虚假数据；</p>
                    <p>5.5 违反《网络安全法》《数据安全法》《个人信息保护法》的行为。</p>
                </div>
            </div>
            
            <div class="section-title">第六条 知识产权</div>
            <div class="clause">
                <div class="clause-content">
                    <p>6.1 本公司独立拥有本服务所有<span class="highlight">源代码、界面设计、商标（"仓小助"）及相关知识产权</span>。</p>
                    <p>6.2 基于您的数据生成的分析报告，其知识产权由双方共享：您享有基础数据权，本公司享有算法衍生成果权。</p>
                </div>
            </div>
            
            <div class="warning">
                <p><strong>特别注意：</strong>您已阅读至协议重点内容，请确认您充分理解以下关键条款：</p>
                <p>• 用户数据所有权归属您所有，授权本公司进行匿名化处理</p>
                <p>• AI分析结果仅供参考，不作为决策唯一依据</p>
                <p>• 禁止上传敏感个人信息及违法违规内容</p>
            </div>
            
            <div class="section-title">第七条 免责声明</div>
            <div class="clause">
                <div class="clause-content">
                    <p>7.1 <span class="highlight">AI分析结果仅供参考</span>，不构成专业决策建议。您应结合实际情况独立判断并承担经营风险。</p>
                    <p>7.2 因下列原因造成的损失，本公司不承担责任：</p>
                    <p>（a）用户操作失误或设备故障；</p>
                    <p>（b）网络运营商服务中断、黑客攻击等不可控因素；</p>
                    <p>（c）政府政策变更等不可抗力事件；</p>
                    <p>（d）第三方支付、微信平台接口故障。</p>
                    <p>7.3 免费服务功能可能随时调整或终止，恕不另行通知。</p>
                </div>
            </div>
            
            <div class="section-title">第八条 隐私保护</div>
            <div class="clause">
                <div class="clause-content">
                    <p>您的个人信息受本公司<span class="highlight">《隐私政策》</span>约束，该政策与本协议互为补充，具有同等效力。</p>
                </div>
            </div>
            
            <div class="section-title">第九条 服务变更与终止</div>
            <div class="clause">
                <div class="clause-content">
                    <p>9.1 本公司有权视情况调整、暂停或终止部分或全部服务（付费服务将提前30日公告）。</p>
                    <p>9.2 您可随时通过【账户设置→注销账户】终止服务。账户注销后，数据将按《隐私政策》规定删除或匿名化处理。</p>
                    <p>9.3 若您违反本协议，本公司有权暂停服务或永久封号，且保留追究法律责任的权利。</p>
                </div>
            </div>
            
            <div class="section-title">第十条 法律适用与争议解决</div>
            <div class="clause">
                <div class="clause-content">
                    <p>10.1 本协议适用<span class="highlight">中华人民共和国大陆地区法律</span>。</p>
                    <p>10.2 争议应首先友好协商；协商不成的，任何一方可向本公司所在地（广州市天河区）人民法院提起诉讼。</p>
                </div>
            </div>
            
       
        </div>
        
        <footer>
            <p style="margin-top: 15px;">©2025 广州飞数方程智能科技有限公司 版权所有</p>
        </footer>
    </div>
    
    </body>
</html>