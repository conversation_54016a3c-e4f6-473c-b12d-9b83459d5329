from typing import List
from rest_framework import viewsets, serializers
from rest_framework.response import Response
from rest_framework import status
from WmsCore.models import Item, Stock, StockHistory, Unit, PurchaseInItem, SalesOutItem, StockReturnItem
from django.db.models import Sum
from rest_framework.decorators import action
from WmsCore.utils.serializer_details import SerializerCollector
from common.logger import logger
from WmsCore.control.base import StandardResultsSetPagination

from common.make_response import make_response

class ItemStockSerializer(serializers.ModelSerializer):
    """
    物品库存信息序列化器
    """
    item_code = serializers.CharField(source='code', read_only=True, help_text="物品编号")
    item_name = serializers.CharField(source='name', read_only=True, help_text="物品名称")
    available_stock = serializers.DecimalField(source='total_stock', max_digits=30, decimal_places=8, read_only=True, help_text="可用库存")
    unit_name = serializers.Char<PERSON>ield(source='unit.unit_type.name', read_only=True, help_text="计量单位")
    stock_cost = serializers.DecimalField(source='total_cost', max_digits=30, decimal_places=8, read_only=True, help_text="库存成本")

    class Meta:
        model = Item
        fields = [ 'id', 'item_code', 'item_name', 'available_stock', 'unit_name', 'stock_cost']

@SerializerCollector(alias="库存历史变动记录")
class StockHistorySerializer(serializers.ModelSerializer):
    """
    库存历史变动记录序列化器
    """

    class Meta:
        model = StockHistory
        fields = ['history_date', 'history_type', 'history_order_id', 'quantity', 'item_id', 'track_id']

class StockHistoryQueryParamSerializer(serializers.Serializer):
    stock_id = serializers.IntegerField(required=False, min_value=0)  # 可选参数，最小值 0
    track_id = serializers.IntegerField(required=False, min_value=0)  # 可选参数，最小值 0

class StockViewSet(viewsets.ReadOnlyModelViewSet):
    """
    库存信息视图集
    list:
        获取所有物品的库存信息列表。
    retrieve:
        获取单个物品的库存历史记录.
    """
    queryset = Item.objects.all()
    pagination_class = StandardResultsSetPagination

    def get_serializer_class(self):
        if self.action == 'list':
            return ItemStockSerializer
        if self.action == 'track_history':
            return StockHistorySerializer
        return ItemStockSerializer

    # def retrieve(self, request, *args, **kwargs):
    #     instance = self.get_object()
        
    #     # 采购入库记录
    #     purchase_in_items = PurchaseInItem.objects.filter(item=instance).values(
    #         'purchase_in__in_date', 
    #         'purchase_in__order_id', 
    #         'quantity', 
    #         'unit__unit_type__name', 
    #         'purchase_price'
    #     )
        
    #     # 销售出库记录
    #     sales_out_items = SalesOutItem.objects.filter(item=instance).values(
    #         'sales_out__out_date', 
    #         'sales_out__id', 
    #         'quantity', 
    #         'stock__unit__unit_type__name', 
    #         'out_cost'
    #     )
        
    #     # 库存退货记录
    #     stock_return_items = StockReturnItem.objects.filter(stock__item=instance).values(
    #         'stock_return__return_date', 
    #         'stock_return__id', 
    #         'quantity', 
    #         'unit__unit_type__name', 
    #         'return_price'
    #     )

    #     history = []
    #     for item in purchase_in_items:
    #         history.append({
    #             'date': item['purchase_in__in_date'],
    #             'document_id': item['purchase_in__order_id'],
    #             'direction': '入库',
    #             'quantity': item['quantity'],
    #             'unit': item['unit__unit_type__name'],
    #             'price': item['purchase_price']
    #         })
            
    #     for item in sales_out_items:
    #         history.append({
    #             'date': item['sales_out__out_date'],
    #             'document_id': f"SO-{item['sales_out__id']}",
    #             'direction': '出库',
    #             'quantity': item['quantity'],
    #             'unit': item['stock__unit__unit_type__name'],
    #             'price': item['out_cost']
    #         })

    #     for item in stock_return_items:
    #         history.append({
    #             'date': item['stock_return__return_date'],
    #             'document_id': f"SR-{item['stock_return__id']}",
    #             'direction': '出库',
    #             'quantity': item['quantity'],
    #             'unit': item['unit__unit_type__name'],
    #             'price': item['return_price']
    #         })
            
    #     # 按日期排序
    #     history.sort(key=lambda x: x['date'], reverse=True)
        
    #     serializer = self.get_serializer(history, many=True)
    #     return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def track_history(self, request, **kwargs):
        serializer = StockHistoryQueryParamSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        
        stock_id = serializer.validated_data.get('stock_id', 0) 
        track_id = serializer.validated_data.get('track_id', 0)
        
        # 根据参数获取历史记录
        if stock_id != 0:
            # 跟踪单个库存物品的变动历史
            queryset = StockHistory.objects.filter(stock_id=stock_id).order_by('-history_date')
            logger.debug(f"stock_id: {stock_id} history count: {queryset.count()}")
        elif track_id != 0:
            # 跟踪整个 track_id 的变动历史
            queryset = StockHistory.objects.filter(track_id=track_id).order_by('-history_date')
            logger.debug(f"track_id: {track_id} history count: {queryset.count()}")
        else:
            # 如果没有提供有效参数，返回空列表，带分页结构
            return make_response(
                code=0, 
                msg='获取成功', 
                data={'count': 0, 'next': None, 'previous': None, 'results': []},
                status=status.HTTP_200_OK
            )
        
        # 应用分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            # 获取分页数据
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            # 使用统一的响应结构
            return make_response(
                code=0,
                msg='获取成功',
                data=paginated_data,
                status=status.HTTP_200_OK
            )
        
        # 如果未使用分页，则仍以标准格式返回
        serializer = self.get_serializer(queryset, many=True)
        return make_response(
            code=0, 
            msg='获取成功', 
            data={'count': queryset.count(), 'next': None, 'previous': None, 'results': serializer.data},
            status=status.HTTP_200_OK
        )

