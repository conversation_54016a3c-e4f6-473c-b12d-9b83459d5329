from django.db import models, transaction
from rest_framework import status
from rest_framework.decorators import action

from AccessGateway.control.auth import make_response
from WmsCore.control.base import HugeResultsSetPagination, SafeModelViewSet
from WmsCore.models import PaymentMethod
from WmsCore.utils.serializer_details import Serializer<PERSON>ollector
from WmsCore.utils.submission import prevent_duplicate_submission
from rest_framework import serializers
from common.make_response import make_response
from WmsCore.model.ret_code import RetCode
from django.utils.translation import gettext_lazy as _
from common.logger import logger;
from common.exception import WmsException
from common.translation import _T
@SerializerCollector(alias="支付方式")
class PayMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentMethod
        fields = ['id', 'account_type', 'default_settlement', 'account_name', 'bank_name', 'bank_account', 'bank_account_name', 'current_balance', 'is_active']

class PaymentMethodViewSet(SafeModelViewSet):
    queryset = PaymentMethod.objects.all()
    serializer_class = PayMethodSerializer
    pagination_class = HugeResultsSetPagination

    # def list(self, request, *args, **kwargs):
    #     response = super().list(request, *args, **kwargs)
    #     return make_response(code=RetCode.SUCCESS, msg='suc', data=response.data)
    
    def CheckMethodType(self, validated_data):
        method_type = validated_data.get('account_type')
        settlement_type = validated_data.get('default_settlement')
        logger.debug(f"method_type: {method_type} settlement_type: {settlement_type}")
        if method_type == PaymentMethod.AccountTypeChoices.CASH:
            if settlement_type is None or settlement_type !=  PaymentMethod.SettlementTypeChoices.CASH :
                raise WmsException(_('现金结算必须选择现金账户'))
        elif method_type == PaymentMethod.AccountTypeChoices.ALIPAY:
            if settlement_type is None or settlement_type !=  PaymentMethod.SettlementTypeChoices.ALIPAY :
                raise WmsException(_('支付宝结算必须选择支付宝账户'))
        elif method_type == PaymentMethod.AccountTypeChoices.WECHAT:
            if settlement_type is None or settlement_type !=  PaymentMethod.SettlementTypeChoices.WECHAT :
                raise WmsException(_('微信结算必须选择微信账户'))
        elif method_type == PaymentMethod.AccountTypeChoices.BANK:
            valid_types = [
                PaymentMethod.SettlementTypeChoices.BANK_DRAFT,
                PaymentMethod.SettlementTypeChoices.BANK_NOTE,
                PaymentMethod.SettlementTypeChoices.CREDIT_CARD,
                PaymentMethod.SettlementTypeChoices.CASH,
            ]
            valid_type_labels = [PaymentMethod.SettlementTypeChoices(label).label for label in valid_types]
            if settlement_type is None or settlement_type not in valid_types:
                raise WmsException(
                    _T(
                        "银行账户结算类型选择有误，当前选择：{chosen}，可选类型：{options}",
                        chosen=settlement_type,
                        options="、".join(valid_type_labels)
                    ),
                    code=1006  # FIELD_TYPE_ERROR
                )
        elif method_type == PaymentMethod.AccountTypeChoices.OTHER:
            return ;
        else:
            raise WmsException(_('选择正确的账户类型'))

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data);
        serializer.is_valid(raise_exception=True);
        validated_data = serializer.validated_data
        self.CheckMethodType(validated_data)
        payment_method = PaymentMethod.objects.create(**validated_data)
        logger.debug(f"create Payment: {payment_method}")
        serializer = self.get_serializer(payment_method)
        return make_response(code=RetCode.SUCCESS, msg='suc', data=serializer.data)

    def update(self, request, pk=None, **kwargs):
        serializer = self.get_serializer(data=request.data);
        serializer.is_valid(raise_exception=True);
        validated_data = serializer.validated_data
        self.CheckMethodType(validated_data)
        # 更新时，不更新余额
        validated_data.pop('current_balance', None)
        payment_method = self.get_object()
        if payment_method:
            for key, value in validated_data.items():
                setattr(payment_method, key, value)
            payment_method.save()
            serializer = self.get_serializer(payment_method)
            return make_response(code=RetCode.SUCCESS, msg='suc', data=serializer.data)
        else:
            return make_response(code=RetCode.METHON_NOT_EXIST_RECORD, msg=_('记录不存在'), status=status.HTTP_404_NOT_FOUND)

    def retrieve(self, request, *args, **kwargs):
        payment_method = PaymentMethod.objects.filter(id=kwargs.get('pk')).first()
        if payment_method:
            serializer = self.get_serializer(payment_method)
            return make_response(code=RetCode.SUCCESS, msg='suc', data=serializer.data)
        else:
            return make_response(code=RetCode.METHON_NOT_EXIST_RECORD, msg=_('记录不存在'), status=status.HTTP_404_NOT_FOUND)

    def destroy(self, request, *args, **kwargs):
        return make_response(code=RetCode.METHON_NOT_ALLOWED_DELETE, msg=_('操作不支持'), status=status.HTTP_405_METHOD_NOT_ALLOWED)