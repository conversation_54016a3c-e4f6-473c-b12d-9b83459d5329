from WmsCore.models import StockHistory, Stock, Item, Unit
from decimal import Decimal


class StockHistoryAdmin():
    @staticmethod
    def AddStockHistory(stock: Stock, history_type: StockHistory.HistoryTypeChoices, history_order_id: str, demand_item_keyid: int, track_id: int, quantity: Decimal ):
        StockHistory.objects.create(
            stock=stock,
            history_type=history_type,
            history_order_id=history_order_id,
            track_id=track_id,
            demand_item_keyid=demand_item_keyid,
            item=stock.item,
            quantity=quantity
        )
        pass

    @staticmethod
    def DecStockHistory(stock: Stock, history_type: StockHistory.HistoryTypeChoices, history_order_id: str, demand_item_keyid: int, track_id: int, quantity: Decimal ):
        StockHistory.objects.create(
            stock=stock,
            history_type=history_type,
            history_order_id=history_order_id,
            track_id=track_id,
            demand_item_keyid=demand_item_keyid,
            item=stock.item,
            quantity= -quantity
        )