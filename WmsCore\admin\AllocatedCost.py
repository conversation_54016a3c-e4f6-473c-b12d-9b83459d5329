
from decimal import Decimal
import json
from WmsCore.models import CostAllocation, Payment, PaymentMethod, ZTUser
from WmsCore.utils.counter import Counter
from common.exception import WmsException
from common.logger import logger 
from common.translation import _T;



class AllocatedCostAdmin:

    @staticmethod
    def create_or_update( allocated_cost_data: dict, handler: ZTUser, source_type: str, source_order_id:str )->CostAllocation:
         # 计算均摊费用
        if not allocated_cost_data:
            return None;
        allocated_id = allocated_cost_data.get('id', None)
        total_cost = Decimal(0); 
        details = json.loads(allocated_cost_data.get('allocation_details', "[]"));
        logger.debug(f"details: {details} type: {type(details)}")
        for cost in details:
            print(f"cost: {cost}")
            cost = Decimal( float(cost['cost']) );
            if cost <= 0:
                raise WmsException(_T("分摊费用不能小于0") + " " + str(cost))
            total_cost += cost;
        if total_cost == 0:
            #清空
            if allocated_id:
                allocated_cost_obj = CostAllocation.objects.get(id=allocated_id, source_order_id=source_order_id, source_type=source_type).first();
                allocated_cost_obj.delete()
            #没有分摊就直接删除了
            return None;
        allocation_method = allocated_cost_data.get('allocation_method', None);
        if allocation_method is None:
            raise WmsException(_T("分摊费用中的付款方式不能为空"))
        if allocation_method not in [CostAllocation.AllocationMethodChoices.AMOUNT, CostAllocation.AllocationMethodChoices.QUANTITY]:
            raise WmsException(_T("分配方式不正确" + " " + allocation_method))
        if allocated_id:
            allocated_cost_obj = CostAllocation.objects.get(id=allocated_id, source_order_id=source_order_id, source_type=source_type).first();
            for field, value in allocated_cost_data.items():
                setattr(allocated_cost_obj, field, value)
            allocated_cost_obj.total_cost = total_cost;
            allocated_cost_obj.save()
        else:
            ca_order_id = Counter.SysDayCounter("CostAllocation" ) #CAL
            allocated_cost_obj = CostAllocation.objects.create(**allocated_cost_data, handler=handler, paid_amount=0, source_type=source_type, source_order_id=source_order_id, order_id=ca_order_id, total_cost=total_cost)
        #allocated_cost_obj.total_cost = total_cost;
        logger.debug(f"total_cost: {total_cost}")
        return allocated_cost_obj;

    @staticmethod
    def create_payment(allocated_cost: CostAllocation, pay_amount: Decimal, payment_method: PaymentMethod):
        if pay_amount <= 0:
            return;
        Payment.objects.create(
            order_type=Payment.OrderTypeChoices.COST_ALLOCATION,
            order_id=allocated_cost.order_id,
            amount=pay_amount,
            supplier=allocated_cost.supplier,
            payment_method=payment_method,
            handler=allocated_cost.handler,
            controller=allocated_cost.handler,
            remark=_T("分摊费用")
        )
        allocated_cost.paid_amount += pay_amount;
        allocated_cost.save();
    
    @staticmethod
    def create_payable(allocated_cost: CostAllocation):
        #生成应付单，供应商的应付单
        if allocated_cost.paid_amount >= allocated_cost.total_cost:
            return ;
        #生成应付单，供应商的应付单
