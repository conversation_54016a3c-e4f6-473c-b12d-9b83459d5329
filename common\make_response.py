from django.http import JsonResponse
from enum import Enum
from common.logger import logger as log
from rest_framework import status

def make_response(code:Enum|int, msg,status=status.HTTP_200_OK,data=None):
    return JsonResponse({
        'code': code.value if isinstance(code, Enum) else code,
        'msg': msg,
        'status': status,
        'data': data
    }, status=status)


def test():
    pass