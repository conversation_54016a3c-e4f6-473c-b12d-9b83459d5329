import random
import string
import threading

from django.conf import settings
from django.db import transaction
import jwt
import requests
from rest_framework import status

from AccessGateway.control.auth import make_response
from AccessGateway.models import Enterprise
from common.APIViewSafe import APIViewSafe

#from AccessGateway.models import Domain, TenantLedger
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _
from common.auth.jwt_serv_auth import JWTServiceAuth
#from django_tenants.utils import tenant_context

class TenantMgr():
    # @staticmethod
    # def RequestToCheckTenantCapacity():
    #     new_code = TenantMgr.NewToken();
    #     #起线程异步发起请求，发起后不用等待回包
    #     log.debug(f"异步发起检查租户容量请求, 新令牌1 ")
    #     thread = threading.Thread(target=TenantMgr.RequestToMalloc, args=(new_code,))
    #     thread.start()
    #     log.debug(f"异步发起检查租户容量请求, 新令牌2 ")

    # @staticmethod
    # def RequestToMalloc(code:str):
    #     try:
    #         log.debug(f"异步发起检查租户容量请求, 新令牌3 ")
    #         response = requests.post(settings.PRE_MALLOC_TENANT_URL, json={'code': code})
    #         rsp = response.json()
    #         log.debug(f"预分配租户回包: {rsp}")
    #     except Exception as e:
    #         log.error(f"预分配租户失败: {str(e)}")
    #         return 0;

    @staticmethod
    def NewToken():
        check_key = settings.MANAGE_SERVER_CHECK_KEY
        return JWTServiceAuth.NewToken(check_key);

    @staticmethod
    def CheckToken(token:str ):
        check_key = settings.MANAGE_SERVER_CHECK_KEY
        check_key2 = settings.MANAGE_SERVER_CHECK_KEY2
        return JWTServiceAuth.CheckToken(token, check_key, check_key2)

    # @staticmethod
    # def AllocTenantDB(pre_db_label=None):
    #     # 根据企业名称，或者前置 db，分配新的数据库
    #     if pre_db_label is None:
    #         # TODO
    #         pre_db_label = "最新的 lab 就好";
    #         # else 旧的存在，就先用的久的，以后再检测负荷
    #     log.debug(f"分配数据库标签: {pre_db_label}")
    #     return pre_db_label

    # @staticmethod
    # def AllocTenantUniqName():
    #     # 生产唯一id，长度为10，由数字和字母组成,检查是否存在
    #     tenant_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
    #     log.debug(f"生成租户ID: {tenant_id}")
    #     # while Domain.objects.filter(domain=tenant_id).exists():
    #     #     tenant_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
    #     #     log.debug(f"重新生成租户ID: {tenant_id}")
    #     return tenant_id


    # @staticmethod
    # def AllocTenantModel(schema_name, db_label):
    #     # TODO
    #     # 给指定 label 的 db 中新建schema 并创建相应的表
    #     # 新建一个临时租户，并创建schema,不保存
    #     try:
    #         log.info(f"分配租户模型: schema_name={schema_name}, db_label={db_label}")
    #         # tmp_tenant = TenantLedger(schema_name=schema_name, db_label=db_label)
    #         # tmp_tenant.create_schema()
    #         # with tenant_context(tmp_tenant):
    #         #     from WmsCore.data.make_datas import make_default_data_to_db
    #         #     make_default_data_to_db(schema_name)
    #         #     pass

    #         log.info(f"创建schema成功: {schema_name}")
    #         return True;
    #     except Exception as e:
    #         log.error(f"分配租户模型失败: {str(e)}")
    #         return False;

    # @staticmethod
    # def PreMallocTenant():
    #     # 预分配租户
    #     Max_Capacity = settings.PRE_MALLOC_TENANT_CAPACITY;
    #     Low_Capacity = settings.PRE_MALLOC_TENANT_LOW;
    #     # 检查当前租户数量
    #     # current_tenant_count = TenantLedger.objects.filter(enterprise_id__isnull=True).count();
    #     # # count = 0;
    #     # # if current_tenant_count <= Low_Capacity:
    #     # #     for i in range(Max_Capacity - current_tenant_count):
    #     # #         count += 1;
    #     # #         TenantMgr.CreateTenant();
    #     # log.debug(f"当前 空余账套数量: {current_tenant_count},创建了{count} 最大容量: {Max_Capacity}, 低容量: {Low_Capacity}")
    #     # return count;

    # @staticmethod
    # def CreateTenant():
    #     """
    #     创建租户
    #     """
    #     log.info("创建租户请求")
    #     # TODO
    #     """  这里应该判断企业的账套数量或者员工数量，反正就是是否符合充值等条件了，
    #     不能由前端发起请求就直接创建 ，现在假设没问题
    #     """

    #     try:
    #         # 分配db_label
    #         db_label = TenantMgr.AllocTenantDB(None)
    #         tenant_id = TenantMgr.AllocTenantUniqName()

    #         # # 创建租户，指定company_id
    #         # tenant = TenantLedger(
    #         #     schema_name=tenant_id,
    #         #     #name=tenant_name,
    #         #     db_label=db_label
    #         # )
    #         # tenant.auto_create_schema = False
    #         # #tenant.save()
    #         # log.info(f"创建租户成功: id={tenant.id} ")
    #         # tenant.alloc_model_flag = True
    #         # tenant.save()

    #         # # 添加 租户配置
    #         # domain = Domain()
    #         # domain.domain = tenant_id
    #         # domain.tenant = tenant
    #         # domain.is_primary = True
    #         # domain.save()
    #         # log.info(f"创建域名成功: domain={tenant_id}")

    #         # succ = TenantMgr.AllocTenantModel(tenant_id, db_label)
    #         # if not succ:
    #         #     log.error(f"创建租户模型失败: {tenant_id}")
    #         #     tenant.delete()
    #         #     domain.delete()
    #         #     return None;

    #         return tenant_id;
    #     except Exception as e:
    #         log.error(f"创建租户失败: {str(e)}")
    #         raise e

class CreateTenantAPIView(APIViewSafe):
    no_safe = True;

    @transaction.atomic
    def post(self, request):
        """
        创建租户
        """
        try:
            log.info("创建租户请求")
            # access_token  = request.data.get('code');
            #   # 解码并验证令牌
            # if not TenantMgr.CheckToken(access_token):
            #     return make_response(
            #         code=1,
            #         msg=_('创建失败: 非法请求'),
            #         status=status.HTTP_400_BAD_REQUEST
            #     )
            
            # size = TenantMgr.PreMallocTenant()
            return make_response(
                code=0,
                msg=_('succ'),
                data={
                    'count': 0,
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            log.error(f"创建租户失败: {str(e)}")
            return make_response(
                code=1,
                msg=_('创建租户失败: ') + str(e),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# class CheckTenantCapacityAPIView(APIViewSafe):
#     no_safe = True;

#     def get(self, request):
#         try:
#             log.debug("检查租户容量请求")
#             # size = TenantMgr.RequestToCheckTenantCapacity()
#             # log.debug(f"检查租户容量请求, 空余账套数量: {size}")
#             return make_response(
#                 code=0,
#                 msg=_('succ'), 
#                 data={
#                     'count': 0,
#                 },
#                 status=status.HTTP_200_OK
#             )
#         except Exception as e:
#             log.error(f"检查租户容量失败: {str(e)}")
#             return make_response(
#                 code=1,
#                 msg=_('检查租户容量失败: ') + str(e),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


class TenantToken(APIViewSafe):
    no_safe = True
    def post(self, request):
        # TenantMgr.RequestToCheckTenantCapacity()
        return make_response(
            code=0,
            msg=_('succ'),
            status=status.HTTP_200_OK
        )
