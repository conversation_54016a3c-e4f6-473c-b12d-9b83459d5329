import time
from rest_framework import authentication
from rest_framework.exceptions import AuthenticationFailed, NotAuthenticated
import jwt
from django.conf import settings
from common.auth.jwt_auth import jwt_manager
from common.logger import logger, set_current_user
from django.utils.translation import gettext_lazy as _


class JWTServiceAuth(authentication.BaseAuthentication):
    """JWT认证类"""
    @staticmethod 
    def Encode(data:dict):
        token = jwt.encode(
            data,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        return token;

    @staticmethod
    def Decode(token:str):
        return jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
    
    @staticmethod
    def NewToken(check_key:str):
        # 生成新 token 加上当前时间戳，生成的 token 校验的时候，同时校验时间戳不能超过 5 分钟
        return JWTServiceAuth.Encode({
            'check_key': check_key,
            'timestamp': int(time.time())
        })

    @staticmethod
    def CheckToken(token:str, check_key:str, check_key2:str):
        info = JWTServiceAuth.Decode(token)
        # 设置2个校验key轮转，用来支持动态更新校验 key
        if info.get('check_key') != check_key and info.get('check_key') != check_key2:
            raise AuthenticationFailed(_('无效的认证令牌'))
        if int(time.time()) - info.get('timestamp') > 300:
            raise AuthenticationFailed(_('认证令牌已过期'))
        return True;

    def authenticate(self, request):
        # 从请求头中获取令牌
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            raise NotAuthenticated(_('未提供认证令牌'))

        try:
            # 提取令牌
            token_type, token = auth_header.split()
            if token_type.lower() != 'bearer':
                raise AuthenticationFailed(_('无效的认证方式'))

            # 验证令牌
            check_key = settings.MANAGE_SERVER_CHECK_KEY
            check_key2 = settings.MANAGE_SERVER_CHECK_KEY2
            JWTServiceAuth.CheckToken(token, check_key, check_key2)

        except (ValueError, jwt.PyJWTError) as e:
            logger.warning(f"Authentication failed: {str(e)}")
            raise e;
        except Exception as e:
            logger.warning(f"Unexpected error during authentication: {str(e)}")
            raise AuthenticationFailed(_('认证过程发生错误'))
