from django.db import DatabaseError
#from django.utils.translation import gettext_lazy as _
from common.translation import _T
from rest_framework import status
#from rest_framework.exceptions import APIException
from rest_framework.views import exception_handler

from common.make_response import make_response
from common.logger import logger 

class ErrorCode:
    """基础错误码
    错误码设计规则：
    0: 成功
    1-999: 系统级别错误
    1000-1999: 通用业务错误
    
    各模块错误码范围：
    40000-49999: WMS模块错误码
    50000-59999: Gateway模块错误码
    """
    # 系统级别错误码
    SUCCESS = 0
    UNKNOWN_ERROR = 1
    SYSTEM_ERROR = 2
    
    # 数据验证错误码 (1000-1999)
    BUSINESS_ERROR = 1000
    VALIDATION_ERROR = 1001
    FOREIGN_KEY_UNEXIST = 1002  # 关联信息不存在
    FIELD_REQUIRED = 1003  # 字段必填
    FIELD_RANGE_ERROR = 1004  # 字段范围错误
    FIELD_UNIQUE_ERROR = 1005  # 字段唯一性错误
    FIELD_TYPE_ERROR = 1006  # 字段类型验证错误

    @staticmethod
    def check_value_range(value: int, start: int, end: int, module_name: str) -> None:
        """检查错误码是否在指定范围内"""
        if not isinstance(value, int):
            raise ValueError(f"错误码必须是整数，得到了 {type(value)}")
        if not (start <= value <= end):
            raise ValueError(f"{module_name} 模块的错误码必须在 {start}-{end} 范围内")

class WmsException(Exception):
    def __init__(self, message: str, code: int = None):
        self.message = message
        self.err_code = code if code is not None else ErrorCode.SYSTEM_ERROR
        super().__init__(self.detail())

    def detail(self):
        return f"{self.err_code}: {self.message}"

    def code(self):
        return self.err_code

    def msg(self):
        return self.message

    def __str__(self):
        return self.detail()


# # 业务异常类
# class BusinessException(APIException):
#     """业务逻辑异常"""
#     status_code = status.HTTP_400_BAD_REQUEST
#     def __init__(self, message, business_code=RetCode.BUSINESS_ERROR.value, operation=None, field=None):
#         self.detail = message
#         self.business_code = business_code
#         self.operation = operation  # 操作类型：create, update, delete, query
#         self.field = field  # 相关字段
#         super().__init__(detail=message)

# # 便捷的业务异常抛出函数

def raise_business_error(message, business_code=ErrorCode.BUSINESS_ERROR, operation=None, field=None):
    raise WmsException(message, business_code, operation, field)


def custom_exception_handler(exc, context):
    # 处理自定义WmsException
    if isinstance(exc, WmsException):
        # 直接返回结构化内容
        return make_response(
            code=exc.code() or ErrorCode.SYSTEM_ERROR,
            msg=exc.msg(),
            data={}
        )
    response = exception_handler(exc, context)
    # TODO 下面这个是打印报错, 可自行删除
    logger.error(exc, context)
    if response is not None:
        if hasattr(exc, 'detail') and isinstance(exc.detail, dict):
            # 字段校验错误，msg直接返回详细dict
            #code = RetCode.UNKOWN_ERROR.value
            #msg = format_validation_errors(exc.detail)
            code, msg, detail_info = validation_errors(exc.detail)
            data = {"error_details": detail_info}
        elif hasattr(exc, 'detail') and isinstance(exc.detail, list):
            code = ErrorCode.UNKNOWN_ERROR
            msg = _T("操作错误，请检查数据")
            data = {"error_details": exc.detail}
        else:
            code = ErrorCode.UNKNOWN_ERROR
            msg = _T("操作错误，请检查数据")
            data = {"error_details": str(exc) if str(exc) else _T('An error occurred') }
        # 新增：msg 列表/字典转字符串
        # if isinstance(msg, dict):
        #     all_msgs = []
        #     for k, v in msg.items():
        #         if isinstance(v, list):
        #             all_msgs.extend([str(i) for i in v])
        #         elif isinstance(v, dict):
        #             all_msgs.append(str(format_validation_errors(v)))
        #         else:
        #             all_msgs.append(str(v))
        #         break;
        #     msg = "; ".join(all_msgs)
        # elif isinstance(msg, list):
        #     msg = "; ".join([str(i) for i in msg])
        return make_response(code=code, msg=msg, data=data)
    else:
        view = context.get('view')
        view_name = view.__class__.__name__ if view else 'UnknownView'
        logger.error(f'[{view_name}] Unhandled exception: {str(exc)}', exc_info=True)
        if isinstance(exc, DatabaseError):
            if 'foreign key constraint' in str(exc).lower():
                code = ErrorCode.FOREIGN_KEY_UNEXIST
                #msg = '关联数据不存在，请检查关联信息'
                msg = _T("数据录入有误，请检查关联信息")
            else:
                code = ErrorCode.UNKNOWN_ERROR
                #msg = '数据库操作失败'
                msg = _T("操作系统，请稍后重试")
            data = {'error_details': str(exc)}
        else:
            code = ErrorCode.UNKNOWN_ERROR
            #msg = f"出现异常。({str(exc)})"
            msg = _T("操作错误，请检查数据")
            data = {'error_details': str(exc)}
        return make_response(
            code=code,
            msg=msg,
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            data=data
        )

def format_validation_errors(detail):
    """
    将 DRF 的验证错误格式化为更友好的结构
    支持嵌套字段和错误代码
    """
    errors = {}
    for field, messages in detail.items():
        if isinstance(messages, dict):
            errors[field] = format_validation_errors(messages)
        elif isinstance(messages, list):
            field_errors = []
            for message in messages:
                # 友好化常见英文错误
                if isinstance(message, str):
                    if 'This field may not be blank' in message:
                        field_errors.append(_T("该字段不能为空"))
                    elif 'This field may not be null' in message:
                        field_errors.append(_T("该字段不能为空"))
                    elif 'This field is required' in message:
                        field_errors.append(_T("该字段为必填项"))
                    elif 'not a valid choice' in message:
                        field_errors.append(_T("不是有效的选项"))
                    elif 'Ensure this value is greater than or equal to' in message:
                        field_errors.append(_T("数值不能小于最小值"))
                    elif 'Ensure this value is less than or equal to' in message:
                        field_errors.append(_T("数值不能大于最大值"))
                    elif 'Enter a valid' in message:
                        field_errors.append(_T("请输入有效的值"))
                    else:
                        field_errors.append(str(message))
                else:
                    field_errors.append(str(message))
            errors[field] = field_errors
        else:
            errors[field] = [str(messages)]
    return errors

def validation_errors(detail):
    """
    将 DRF 的验证错误格式化为更友好的结构
    返回第一条错误信息和对应的字段路径
    返回格式: (错误码, 错误消息, 错误字段路径)
    """
    def extract_first_error(data, field_path=""):
        """递归提取第一个错误"""
        if isinstance(data, dict):
            for field, messages in data.items():
                current_path = f"{field_path}.{field}" if field_path else field
                result = extract_first_error(messages, current_path)
                if result:
                    return result
        elif isinstance(data, list):
            if data:
                # 处理列表中的第一个元素
                first_item = data[0]
                if isinstance(first_item, dict):
                    # 如果列表元素是字典，递归处理
                    current_path = f"{field_path}[0]" if field_path else "[0]"
                    result = extract_first_error(first_item, current_path)
                    if result:
                        return result
                elif isinstance(first_item, list):
                    # 如果列表元素还是列表，递归处理
                    current_path = f"{field_path}[0]" if field_path else "[0]"
                    result = extract_first_error(first_item, current_path)
                    if result:
                        return result
                else:
                    # 列表元素是字符串或其他类型
                    message = str(first_item)
                    # 友好化常见英文错误
                    if isinstance(first_item, str):
                        if 'This field may not be blank' in message:
                            err_code = ErrorCode.FIELD_REQUIRED
                            msg = _T("该字段不能为空")
                        elif 'This field may not be null' in message:
                            err_code = ErrorCode.FIELD_REQUIRED
                            msg = _T("该字段不能为空")
                        elif 'This field is required' in message:
                            err_code = ErrorCode.FIELD_REQUIRED
                            msg = _T("该字段为必填项")
                        elif 'not a valid choice' in message:
                            err_code = ErrorCode.FIELD_TYPE_ERROR
                            msg = _T("不是有效的选项")
                        elif 'Ensure this value is greater than or equal to' in message:
                            err_code = ErrorCode.FIELD_RANGE_ERROR
                            msg = _T("数值不能小于最小值")
                        elif 'Ensure this value is less than or equal to' in message:
                            err_code = ErrorCode.FIELD_RANGE_ERROR
                            msg = _T("数值不能大于最大值")
                        elif 'Enter a valid' in message:
                            err_code = ErrorCode.FIELD_TYPE_ERROR
                            msg = _T("请输入有效的值")
                        else:
                            err_code = ErrorCode.UNKNOWN_ERROR
                            msg = str(message)
                    else:
                        err_code = ErrorCode.UNKNOWN_ERROR
                        msg = str(message)
                    return err_code, msg, field_path
        else:
            # 处理非列表/字典类型的错误
            err_code = ErrorCode.UNKNOWN_ERROR
            msg = str(data)
            return err_code, msg, field_path
        return None

    # 提取第一个错误
    result = extract_first_error(detail)
    if result:
        err_code, msg, field_path = result
        # 构建错误详情，只包含第一个错误的字段路径
        errors = {"path": field_path } if field_path else {}
        return err_code, msg, errors
    else:
        # 如果没有找到错误，返回默认值
        return ErrorCode.UNKNOWN_ERROR, _T("数据验证失败"), {}
