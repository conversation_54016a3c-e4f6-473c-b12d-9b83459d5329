from datetime import timedelta
from decimal import Decimal
import json
from django.db import models, transaction
from django.forms.models import model_to_dict
from rest_framework import status
from rest_framework.decorators import action
from rest_framework import serializers
from django.db.models import Prefetch, F, Q
from django.utils.translation import gettext_lazy as _

from AccessGateway.control.auth import make_response
from WmsCore.admin.AllocatedCost import AllocatedCostAdmin
from WmsCore.admin.stock_history import StockHistoryAdmin
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import CostAllocation, Payment, PaymentMethod, StockHistory, StockReturn, StockReturnItem, Stock, PurchaseIn, PurchaseInItem, StockReturnPayment, Warehouse, ZTUser
from WmsCore.control.PurchaseIn import AllocatedCostSerializer
from WmsCore.utils.counter import Counter
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.logger import logger as log
from common.exception import WmsException
from common.make_response import make_response

class StockReturnItemSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True, read_only=True)
    item_id = serializers.IntegerField(source='stock.item.id', read_only=True)
    item_name = serializers.CharField(source='stock.item.name', read_only=True)
    #code = serializers.CharField(source='stock.item.code', read_only=True)
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True)
    stock_quantity = serializers.DecimalField(max_digits=30, decimal_places=8, source='stock.quantity', read_only=True)
    stock_remaining = serializers.DecimalField(max_digits=30, decimal_places=8, source='stock.remaining_quantity', read_only=True)
    purchase_in_code = serializers.CharField(source='purchase_in_item.purchase_in.order_id', read_only=True)

    class Meta:
        model = StockReturnItem
        fields = [
            'id', 'stock', 'item_id', 'item_name', 'purchase_in_item', 'purchase_in_code',
            'unit', 'unit_name', 'quantity', 'return_price',
            'is_defective', 'defect_description', 'stock_quantity', 'stock_remaining', 'remark', 'track_id', 'batch_number'
        ]
        read_only_fields = [ 'track_id', 'batch_number']

    # def validate(self, data):
    #     if data['quantity'] > data['stock'].remaining_quantity:
    #         raise serializers.ValidationError(_('退货数量不能超过库存剩余数量'))
    #     return data

@SerializerCollector(alias="退货单列表")
class StockReturnListSerializer(serializers.ModelSerializer):
    """用于列表查询的序列化器，只返回指定字段"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    items_count = serializers.IntegerField(source='items.count', read_only=True)
    handler_name = serializers.CharField(source='handler.name', read_only=True)
    # def get_unpaid_amount(self, obj):
    #     return obj.total_amount - (obj.payments.aggregate(models.Sum('amount'))['amount__sum'] or 0)

    class Meta:
        model = StockReturn
        fields = [
            'id', 'supplier_name', 'warehouse_name', 'handler', 'handler_name', 'items_count',
            'total_amount', 'refunded_amount', 'return_date', 'actual_amount',
            'discount', 'return_status', 'short_desc', 'items_count'
        ]

@SerializerCollector(alias="退货单")
class StockReturnSerializer(serializers.ModelSerializer):
    items = StockReturnItemSerializer(many=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    is_commit = serializers.IntegerField(required=False, allow_null=True, write_only=True)
    purchase_in_code = serializers.CharField(source='purchase_in.order_id', read_only=True)
    allocated_cost = AllocatedCostSerializer(required=False, allow_null=True )

    class Meta:
        model = StockReturn
        fields = [
            'id', 'supplier', 'supplier_name', 'warehouse', 'warehouse_name',
            'return_date', 'handler', 'return_reason', 'items', 'return_status',
            'total_amount', 'actual_amount', 'refunded_amount', 'discount', 'remark', 'is_commit',
            'payment_method', 'pay_amount', 'purchase_in', 'purchase_in_code', 'allocated_cost', 'allocated_payment_method', 'allocated_pay_amount'
        ]
        read_only_fields = ['handler', 'total_amount', 'return_status', 'actual_amount', 'refunded_amount' ]

class StockReturnViewSet(SafeModelViewSet):
    """
    退货单的增删改查接口
    """
    queryset = StockReturn.objects.prefetch_related(
        Prefetch('items', queryset=StockReturnItem.objects.select_related('stock', 'unit')),
        'payments'
    ).all()
    serializer_class = StockReturnSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """获取查询集，过滤掉已取消的订单"""
        return super().get_queryset().exclude(return_status=StockReturn.ReturnStatusChoices.CANCELLED)

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'search':
            return StockReturnListSerializer
        return StockReturnSerializer

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新退货单"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_return = self.perform_create_or_update(serializer, request.zt_user, is_update=False)
        serializer = self.get_serializer(stock_return)
        return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)

    def perform_create_or_update(self, serializer, zt_user, is_update=False):
        is_commit = serializer.validated_data.pop('is_commit', 0)
        items_data = serializer.validated_data.pop('items', [])
        allocated_cost_data = serializer.validated_data.pop('allocated_cost', None)
        if len(items_data) == 0:
            raise WmsException(_("退货单必须包含至少一个物品"))

        if is_update:
            stock_return = serializer.instance
            if stock_return.return_status != StockReturn.ReturnStatusChoices.DRAFT:
                raise WmsException(_("只能修改待处理状态的退货单"))
            
            for field, value in serializer.validated_data.items():
                setattr(stock_return, field, value)
            stock_return.save()
        else:
            order_id = Counter.SysDayCounter("StockReturn")
            stock_return = StockReturn.objects.create(**serializer.validated_data, handler=zt_user, total_amount=0, order_id=order_id)

        purchase_in: PurchaseIn = stock_return.purchase_in
        if purchase_in is None:
            raise WmsException(_("退货单必须关联采购入库"))
        if purchase_in.supplier != stock_return.supplier:
            raise WmsException(_("退货单的供应商与采购入库的供应商不一致"))

        purchase_in_items: list[PurchaseInItem] = PurchaseInItem.objects.filter(purchase_in=purchase_in)

        track_ids = [];
        for purchase_in_item in purchase_in_items:
            if purchase_in_item.track_id > 0 and purchase_in_item.track_id not in track_ids :
                # 入库订单存在的所有的跟踪ID
                track_ids.append(purchase_in_item.track_id);

        total_amount = 0
        return_items: list[StockReturnItem] = []
        short_desc = ""

        warehouse: Warehouse = None;
        for item_data in items_data:
            return_item_id = item_data.get('id', 0)
            if return_item_id > 0:
                return_item = StockReturnItem.objects.filter(id=return_item_id).first()
                if return_item is None:
                    raise WmsException(_("退货物品不存在") + " " + str(return_item_id))
                for field, value in item_data.items():
                    setattr(return_item, field, value)
                return_item.track_id = return_item.stock.track_id
                return_item.save()
            else:
                stock = item_data.get('stock', None)
                return_item = StockReturnItem.objects.create(**item_data, stock_return=stock_return, track_id=stock.track_id)

            # 检查是来源于同一个订单的退货单
            if not warehouse :
                warehouse = return_item.stock.warehouse;
            elif warehouse != return_item.stock.warehouse:
                raise WmsException(_("退货商品不在一个仓库中") + f" {warehouse.name} != {return_item.stock.warehouse.name}")

            if return_item.track_id not in track_ids:
                raise WmsException(_("退货商品非该批次物品" + return_item.stock.item.name) )

            # 验证退货数量不超过库存剩余数量
            if return_item.quantity > return_item.stock.remaining_quantity:
                raise WmsException(_("退货数量不能超过库存剩余数量") + f" {return_item.stock.item.name}")

            # 计算退货金额
            return_amount = return_item.quantity * return_item.return_price
            total_amount += return_amount

            # 生成简要描述
            if len(return_items) < 3:
                if len(short_desc + return_item.stock.item.name) < 50:
                    short_desc += return_item.stock.item.name + " "

            return_items.append(return_item)

        if len(return_items) > 3:
            short_desc += "..."

        stock_return.short_desc = short_desc
        # 更新退货单总金额和状态
        stock_return.total_amount = total_amount
        stock_return.actual_amount = total_amount - stock_return.discount
        stock_return.items_count = len(return_items)
        if is_commit == 1:
            stock_return.return_status = StockReturn.ReturnStatusChoices.COMPLETED
            # 更新库存
            self.handle_stock_return(stock_return, return_items)
        else:
            stock_return.return_status = StockReturn.ReturnStatusChoices.DRAFT
        
        self.calculate_allocated_cost(stock_return, allocated_cost_data, is_commit)

        if is_commit == 1 and stock_return.pay_amount > 0:
            if stock_return.pay_amount > stock_return.actual_amount:
                raise WmsException(_("退款金额不能大于退货金额"))
            if stock_return.payment_method is None:
                raise WmsException(_("付款方式不能为空"))
            Payment.objects.create(
                order_type=Payment.OrderTypeChoices.STOCK_RETURN,
                order_id=stock_return.order_id,
                amount= -stock_return.pay_amount,
                actual_amount= -stock_return.pay_amount,
                payment_method=stock_return.payment_method,
                supplier=stock_return.supplier,
                handler=zt_user,
                controller=zt_user,
                remark=_("库存退货")
            )
            stock_return.refunded_amount = stock_return.pay_amount

        # 如果是更新操作，删除不在本次提交中的退货物品
        if is_update:
            StockReturnItem.objects.filter(stock_return=stock_return).exclude(
                id__in=[item.id for item in return_items]
            ).delete()
        stock_return.save()

        return stock_return

    def handle_stock_return(self, stock_return: StockReturn, return_items: list[StockReturnItem]):
        """处理退货时的库存更新"""
        for return_item in return_items:
            stock:Stock = return_item.stock
            if stock.remaining_quantity < return_item.quantity:
                # 可能在订单提交后，库存又发生了修改了
                raise WmsException(_("退货数量不能超过库存剩余数量") + f" {stock.item.name}")
            # 更新库存剩余数量
            stock.remaining_quantity = F('remaining_quantity') - return_item.quantity
            stock.save()
            StockHistoryAdmin.DecStockHistory(stock, StockHistory.HistoryTypeChoices.STOCK_RETURN, stock_return.order_id, return_item.id, stock.track_id, return_item.quantity)
    

    def calculate_allocated_cost(self, stock_return: StockReturn, allocated_cost_data: dict, is_commit: int):
        source_type = CostAllocation.SourceTypeChoices.PURCHASE_IN;
        allocated_cost_obj = AllocatedCostAdmin.create_or_update(allocated_cost_data, stock_return.handler, source_type, stock_return.order_id)
        if allocated_cost_obj is None:
            return None;
        allocated_pay_amount = stock_return.allocated_pay_amount;
        allocated_payment_method = stock_return.allocated_payment_method;
        # 这里如果 有均摊的付款，则生成付款单
        if is_commit == 1:
            if allocated_pay_amount > 0:
                if allocated_pay_amount > allocated_cost_obj.total_cost:
                    raise WmsException(_("支付费用大于分摊费用") + " " + str(allocated_pay_amount) + " " + str(allocated_cost_obj.total_cost))
                if allocated_payment_method is None:
                    raise WmsException(_("付款方式不能为空") + " " + str(allocated_payment_method))
                AllocatedCostAdmin.create_payment(allocated_cost_obj, allocated_pay_amount, allocated_payment_method)
            # 有欠款
            AllocatedCostAdmin.create_payable(allocated_cost_obj)

        return allocated_cost_obj.total_cost - allocated_cost_obj.discount;

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新退货单信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        stock_return = self.perform_create_or_update(serializer, request.zt_user, is_update=True)
        serializer = self.get_serializer(stock_return)
        return make_response(code=0, msg=_('更新成功'), data=serializer.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    @action(detail=True, methods=['put'])
    def cancel(self, request, pk=None, **kwargs):
        """取消退货单"""
        instance = self.get_object()
        if instance.return_status != StockReturn.ReturnStatusChoices.DRAFT:
            raise WmsException(_("只能取消待处理状态的退货单"))
        instance.return_status = StockReturn.ReturnStatusChoices.CANCELLED
        instance.save()
        return make_response(code=0, msg=_('取消成功'), status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索退货单"""
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=1, msg='请输入搜索关键词', status=status.HTTP_400_BAD_REQUEST)

        # 先找出匹配的供应商和经办人
        from django.db.models import Q
        from WmsCore.models import Supplier, ZTUser, Item

        # 查找匹配的供应商ID
        supplier_ids = Supplier.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 查找匹配的经办人ID
        handler_ids = ZTUser.objects.filter(name__icontains=query).values_list('id', flat=True)
        
        # 查找匹配的物品ID
        item_ids = Item.objects.filter(name__icontains=query).values_list('id', flat=True)

        # 构建查询条件
        filter_query = Q()
        
        # 添加供应商查询
        if supplier_ids:
            filter_query |= Q(supplier_id__in=supplier_ids)
            
        # 添加经办人查询
        if handler_ids:
            filter_query |= Q(handler_name__in=handler_ids)
            
        # 添加退货原因查询
        if query:
            filter_query |= Q(return_reason__icontains=query)
            
        # 添加物品查询 (如果需要)
        if item_ids:
            filter_query |= Q(items__stock__item_id__in=item_ids)
        
        # 执行查询
        stock_returns = StockReturn.objects.filter(filter_query).exclude(
            return_status=StockReturn.ReturnStatusChoices.CANCELLED
        ).distinct()

        serializer = self.get_serializer(stock_returns, many=True)
        return make_response(code=0, msg='搜索成功', data=serializer.data, status=status.HTTP_200_OK)
