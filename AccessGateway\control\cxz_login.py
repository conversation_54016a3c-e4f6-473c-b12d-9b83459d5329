import random
import string
from rest_framework import status
from django.db import transaction
from AccessGateway.control.auth import make_response
from AccessGateway.control.tenant import TenantMgr
from AccessGateway.models import CXZUser, StatusPermission
from AccessGateway.model.ret_code import RetCode
from AccessGateway.models import Enterprise, Ledger
from common.APIViewSafe import APIViewSafe
from common.exception import WmsException
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _
from common.auth.jwt_auth import JWTAuthenticationWithNoPhoneCheck


class CxzLoginAPIView(APIViewSafe):
    """处理微信小程序登录/注册"""

    authentication_classes = [JWTAuthenticationWithNoPhoneCheck]
    def NewLedger(self, enterprise:Enterprise):
        ledger_name = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
        ledger = Ledger.objects.filter(ledger_name=ledger_name).first();
        if ledger is not None:
            return self.NewLedger(enterprise);
        ledger = Ledger.objects.create(name=f"{enterprise.enterprise_name}的账套", ledger_name=ledger_name, enterprise=enterprise);

        from WmsCore.data.make_datas import make_default_data_to_db
        #初始化最基础的数据
        make_default_data_to_db(ledger_name)

        return ledger;

    def AllocEnterpriseToOwn(self, platform_account:CXZUser):
        # 分配企业信息
        user_nick_name = platform_account.nick_name;
        enterprise = Enterprise.objects.create(enterprise_name=f"{user_nick_name}的企业");
        # 分配账套
        tenant_ledger = self.NewLedger(enterprise);
        if tenant_ledger is None:
            log.error(f"没有可用的账套,创建失败")
            raise   WmsException("没有可用的账套");

        # tenant_ledger.enterprise = enterprise;
        # tenant_ledger.name = tenant_ledger.schema_name;
        # tenant_ledger.save();
        platform_account.owner_enterprise = enterprise;
        platform_account.enterprise = enterprise;
        platform_account.account_type = CXZUser.AccountTypeChoices.ADMIN;
        platform_account.save();
        log.info(f"分配企业信息: {enterprise.enterprise_name} ({enterprise.id}) 到 user_id: {platform_account.id},基础账套: {tenant_ledger.ledger_name}({tenant_ledger.id})");
        return enterprise;


    def GetAvailableLedger(self, platform_account:CXZUser, enterprise:Enterprise):
        tenant_ledgers = Ledger.objects.filter(enterprise=enterprise.id).all();
        if tenant_ledgers.count() == 0:
            log.error(f"企业没有可用的账套,需要提前预分配,企业: {enterprise.enterprise_name} ({enterprise.id})")
            return [];
        # 获取可用账套信息
        ledgers = [];
        for tenant_ledger in tenant_ledgers:
            ledgers.append(tenant_ledger);
        permission_list = StatusPermission.objects.filter(platform_account=platform_account, status=True, frozen_status=False).all();
        if permission_list.count() == 0:
            # 没有设置权限，默认全通过
            log.debug(f"企业账套权限没有设置，默认全部可以用,企业: {enterprise.enterprise_name} ({enterprise.id})");
            return ledgers;
        ledger_ids = [ledger.id for ledger in ledgers];
        available_tenants = [];
        for permission in permission_list:
            if permission.ledger_id in ledger_ids:
                available_tenants.append(permission.ledger);
        return available_tenants;

    @transaction.atomic
    def post(self, request):
        """处理仓小助登录，获取基础信息"""
        try:
            user_id = request.user_id;
            platform_account = CXZUser.objects.filter(id=user_id).first();
            if platform_account is None:
                return make_response(
                    code=RetCode.ACCOUNT_NOT_LOGIN,
                    msg=_("请先登录"),
                    status=status.HTTP_401_UNAUTHORIZED
                )
            if platform_account.mobile is None:
                return make_response(
                    code=RetCode.ACCOUNT_NOT_LOGIN,
                    msg=_("请先绑定手机号"),
                    status=status.HTTP_401_UNAUTHORIZED
                )
            is_own = 0;
            enterprise = Enterprise.objects.filter(id=platform_account.enterprise_id).first();
            if enterprise is None:
                #TODO 如果有限量注册，在这里处理一下
                enterprise = self.AllocEnterpriseToOwn(platform_account);
                # # 处理个人企业信息，如果信息不存在，则自动创建一个
                # own_enterprise = Enterprise.objects.filter(id=platform_account.owner_enterprise_id).first();
                # if own_enterprise is None:
                #     own_enterprise = self.AllocEnterpriseToOwn(platform_account);
                # enterprise = own_enterprise;
            if platform_account.owner_enterprise_id == platform_account.enterprise_id:
                is_own = 1;
            data = {
                "enterprise_name": enterprise.enterprise_name,
                "enterprise_id": enterprise.id,
            }
            log.debug(f"登录请求: {data} is_own: {is_own}")
            # 获取可用账套信息
            available_ledgers = self.GetAvailableLedger(platform_account, enterprise);
            log.debug(f"可用账套信息: {available_ledgers[0].ledger_name} 企业: {enterprise.enterprise_name} ({enterprise.id})")
            if len(available_ledgers) == 0:
                if is_own == 0:
                    # 他人企业，那没有空用账套的话，提醒用户联系管理员操作授权
                    data['code'] = RetCode.LEDGER_NOT_AVAILABLE;
                    data['msg'] = _("请联系管理员操作授权");
                    return make_response(
                        code=RetCode.LEDGER_NOT_AVAILABLE,
                        msg=_("请联系管理员操作授权"),
                        data=data,
                        status=status.HTTP_400_BAD_REQUEST
                    );
                else:
                    # 没有可用账套，则分配一个
                    platform_account.last_ledger_id = 0;
                    platform_account.enterprise = None;
                    platform_account.save();
                    data['code'] = RetCode.REGISTER_COUNT_LIMIT;
                    data['msg'] = "";
                    return make_response(
                        code=RetCode.REGISTER_COUNT_LIMIT,
                        msg=_("请联系管理员操作授权"), 
                        data=data,
                        status=status.HTTP_400_BAD_REQUEST
                    );
            use_last_ledger = False;
            if platform_account.last_ledger_id is not None and platform_account.last_ledger_id != 0 :
                available_ledger_ids = [ledger.id for ledger in available_ledgers];
                if platform_account.last_ledger_id in available_ledger_ids:
                    use_last_ledger = True;
                    last_ledger_index = available_ledger_ids.index(platform_account.last_ledger_id);
                    data['ledger_id'] = platform_account.last_ledger_id;
                    data['ledger_name'] = available_ledgers[last_ledger_index].ledger_name;
                    data['display_name'] = available_ledgers[last_ledger_index].name;
                    data['ledger_schema_name'] = available_ledgers[last_ledger_index].ledger_name;
                    log.debug(f"使用最后一次操作的账套: {data['ledger_name']}({data['ledger_id']}):{data['ledger_schema_name']}")
                else:
                    data['ledger_id'] = available_ledgers[0].id;
                    data['ledger_name'] = available_ledgers[0].ledger_name;
                    data['display_name'] = available_ledgers[0].name;
                    data['ledger_schema_name'] = available_ledgers[0].ledger_name;
                    # 更新最后一次操作的账套
                    platform_account.last_ledger_id = data['ledger_id'];
                    platform_account.save();
                    log.debug(f" 最后使用的已经无效，使用第一个可用的账套: {data['ledger_name']}({data['ledger_id']}):{data['ledger_schema_name']}")
            else:
                data['ledger_id'] = available_ledgers[0].id;
                data['ledger_name'] = available_ledgers[0].ledger_name;
                data['display_name'] = available_ledgers[0].name;
                data['ledger_schema_name'] = available_ledgers[0].ledger_name;
                # 更新最后一次操作的账套
                platform_account.last_ledger_id = data['ledger_id'];
                platform_account.save();
                log.debug(f"使用第一个可用的账套: {data['ledger_name']}({data['ledger_id']}):{data['ledger_schema_name']}")
            
            return make_response(
                code=RetCode.SUCCESS,
                msg=_("登录成功"),
                data=data,
                status=status.HTTP_200_OK
            );
        except Exception as e:  
            import traceback;
            traceback.print_exc();
            log.error(f"处理微信登录异常 - 用户: {request.user_id}")
            return make_response(
                code=RetCode.UNKNOWN_ERROR,
                msg=_("登陆时发生错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            );