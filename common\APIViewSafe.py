from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from AccessGateway.permissions import IsPlatformAccount
from common.auth.jwt_auth import JWTAuthentication

@method_decorator(csrf_exempt, name='dispatch')
class APIViewSafe(APIView):
    """
    APIView 的子类，添加了安全校验功能
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsPlatformAccount]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if getattr(self, 'no_safe', False):
            self.authentication_classes = []
            self.permission_classes = [AllowAny]