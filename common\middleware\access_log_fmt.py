# myapp/middleware.py
import time
from common.logger import logger
from django.db import connection
from django.http import FileResponse

class AccessLogMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # 重置查询统计
        if connection.queries_log:
            connection.queries_log.clear()
        # 请求开始时间
        start_time = time.time()
        
        response = self.get_response(request)
        
        # 计算处理时长
        duration = time.time() - start_time
        total_db_time = 0
        if connection.queries:
            total_db_time = sum(float(q['time']) for q in connection.queries)
        
        # 获取客户端 IP
        ip = request.META.get('HTTP_X_FORWARDED_FOR') or request.META.get('REMOTE_ADDR')
        
        # 获取响应内容长度
        content_length = '-'
        if isinstance(response, FileResponse):
            if response.file_to_stream:
                try:
                    content_length = response.file_to_stream.size
                except (AttributeError, OSError):
                    pass
        else:
            try:
                content_length = len(response.content)
            except AttributeError:
                pass
        
        # 格式化日志
        log_data = (
            f'"{request.method} {request.path} {request.META["SERVER_PROTOCOL"]}" '
            f'{response.status_code} {content_length} '
            f'[{ip}] ({duration:.2f}s db:{total_db_time:.2f}s)'
        )
        
        # 记录日志 (可根据需要调整级别)
        if response.status_code >= 500:
            logger.error(log_data)
        elif response.status_code >= 400:
            logger.warning(log_data)
        else:
            logger.debug(log_data)
            
        return response