from django.db import models, transaction
from rest_framework import status
from rest_framework.decorators import action

from AccessGateway.control.auth import make_response
from WmsCore.control.base import SafeModelViewSet, StandardResultsSetPagination
from WmsCore.models import Customer, CatalogTypeTree
from WmsCore.utils.submission import prevent_duplicate_submission
from rest_framework import serializers
from common.exception import WmsException
from common.make_response import make_response
from django.utils.translation import gettext_lazy as _
from common.translation import _T
class CustomerSerializer(serializers.ModelSerializer):
    customer_type_name = serializers.CharField(source='customer_type.name', read_only=True)
    phone = serializers.CharField(max_length=20, required=False,allow_blank=True, label=_T('手机号'))
    contact_person = serializers.CharField(max_length=20, required=False,allow_blank=True, label=_T('联系人'))
    address = serializers.CharField(max_length=100, required=False,allow_blank=True, label=_T('地址'))
    class Meta:
        model = Customer
        fields = [
            'id', 'name', 'contact_person', 'phone', 'address', 'customer_type', 
            'create_date', 'last_contact_date', 'last_purchase_date', 'customer_type_name'
        ]
        read_only_fields = ['create_date']
    
    def validate_customer_type(self, value):
        """验证customer_type字段"""
        if value is not None:
            # 验证传入的customer_type必须是客户类型
            if value.for_type != CatalogTypeTree.ForTypeChoices.CUSTOMER:
                raise WmsException(_('客户类型必须是客户分类'))
        return value
    
    def validate(self, attrs):
        """验证并设置默认的customer_type"""
        customer_type = attrs.get('customer_type')
        
        # 如果没有传入customer_type，则使用默认的第一个客户分类
        if customer_type is None:
            default_customer_type = CatalogTypeTree.objects.filter(
                for_type=CatalogTypeTree.ForTypeChoices.CUSTOMER
            ).first()
            
            if default_customer_type:
                attrs['customer_type'] = default_customer_type
            else:
                raise WmsException(_('系统中没有可用的客户分类'))
        
        return attrs

class CustomerViewSet(SafeModelViewSet):
    """
    客户的增删改查接口
    """
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    pagination_class = StandardResultsSetPagination

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        """创建新客户"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, pk=None, **kwargs):
        """获取单个客户详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, pk=None, **kwargs):
        """更新客户信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        serializer = self.get_serializer(instance)
        return make_response(code=0, msg=_('更新成功'), data=serializer.data, status=status.HTTP_200_OK)

    def destroy(self, request, pk=None, **kwargs):
        """删除客户"""
        instance = self.get_object()
        instance.delete()
        return make_response(code=0, msg=_('删除成功'), data={}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索客户"""
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=0, msg=_('获取成功'), data={}, status=status.HTTP_200_OK)
        customers = Customer.objects.filter(
            models.Q(name__icontains=query) |
            models.Q(contact_person__icontains=query) |
            models.Q(phone__icontains=query) |
            models.Q(address__icontains=query)
        )
        serializer = self.get_serializer(customers, many=True)
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)