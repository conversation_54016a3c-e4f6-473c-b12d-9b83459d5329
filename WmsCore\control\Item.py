import re
from django.db import models
from django.db import transaction
from django.http import JsonResponse
from requests import Response
from rest_framework import status
from rest_framework.decorators import action
from rest_framework import pagination
from django.db.models import Prefetch
from rest_framework import serializers
from AccessGateway.control.auth import make_response
from WmsCore.admin.warehouse import WarehouseAdmin
from WmsCore.control.base import SafeModelViewSet, HugeResultsSetPagination, StandardResultsSetPagination
from WmsCore.models import CatalogTypeTree, Item, UnitType, Unit, Warehouse, Stock, StockHistory
from WmsCore.control.Image import MultiImagesField
#from WmsCore.serializers import ItemSerializer
from WmsCore.utils.serializer_details import SerializerCollector
from WmsCore.utils.submission import prevent_duplicate_submission
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _
from common.translation import _T
from django.db.models import Q
from django_redis import get_redis_connection
from WmsCore.utils.counter import Counter
from common.db.Redis import RedisUtil
import json
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.response import Response
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import os
from django.db import connection
from PIL import Image
import io
import hashlib
from django.conf import settings
from django.http import FileResponse
from rest_framework.exceptions import ValidationError, NotFound
from common.exception import WmsException
from common.make_response import make_response
from WmsCore.admin.stock_history import StockHistoryAdmin
from django.db.models import ProtectedError
from django.db import IntegrityError

REDIS_ITEM_CODE_KEY = "wms:item:code:{}"
REDIS_ITEM_CODE_EXPIRE = 3600  # 1小时
@SerializerCollector(alias="物品计量单位类型")
class UnitTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnitType
        fields = ['id', 'name', ]

@SerializerCollector(alias="物品计量单位")
class UnitSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True, label=_T('物品计量单位ID'))
    unit_type_id = serializers.PrimaryKeyRelatedField(
        queryset=UnitType.objects.all(), 
        source='unit_type', 
        label=_T('物品计量单位类型ID')
    )   
    
    # 新增只读字段 - 用于响应中展示
    unit_type_name = serializers.CharField(
        source='unit_type.name',
        read_only=True  # 只读，不参与写入
    )

    retail_price = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True, default=0, label=_T('物品零售价'))
    wholesale_price = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True, default=0, label=_T('物品批发价'))
    min_price = serializers.DecimalField(max_digits=30, decimal_places=8, required=False, allow_null=True, default=0, label=_T('物品最低价格'))

    def validate_min_price(self, value):
        if value < 0:
            raise WmsException(_("最低价格不能为负数"))
        return value

    class Meta:
        model = Unit
        fields = ['id', 'unit_type_id', 'conversion_rate', 'retail_price', 'wholesale_price', 'unit_type_name', 'min_price']

    def to_internal_value(self, data):
        # 把空字符串转为0
        for field in ['retail_price', 'wholesale_price', 'min_price']:
            if field in data and (data[field] is None or data[field] == ''):
                data[field] = 0
        return super().to_internal_value(data)
# @SerializerCollector(alias="物品分类")
# class ItemTypeSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = ItemType
#         fields = ['id', 'name', 'parent']

# @SerializerCollector(alias="物品分类树")
# class ItemTypeTreeSerializer(serializers.ModelSerializer):
#     children = serializers.SerializerMethodField()

#     class Meta:
#         model = ItemType
#         fields = ['id', 'name', 'parent', 'children']

#     def get_children(self, obj):
#         children = ItemType.objects.filter(parent=obj)
#         return ItemTypeTreeSerializer(children, many=True).data

@SerializerCollector(alias="物品列表")
class ItemListSerializer(serializers.ModelSerializer):
    """用于列表查询的序列化器，只返回指定字段"""
    unit_name = serializers.CharField(source='unit.unit_type.name', read_only=True, label=_T('物品计量单位名称'))
    item_type_name = serializers.CharField(source='item_type.name', read_only=True, label=_T('物品分类名称'))
    images = MultiImagesField(required=False, label=_T('物品图片'))
    retail_price = serializers.DecimalField(max_digits=30, decimal_places=8, source='unit.retail_price', read_only=True, label=_T('物品零售价'))
    wholesale_price = serializers.DecimalField(max_digits=30, decimal_places=8, source='unit.wholesale_price', read_only=True, label=_T('物品批发价'))
    min_price = serializers.DecimalField(max_digits=30, decimal_places=8, source='unit.min_price', read_only=True, label=_T('物品最低价格'))
    warehouse=serializers.PrimaryKeyRelatedField(queryset=Warehouse.objects.all(), label=_T('仓库'),required=False,allow_null=True)
    warehouse_stock = serializers.SerializerMethodField(label=_T('仓库库存数量'))

    class Meta:
        model = Item
        fields = [
            'id', 'code', 'name', 'item_type_name', 'total_stock', 'total_cost', 'expiry_quantity',
            'unit', 'unit_name', 'order_demand', 'order_lock', 'reorder_cycle', 'expiry_days',
            'is_active', 'serial_number', 'images', 'retail_price', 'wholesale_price', 'min_price',
            'warehouse', 'warehouse_stock'
        ]

    def get_warehouse_stock(self, obj):
        """获取仓库库存数量"""
        warehouse_id = self.context.get('warehouse_id')
        
        # 使用 Sum 聚合函数计算库存数量
        from django.db.models import Sum
        
        if warehouse_id:
            # 如果指定了仓库ID，返回该仓库的库存数量
            stock_quantity = Stock.objects.filter(
                item=obj,
                warehouse_id=warehouse_id
            ).aggregate(
                total=Sum('remaining_quantity')
            )['total'] or 0
            return stock_quantity
        else:
            # 如果没有指定仓库ID，返回所有仓库的库存数量
            stocks = Stock.objects.filter(
                item=obj
            ).values(
                'warehouse_id',
                'warehouse__name'
            ).annotate(
                quantity=Sum('remaining_quantity')
            ).order_by('warehouse_id')
            
            return [
                {
                    'warehouse_id': stock['warehouse_id'],
                    'warehouse_name': stock['warehouse__name'],
                    'quantity': stock['quantity'] or 0
                }
                for stock in stocks
            ]


class StockInitSerializer(serializers.Serializer):
    warehouse = serializers.PrimaryKeyRelatedField(queryset=Warehouse.objects.all(), label=_T('仓库'))
    quantity = serializers.DecimalField(max_digits=30, decimal_places=8, required=True, label=_T('数量'))
    unit_type_id = serializers.PrimaryKeyRelatedField(queryset=UnitType.objects.all(), required=True, label=_T('单位类型'))
    actual_cost = serializers.DecimalField(max_digits=30, decimal_places=8, required=True, label=_T('成本'))
    def validate_unit_type(self, value):
        if not UnitType.objects.filter(pk=value.pk).exists():
            raise WmsException(_T('单位类型不存在'))
        return value

@SerializerCollector(alias="物品")
class ItemSerializer(serializers.ModelSerializer):
    units = UnitSerializer(many=True, required=False,label=_T('物品计量单位'))
    main_unit_id = serializers.IntegerField(source='unit.id', read_only=True, label=_T('物品主计量单位ID'))
    images = MultiImagesField(required=False, label=_T('物品图片'))
    code = serializers.CharField(read_only=True, label=_T('物品编码'))
    item_type_name=serializers.CharField(source='item_type.name', read_only=True, label=_T('物品分类名称'))
    stocks = StockInitSerializer(many=True, required=False, label=_T('物品库存'))
    on_way_quantity = serializers.SerializerMethodField(label=_T('在途数量'))
    warehouse_stock = serializers.SerializerMethodField(label=_T('仓库库存数量'))
    class Meta:
        model = Item
        fields = [
            'id', 'code', 'name', 'item_type', 'total_stock', 'total_cost', 'expiry_quantity',
            'unit', 'main_unit_id', 'order_demand', 'order_lock', 'reorder_cycle', 'expiry_days',
            'over_sell_quantity', 'is_over_sell', 'is_active', 'serial_number', 'color', 'brand',
            'model', 'base_weight', 'location', 'manufacturer', 'specification', 'units', 'images',
            'item_type_name', 'stocks', 'on_way_quantity', 'warehouse_stock'
        ]
        read_only_fields = [
            'total_stock', 'order_demand', 'order_lock', 'main_unit_id', 'on_way_quantity', 'warehouse_stock'
        ]
        
    def get_warehouse_stock(self, obj):
        """获取仓库库存数量"""
        warehouse_id = self.context.get('warehouse_id')
        
        # 使用 Sum 聚合函数计算库存数量
        from django.db.models import Sum
        
        if warehouse_id:
            # 如果指定了仓库ID，返回该仓库的库存数量
            stock_quantity = Stock.objects.filter(
                item=obj,
                warehouse_id=warehouse_id
            ).aggregate(
                total=Sum('remaining_quantity')
            )['total'] or 0
            return stock_quantity
        else:
            # 如果没有指定仓库ID，返回所有仓库的库存数量
            stocks = Stock.objects.filter(
                item=obj
            ).values(
                'warehouse_id',
                'warehouse__name'
            ).annotate(
                quantity=Sum('remaining_quantity')
            ).order_by('warehouse_id')
            
            return [
                {
                    'warehouse_id': stock['warehouse_id'],
                    'warehouse_name': stock['warehouse__name'],
                    'quantity': stock['quantity'] or 0
                }
                for stock in stocks
            ]
    def get_on_way_quantity(self, obj):
        """获取在途数量（主单位）"""
        from WmsCore.models import PurchaseOrderItem
        from django.db import models
        from decimal import Decimal
        
        po_items = PurchaseOrderItem.objects.filter(
            item=obj,
            delivered_quantity__lt=models.F('quantity')
        ).select_related('unit', 'purchase_order')
        
        on_way_main_qty = Decimal('0')
        for po_item in po_items:
            remaining_qty = (po_item.quantity - po_item.delivered_quantity) or Decimal('0')
            if remaining_qty <= 0:
                continue
            on_way_main_qty += po_item.unit.conversion_rate * remaining_qty
        return on_way_main_qty

    def get_serializer_context(self):
        """添加额外的上下文信息"""
        context = super().get_serializer_context()
        if hasattr(self, 'context') and 'request' in self.context:
            context['request'] = self.context['request']
        return context

    def create(self, validated_data):
        units_data = validated_data.pop('units', [])
        item = Item.objects.create(**validated_data)
        
        # 处理单位
        for unit_data in units_data:
            Unit.objects.create(item=item, **unit_data)
            
        return item

    def update(self, instance, validated_data):
        units_data = validated_data.pop('units', [])
        item = super().update(instance, validated_data)
        
        # 处理单位
        if units_data:
            instance.units.all().delete()
            for unit_data in units_data:
                Unit.objects.create(item=instance, **unit_data)
                
        return item

    def to_internal_value(self, data):
        # 创建数据的可变副本
        mutable_data = data.copy() if hasattr(data, 'copy') else dict(data)
        
        # 适配 units 为字符串的情况
        if 'units' in mutable_data and isinstance(mutable_data['units'], str):
            try:
                mutable_data['units'] = json.loads(mutable_data['units'])
            except Exception:
                mutable_data['units'] = []
                
        return super().to_internal_value(mutable_data)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        sorted_units = self.context.get('sorted_units')
        if sorted_units is not None:
            # 用排序后的 units 序列化
            ret['units'] = UnitSerializer(sorted_units, many=True, context=self.context).data
        return ret
    
    def validate_item_type(self, value):
        """验证item_type字段"""
        if value is not None:
            # 验证传入的item_type必须是物品类型
            if value.for_type != CatalogTypeTree.ForTypeChoices.ITEM:
                raise WmsException(_('物品类型必须是物品分类'))
        return value
    
    def validate(self, attrs):
        """验证并设置默认的item_type"""
        item_type = attrs.get('item_type')
        
        # 如果没有传入item_type，则使用默认的第一个物品分类
        if item_type is None:
            default_item_type = CatalogTypeTree.objects.filter(
                for_type=CatalogTypeTree.ForTypeChoices.ITEM
            ).first()
            
            if default_item_type:
                attrs['item_type'] = default_item_type
            else:
                raise WmsException(_('系统中没有可用的物品分类'))
        
        return attrs


class UnitTypeViewSet(SafeModelViewSet):
    """
    计量单位类型增删改查接口
    """
    queryset = UnitType.objects.all()
    serializer_class = UnitTypeSerializer
    pagination_class = HugeResultsSetPagination
  
    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        按名称模糊搜索计量单位类型
        """
        query = request.query_params.get('query', '')
        if not query:
            return JsonResponse({'code': 1, 'msg': '请输入搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)
        unit_types = UnitType.objects.filter(Q(name__icontains=query))
        serializer = self.get_serializer(unit_types, many=True)
        return make_response(code=0, msg=_('搜索成功'), data=serializer.data, status=status.HTTP_200_OK)

# class ItemTypeViewSet(SafeModelViewSet):
#     """
#     物品类型增删改查接口
#     """
#     queryset = ItemType.objects.all()
#     serializer_class = ItemTypeSerializer
#     pagination_class = HugeResultsSetPagination

#     def get_serializer_class(self):
#         if self.action == 'list':
#             return ItemTypeTreeSerializer
#         return ItemTypeSerializer

#     def list(self, request, *args, **kwargs):
#         queryset = ItemType.objects.all()
#         roots = queryset.filter(parent=None)
#         serializer = self.get_serializer(roots, many=True)
#         count = queryset.count()
#         data = {
#             "count": count,
#             "next": None,
#             "previous": None,
#             "results": serializer.data
#         }
#         return make_response(code=0, msg=_('获取成功'), data=data, status=status.HTTP_200_OK)

#     def destroy(self, request, *args, **kwargs):
#         instance = self.get_object()
#         if instance.id == 1:
#             raise serializers.ValidationError(_("根类型不能删除"))
#         try:
#             instance.delete()
#             return make_response(code=0, msg=_('删除成功'), data={}, status=status.HTTP_200_OK)
#         except ProtectedError:
#             return make_response(code=1, msg=_('该分类已添加商品，不允许删除'), data={})
#         except IntegrityError:
#             return make_response(code=1, msg=_('该分类已添加商品，不允许删除'), data={})

def generate_item_code():
    for _ in range(20):  # 最多尝试20次
        num = 100000 + Counter.CounterInt("ITEM")
        code = f"{num:05d}"
        redis_key = REDIS_ITEM_CODE_KEY.format(code)
        # 只有不存在时才设置，防止并发重复
        if RedisUtil.set(redis_key, 2000, timeout=REDIS_ITEM_CODE_EXPIRE, nx=True):
            return code
    raise Exception("生成物品编码失败，请重试")

def release_item_code(code):
    redis_key = REDIS_ITEM_CODE_KEY.format(code)
    RedisUtil.delete(redis_key)

class ItemViewSet(SafeModelViewSet):
    """
    物品的增删改查接口
    """
    # TODO 物品库存的刷新时间
    queryset = Item.objects.prefetch_related(
            Prefetch('units', queryset=Unit.objects.select_related('unit_type' ))
        ).all().order_by('-is_active')
    serializer_class = ItemSerializer
    pagination_class = StandardResultsSetPagination
    def get_queryset(self):
        """根据include_inactive参数过滤查询集"""
        queryset = super().get_queryset()
        
        # 检查是否包含禁用物品的参数
        include_inactive = self.request.query_params.get('include_inactive', 'true').lower()
        if include_inactive in ['false', '0']:
            # 只有明确指定不包含禁用物品时才过滤
            queryset = queryset.filter(is_active=True)

        # 检查是否按仓库过滤
        warehouse_id = self.request.query_params.get('warehouse_id')
        if warehouse_id:
            try:
                warehouse_id = int(warehouse_id)
                # 只返回在指定仓库中有库存的物品
                queryset = queryset.filter(
                    stock__warehouse_id=warehouse_id,
                    stock__remaining_quantity__gt=0
                ).distinct()
            except (ValueError, TypeError):
                # 如果warehouse_id不是有效整数，忽略过滤
                pass

        return queryset

    def get_serializer_context(self):
        """确保序列化器获得完整的上下文信息"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def get_serializer_class(self):
        if self.action == 'list' or self.action == 'search':
            return ItemListSerializer
        return ItemSerializer
    
    def get_serializer(self, *args, **kwargs):
        """确保序列化器使用优化后的查询集"""
        if self.action in ['create', 'update', 'partial_update']:
            # 对于写操作，不需要预取关系
            return super().get_serializer(*args, **kwargs)
        
        # 对于读操作，使用优化后的查询集
        if 'context' not in kwargs:
            kwargs['context'] = self.get_serializer_context()
            
        # 获取带关系的实例
        instance = kwargs.get('instance', None)
        if instance and isinstance(instance, models.Model):
            instance = self.get_queryset().get(id=instance.id)
            kwargs['instance'] = instance
            
        return super().get_serializer(*args, **kwargs)
    
    def retrieve(self, request, pk=None, **kwargs):
        """
        获取单个物品详情（已优化，单位按 conversion_rate 升序）
        """
        item = self.get_object()
        log.debug(f"retrieve item: {item} (id: {item.id})")
        sorted_units = item.units.all().order_by('conversion_rate')
        serializer = self.get_serializer(item, context={**self.get_serializer_context(), 'sorted_units': sorted_units})
        return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)

    def validate_create_data(self, data):
        # 前置校验逻辑
        name = data.get('name')
        if name and Item.objects.filter(name=name).exists():
            raise WmsException(_T('物品名称"{name}"已存在，请更换', name=name))

        units_data = data.get('units', [])
        if not units_data or not isinstance(units_data, list):
            raise WmsException(_T('请至少设置一个计量单位'))
        main_units = [u for u in units_data if str(u.get('conversion_rate', 0)) == '1']
        if len(main_units) != 1:
            raise WmsException(_T('必须有且仅有一个兑换率为1的主计量单位'))
        # 校验单位类型外键是否存在
        for idx, unit in enumerate(units_data):
            unit_type_id = unit.get('unit_type_id') or unit.get('unit_type')
            if not unit_type_id or not UnitType.objects.filter(pk=unit_type_id).exists():
                raise WmsException(_T('第{idx}个单位的单位类型不存在', idx=idx+1))

        stocks_data = data.get('stocks', [])
        if stocks_data and not isinstance(stocks_data, list):
            raise WmsException(_T('初始库存格式错误'))
        for idx, stock in enumerate(stocks_data):
            warehouse_id = stock.get('warehouse')
            unit_type_id = stock.get('unit_type_id')
            if not warehouse_id:
                raise WmsException(_T('第{idx}条初始库存未指定仓库', idx=idx+1))
            if not Warehouse.objects.filter(pk=warehouse_id).exists():
                raise WmsException(_T('第{idx}条初始库存指定的仓库不存在', idx=idx+1))
            if not unit_type_id:
                raise WmsException(_T('第{idx}条初始库存未指定单位类型', idx=idx+1))
            if not UnitType.objects.filter(pk=unit_type_id).exists():
                raise WmsException(_T('第{idx}条初始库存指定的单位类型不存在', idx=idx+1))
            try:
                quantity = float(stock.get('quantity', 0))
            except Exception:
                raise WmsException(_T('第{idx}条初始库存数量格式错误', idx=idx+1))
            if quantity <= 0:
                raise WmsException(_T('第{idx}条初始库存数量必须大于0', idx=idx+1))

    def _create_initial_stock(self, item, stock_data):
        """创建初始库存的通用方法"""
        warehouse = stock_data.get('warehouse')
        quantity = stock_data.get('quantity', 0)
        unit_type = stock_data.get('unit_type_id')
        actual_cost = stock_data.get('actual_cost', 0)
        
        # 查找该物品下对应 unit_type 的 unit
        unit = Unit.objects.filter(item=item, unit_type=unit_type).first()
        if not unit:
            raise WmsException(_("物品未设置该单位类型的计量单位"))
        
        # 生成批次号和入库单号
        batch_number = Counter.SysDayCounter("InitialIn")
        in_order_id = Counter.SysDayCounter("InitialIn")
        
        stock = Stock.objects.create(
            item=item,
            warehouse=warehouse,
            unit=item.unit,
            in_type=Stock.StockInChoices.INITIAL_IN,
            in_order_id=in_order_id,
            batch_number=batch_number,
            track_id=0,
            quantity=quantity*unit.conversion_rate,
            expiry_days=0,
            actual_cost=actual_cost,
            allocated_cost=0,
            out_cost=0,
            remaining_quantity=quantity,
        )
        stock.track_id = stock.id
        stock.save()
        
        # 更新仓库物品数量
        WarehouseAdmin.UpdateItemQuantity(item, warehouse)
        
        # 添加库存历史记录
        StockHistoryAdmin.AddStockHistory(
            stock=stock,
            history_type=StockHistory.HistoryTypeChoices.INITIAL_IN,
            history_order_id=in_order_id,
            demand_item_keyid=item.id,
            track_id=stock.id,
            quantity=quantity
        )
        return stock

    def _process_units_data(self, item, units_data):
        """处理单位数据的通用方法"""
        # 批发价、零售价、最低售价都不是必填，未传或空字符串自动补0
        for unit in units_data:
            for price_field in ['wholesale_price', 'retail_price', 'min_price']:
                val = unit.get(price_field, None)
                if val in [None, '', []]:
                    unit[price_field] = 0
        
        main_unit = None
        unit_model_fields = {f.name for f in Unit._meta.get_fields()}
        
        for unit_data in units_data:
            unit_data.pop('id', None)
            # 只保留Unit模型字段
            clean_unit_data = {k: v for k, v in unit_data.items() if k in unit_model_fields}
            # 处理unit_type
            unit_type_id = unit_data.get('unit_type_id') or unit_data.get('unit_type')
            if not unit_type_id:
                raise WmsException(_("单位(unit)必须指定unit_type"))
            clean_unit_data['unit_type_id'] = unit_type_id
            clean_unit_data['item'] = item
            
            unit = Unit.objects.create(**clean_unit_data)
            if main_unit is None or main_unit.conversion_rate > unit.conversion_rate:
                main_unit = unit
        
        return main_unit

    def _validate_and_set_main_unit(self, item, main_unit):
        """验证并设置主单位"""
        if main_unit and main_unit.conversion_rate != 1:
            raise WmsException(_("主计量单位兑换率必须为1"))
        if main_unit:
            item.unit = main_unit
            item.save()
        else:
            raise WmsException(_("主计量单位不能为空"))

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['POST'])
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        # 先用自定义方法做前置校验
        self.validate_create_data(serializer.initial_data)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{getattr(request, 'user_id', None)} create item: {serializer.validated_data}")
        item = self.perform_create(serializer)
        # 重新获取完整对象（带prefetch等）
        item = self.get_queryset().get(id=item.id)
        serializer = self.get_serializer(item)
        return make_response(code=0, msg=_('创建成功'), data=serializer.data, status=status.HTTP_200_OK)

    def perform_create(self, serializer):
        data = serializer.initial_data
        code = serializer.initial_data.get('code')
        if not code:
            code = generate_item_code()
        else:
            code = generate_item_code()
        validated_data = serializer.validated_data
        validated_data['code'] = code

        # 处理 serial_number
        serial_number = validated_data.get('serial_number')
        if not serial_number or str(serial_number).strip() == '':
            validated_data['serial_number'] = code

        # 处理 units、stocks
        units_data = serializer.initial_data.get('units', []) if 'units' in serializer.initial_data else []
        validated_data.pop('units', None)
        stocks_data = validated_data.pop('stocks', [])
        
        # 创建 Item
        item = Item.objects.create(**validated_data)

        # 创建单位
        main_unit = self._process_units_data(item, units_data)
        self._validate_and_set_main_unit(item, main_unit)

        # 创建初始库存
        for stock_data in stocks_data:
            self._create_initial_stock(item, stock_data)
        
        release_item_code(code)
        return item

    @transaction.atomic
    @prevent_duplicate_submission(timeout=3, methods=['PUT'])
    def update(self, request, *args, **kwargs):
        # 自定义更新逻辑
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        log.info(f"user:{request.user_id} update item: {serializer.validated_data}")
        self.perform_update(serializer)
        
        # 获取带有关系的完整对象
        updated_instance = self.get_queryset().get(id=serializer.instance.id)
        serializer = self.get_serializer(updated_instance)
        
        return make_response(code=0, msg=_('更新成功'), data=serializer.data, status=status.HTTP_200_OK)

    def perform_update(self, serializer):
        # 在保存前处理关系
        units_data = serializer.validated_data.pop('units', [])
        stocks_data = serializer.validated_data.pop('stocks', [])
        item = serializer.save()

        # 更新关联单位
        existing_ids = set()
        current_ids = set(item.units.values_list('id', flat=True))
        log.debug(f"update_units item: {item.units.all()} units_data: {units_data}")

        # 处理所有单元记录
        main_unit = None
        for unit_data in units_data:
            unit_id = unit_data.get('id')
            log.debug(f"处理单位 unit_data: {unit_data}")
            if unit_id:
                try:
                    unit = Unit.objects.get(id=unit_id, item=item)
                    for attr, value in unit_data.items():
                        if attr != 'id':
                            setattr(unit, attr, value)
                    unit.save()
                    log.debug(f"更新单位: {unit}")
                except Unit.DoesNotExist:
                    log.error(f"物品 {item.id} 下不存在单位 {unit_id}，禁止伪造id重复创建！")
                    raise WmsException(_(f"物品 {item.id} 下不存在单位 {unit_id}"))
            else:
                # 检查 item+unit_type 是否已存在，防止重复创建
                unit_type_id = unit_data.get('unit_type_id') or unit_data.get('unit_type')
                if Unit.objects.filter(item=item, unit_type_id=unit_type_id).exists():
                    log.error(f"物品 {item.id} 已存在该类型单位，禁止重复创建！")
                    raise WmsException(_(f"物品 {item.id} 已存在该类型单位，禁止重复创建！"))
                unit = Unit.objects.create(item=item, **unit_data)
                log.debug(f"新建单位: {unit}")
            existing_ids.add(unit.id)
            if main_unit is None:
                main_unit = unit

        self._validate_and_set_main_unit(item, main_unit)

        # 删除不再存在的单元
        to_delete = current_ids - existing_ids
        log.debug(f"current_ids: {current_ids} existing_ids: {existing_ids} to_delete: {to_delete}")
        if to_delete:
            Unit.objects.filter(id__in=to_delete).delete()

        # 处理库存更新逻辑
        if stocks_data:
            # 检查该物品是否已有库存记录
            has_existing_stock = Stock.objects.filter(item=item).exists()
            if has_existing_stock:
                raise WmsException(_("该物品已有库存记录，不能更新库存"))
            
            # 如果没有库存记录，则可以创建初始库存
            for stock_data in stocks_data:
                self._create_initial_stock(item, stock_data)
        
        return item



    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        搜索物品（按名称、商品编号、序列号、规格型号模糊查询）
        """
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=1, msg='请输入搜索关键词', status=status.HTTP_400_BAD_REQUEST)

        items = Item.objects.filter(
            models.Q(name__icontains=query) |
            models.Q(code__icontains=query) |
            models.Q(serial_number__icontains=query) |
            models.Q(model__icontains=query)
        ).filter(is_active=True)
        serializer = self.get_serializer(items, many=True)
        # 增加物品类型名称
        data = serializer.data
        for idx, item in enumerate(items):
            data[idx]['item_type_name'] = item.item_type.name if item.item_type else ''
        return make_response(code=0, msg='搜索成功', status=status.HTTP_200_OK, data=data)
    
    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        """
        删除物品（支持单个物品删除，并做相关检查）
        """
        instance = self.get_object()
        
        # 1. 检查是否有库存
        if instance.total_stock > 0:
            return make_response(code=1, 
                                msg=_('该物品有库存，不能删除'),)
        
        # 2. 检查是否有未完成的订单需求
        if instance.order_demand > 0:
            return make_response(code=1, 
                                msg=_('该物品有未完成的订单需求，不能删除'),)
        
        # 3. 检查是否有库存记录（包括历史库存）
        if Stock.objects.filter(item=instance).exists():
            return make_response(code=1, 
                                msg=_('该物品有库存记录，不能删除'),)
        
        # 4. 检查是否有库存历史记录
        if StockHistory.objects.filter(item=instance).exists():
            return make_response(code=1, 
                                msg=_('该物品有库存历史记录，不能删除'),)
        
        # 5. 检查是否有采购订单物品记录
        if hasattr(instance, 'purchaseorderitem_set') and instance.purchaseorderitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有采购订单记录，不能删除'),)
        
        # 6. 检查是否有销售订单物品记录
        if hasattr(instance, 'salesorderitem_set') and instance.salesorderitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有销售订单记录，不能删除'),)
        
        # 7. 检查是否有销售出库物品记录
        if hasattr(instance, 'salesoutitem_set') and instance.salesoutitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有销售出库记录，不能删除'),)
        
        # 8. 检查是否有销售退货物品记录
        if hasattr(instance, 'salesreturnitem_set') and instance.salesreturnitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有销售退货记录，不能删除'),)
        
        # 9. 检查是否有采购入库物品记录
        if hasattr(instance, 'purchaseinitem_set') and instance.purchaseinitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有采购入库记录，不能删除'),)
        
        # 10. 检查是否有退货物品记录
        if hasattr(instance, 'stockreturnitem_set') and instance.stockreturnitem_set.exists():
            return make_response(code=1, 
                                msg=_('该物品有退货记录，不能删除'),)
        
        # 先将主计量单位设为 None，避免外键约束冲突
        instance.unit = None
        instance.save()
        
        # 删除所有关联的计量单位
        if instance.units.exists():
            instance.units.all().delete()
        
        instance.delete()
        return make_response(code=0, msg=_('物品删除成功'), data={}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def gen_code(self, request):
        try:
            code = generate_item_code()
            return make_response(code=0, msg=_('生成成功'), data={'code': code}, status=status.HTTP_200_OK)
        except Exception as e:
            return make_response(code=1, msg=str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def list(self, request, *args, **kwargs):
        """获取物品列表，支持按仓库过滤"""
        warehouse_id = request.query_params.get('warehouse_id')
        
        if warehouse_id:
            try:
                warehouse_id = int(warehouse_id)
                # 验证仓库是否存在
                if not Warehouse.objects.filter(id=warehouse_id).exists():
                    return make_response(code=1, msg=_('指定的仓库不存在'))
                
                # 获取该仓库中有库存的物品
                queryset = self.filter_queryset(self.get_queryset())
                
                # 手动添加库存信息到序列化器上下文
                page = self.paginate_queryset(queryset)
                if page is not None:
                    serializer = self.get_serializer(page, many=True, context={
                        **self.get_serializer_context(),
                        'warehouse_id': warehouse_id
                    })
                    return self.get_paginated_response(serializer.data)
                
                serializer = self.get_serializer(queryset, many=True, context={
                    **self.get_serializer_context(),
                    'warehouse_id': warehouse_id
                })
                return make_response(code=0, msg=_('获取成功'), data=serializer.data, status=status.HTTP_200_OK)
                
            except (ValueError, TypeError):
                return make_response(code=1, msg=_('仓库ID格式错误'))
        
        # 默认列表逻辑
        return super().list(request, *args, **kwargs)

class UnitViewSet(SafeModelViewSet):
    queryset = Unit.objects.select_related('unit_type', 'item').all()
    serializer_class = UnitSerializer
    pagination_class = StandardResultsSetPagination

    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        搜索计量单位
        支持按单位名称、物品名称模糊查找
        """
        query = request.query_params.get('query', '')
        if not query:
            return make_response(code=1, msg=_('请输入搜索关键词'))
        units = Unit.objects.filter(
            models.Q(unit_type__name__icontains=query) |
            models.Q(item__name__icontains=query)
        ).filter(item__is_active=True).select_related('unit_type', 'item')
        serializer = self.get_serializer(units, many=True)
        return make_response(code=0, msg=_('搜索成功'), data=serializer.data, status=status.HTTP_200_OK)

    
