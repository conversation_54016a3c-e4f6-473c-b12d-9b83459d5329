import time
from django.conf import settings
import requests
from WmsCore.models import ZTUser
from common.auth.JWT import <PERSON><PERSON><PERSON>ana<PERSON>
from rest_framework.exceptions import AuthenticationFailed, NotAuthenticated
from rest_framework import authentication
from django.utils.translation import gettext_lazy as _
import jwt
from common.logger import logger as log
from common.logger import set_current_user

jwt_manager = JWTManager()

class JWTWmsCoreAuthentication(authentication.BaseAuthentication):
    """JWT认证类"""
    def authenticate_header(self, request):
        return 'Bearer'

    def authenticate(self, request):

        try:
            # 从请求头中获取令牌
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if not auth_header:
                raise NotAuthenticated(_('未提供认证令牌') )
            # 提取令牌
            token_type, token = auth_header.split()
            if token_type.lower() != 'bearer':
                log.error(f"Invalid auth type: {token_type}")
                raise AuthenticationFailed(_('无效的认证方式') )

            # 验证令牌
            payload = jwt_manager.verify_token(token)
            user_id = payload.get('user_id')
            request.user_id = user_id
            set_current_user(user_id)

            account_type = payload.get('account_type')

            #log.debug(f"Authenticated user_id: {user_id} account_type: {account_type}")

            if not user_id or not account_type:
                raise AuthenticationFailed(_('无效的令牌载荷'))

            # 根据账号类型获取用户
            if account_type == 'platform':
                from AccessGateway.models import CXZUser
                user:CXZUser = CXZUser.objects.get(id=user_id)
                if not user.mobile:
                    log.error(f"Authenticated user: {user_id} not bind mobile user: {user}")
                    raise AuthenticationFailed(_('not_bind_mobile') )
                if not user.enterprise_id:
                    log.error(f"Authenticated user: {user_id} not bind enterprise user: {user}")
                    raise AuthenticationFailed(_('not_bind_enterprise') )

                zt_user = ZTUser.objects.filter(user_id=user_id).first()
                if not zt_user:
                    zt_user = ZTUser.objects.create(user_id=user_id,
                                                    name=user.nick_name,
                                                    roles=[ZTUser.RoleTypeChoices.ADMIN])
                request.user_enterprise = user.enterprise_id;
                request.zt_user = zt_user;
                request.user = user;
            else:
                raise AuthenticationFailed(_('无效的账号类型'), code='invalid_account_type')

            return (user, None)

        except (ValueError, jwt.PyJWTError) as e:
            import traceback
            traceback.print_exc()
            log.warning(f"Authentication failed: {str(e)}")
            raise AuthenticationFailed(_('无效的认证令牌'))
        except AuthenticationFailed as e:
            #log.debug(f"Authentication failed: {str(e)}")
            raise e
        except Exception as e:
            import traceback
            traceback.print_exc()
            log.warning(f"Unexpected error during authentication: {str(e)}")
            raise AuthenticationFailed(_('认证过程发生错误'))
