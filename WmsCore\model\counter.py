from django.db import models


class CounterTable(models.Model):
    """Counter model for storing daily counters"""
    prefix = models.CharField(max_length=255, unique=True, help_text="Counter prefix identifier")
    mid_key = models.CharField(max_length=20, help_text="mid key")
    counter_value = models.IntegerField(default=0, help_text="Current counter value")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'counter'

    def __str__(self):
        return f"{self.prefix}_{self.mid_key}: {self.counter_value}" 