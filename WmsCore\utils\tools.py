class CommonTools:
    @staticmethod
    def generate_short_desc(items_data):
        """生成简要描述：前三个不重复的物品名称，用、分隔，超过三个加..."""
        unique_item_names = []
        seen_names = set()

        for item_data in items_data:
            # 支持字典格式和模型对象格式
            if hasattr(item_data, 'item'):
                # 模型对象格式 (如 PurchaseOrderItem)
                item_name = item_data.item.name
            elif isinstance(item_data, dict) and 'item' in item_data:
                # 字典格式
                item_name = item_data['item'].name
            else:
                continue

            if item_name not in seen_names:
                unique_item_names.append(item_name)
                seen_names.add(item_name)
                if len(unique_item_names) >= 3:
                    break

        # 判断是否需要添加省略号
        all_item_names = []
        for item_data in items_data:
            if hasattr(item_data, 'item'):
                all_item_names.append(item_data.item.name)
            elif isinstance(item_data, dict) and 'item' in item_data:
                all_item_names.append(item_data['item'].name)

        total_unique_items = len(set(all_item_names))
        if total_unique_items > 3:
            return '、'.join(unique_item_names) + '...'
        else:
            return '、'.join(unique_item_names)