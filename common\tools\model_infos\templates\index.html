<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型信息查看器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .model-list {
            max-height: 600px;
            overflow-y: auto;
        }
        .model-item {
            cursor: pointer;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .model-item:hover {
            background-color: #f8f9fa;
        }
        .model-item.active {
            background-color: #e9ecef;
        }
        .field-table {
            margin-top: 20px;
        }
        #hostSelect {
            width: 200px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">模型列表</h5>
                        <select class="form-select mt-2" id="hostSelect">
                            {% for name, host in hosts.items() %}
                            <option value="{{ name }}">{{ name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="card-body p-0 model-list" id="modelList">
                        <!-- 模型列表将在这里动态加载 -->
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0" id="selectedModelTitle">字段信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered field-table">
                                <thead>
                                    <tr>
                                        <th>字段名</th>
                                        <th>类型</th>
                                        <th>标签</th>
                                        <th>只读</th>
                                        <th>选项 (Choices)</th>
                                    </tr>
                                </thead>
                                <tbody id="fieldList">
                                    <!-- 字段信息将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const fieldTypeMap = {
            "CharField": "char",
            "IntegerField": "int",
            "DecimalField": "float",
            "BooleanField": "bool",
            "DateTimeField": "datetime",
            "DateField": "date",
            "TimeField": "time",
            "FileField": "file",
            "ChoiceField": "choice",
            "PrimaryKeyRelatedField": "key_related",
        };

        // 获取当前选择的环境
        function getCurrentHost() {
            return document.getElementById('hostSelect').value;
        }

        // 保存环境选择到本地存储
        function saveHostSelection(host) {
            localStorage.setItem('selectedHost', host);
        }

        // 从本地存储加载环境选择
        function loadHostSelection() {
            const savedHost = localStorage.getItem('selectedHost');
            if (savedHost) {
                const hostSelect = document.getElementById('hostSelect');
                if (Array.from(hostSelect.options).some(option => option.value === savedHost)) {
                    hostSelect.value = savedHost;
                }
            }
        }

        // 加载模型列表
        async function loadModelList() {
            try {
                const host = getCurrentHost();
                const response = await fetch(`/api/models?host=${encodeURIComponent(host)}`);
                const data = await response.json();
                const modelList = document.getElementById('modelList');
                modelList.innerHTML = '';

                if (data.error) {
                    modelList.innerHTML = `<div class="p-3 text-danger">加载失败: ${data.error}</div>`;
                    return;
                }

                data.model_names.forEach(model => {
                    const div = document.createElement('div');
                    div.className = 'model-item';
                    const displayName = model.alias ? `${model.alias}(${model.name})` : model.name;
                    div.textContent = displayName;
                    div.onclick = () => loadModelDetails(model.name, displayName);
                    modelList.appendChild(div);
                });
            } catch (error) {
                console.error('加载模型列表失败:', error);
                const modelList = document.getElementById('modelList');
                modelList.innerHTML = `<div class="p-3 text-danger">加载失败: ${error.message}</div>`;
            }
        }

        // 加载模型详情
        async function loadModelDetails(modelName, displayName) {
            try {
                // 更新选中状态
                document.querySelectorAll('.model-item').forEach(item => {
                    item.classList.remove('active');
                    if (item.textContent === displayName) {
                        item.classList.add('active');
                    }
                });

                // 更新标题
                document.getElementById('selectedModelTitle').textContent = `字段信息 - ${displayName}`;

                const host = getCurrentHost();
                const response = await fetch(`/api/model/${modelName}?host=${encodeURIComponent(host)}`);
                const data = await response.json();

                if (data.error) {
                    document.getElementById('fieldList').innerHTML = `<tr><td colspan="5" class="text-danger">加载失败: ${data.error}</td></tr>`;
                    return;
                }

                const fieldList = document.getElementById('fieldList');
                fieldList.innerHTML = '';

                data.details.forEach(field => {
                    const row = document.createElement('tr');
                    
                    // 字段名
                    row.innerHTML += `<td>${field.field_name}</td>`;
                    
                    // 类型
                    const fieldType = fieldTypeMap[field.field_type] || field.field_type;
                    row.innerHTML += `<td>${fieldType}</td>`;
                    
                    // 标签
                    row.innerHTML += `<td>${field.label || ''}</td>`;
                    
                    // 只读
                    row.innerHTML += `<td>${field.read_only ? '是' : '否'}</td>`;
                    
                    // 选项
                    let choicesStr = '';
                    if (field.field_type === 'ChoiceField' && field.choices) {
                        const choices = {};
                        field.choices.forEach(choice => {
                            choices[choice.value] = choice.display_name;
                        });
                        choicesStr = JSON.stringify(choices, null, 2);
                    }
                    row.innerHTML += `<td>${choicesStr}</td>`;
                    
                    fieldList.appendChild(row);
                });
            } catch (error) {
                console.error('加载模型详情失败:', error);
                document.getElementById('fieldList').innerHTML = `<tr><td colspan="5" class="text-danger">加载失败: ${error.message}</td></tr>`;
            }
        }

        // 监听环境选择变化
        document.getElementById('hostSelect').addEventListener('change', (event) => {
            const selectedHost = event.target.value;
            saveHostSelection(selectedHost);
            loadModelList();
        });

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', () => {
            loadHostSelection();
            loadModelList();
        });
    </script>
</body>
</html> 