import datetime
from datetime import timedelta
from typing import Dict, Any, Optional

import jwt
from django.conf import settings
from rest_framework.exceptions import AuthenticationFailed, APIException

from common.db.Redis import RedisUtil
from common.logger import logger as log
from django.utils.translation import gettext_lazy as _

class JWTManager:
    """
    JWT管理器类

    负责处理所有JWT相关的操作，包括令牌的创建、验证和刷新。
    使用配置中的密钥和算法进行令牌的签名和验证。
    """

    def __init__(self):
        """
        初始化JWT管理器

        从配置中加载JWT相关设置
        """
        # 使用配置中的SECRET_KEY和JWT_ALGORITHM
        self.secret_key = getattr(settings, 'JWT_SECRET_KEY', settings.SECRET_KEY)
        self.algorithm = getattr(settings, 'JWT_ALGORITHM', 'HS256')
        self.access_token_expire_days = getattr(settings, 'JWT_EXPIRATION', 30)  # 默认30天
        self.refresh_token_expire_days = getattr(settings, 'JWT_REFRESH_EXPIRATION', 90)  # 默认90天

    def create_token(self, data: Dict[str, Any], expires_delta: Optional[int] = None) -> str:
        """
        创建JWT令牌

        生成包含用户数据的访问令牌，可以指定过期时间

        Args:
            data: 要包含在令牌中的用户数据
            expires_delta: 令牌过期时间（天），默认使用配置值

        Returns:
            str: 编码后的JWT令牌

        Raises:
            Exception: 当令牌创建失败时抛出
        """
        try:
            # 准备令牌数据
            to_encode = data.copy()

            # 设置过期时间
            if expires_delta is None:
                expires_delta = self.access_token_expire_days
            expire = datetime.datetime.utcnow() + timedelta(days=expires_delta)

            # 添加过期时间到令牌数据
            to_encode.update({"exp": expire})

            # 创建令牌
            encoded_jwt = jwt.encode(
                to_encode,
                self.secret_key,
                algorithm=self.algorithm
            )

            log.debug(f"Created token for user: {data.get('user_id')}")

            # 将token存入Redis，用于token撤销和会话管理
            shot_len = 24 
            while True:
                shot_token = encoded_jwt[-shot_len:]
                token_key = f"access_token:{shot_token}"
                if not RedisUtil.has_key(token_key):
                    break
                shot_len += 2
                if shot_len > 64:
                    raise Exception(f"Token creation failed: {str(e)}")
            RedisUtil.set(token_key, encoded_jwt, timeout=expires_delta*24*60*60)

            # 保存用户当前的访问令牌，方便之后撤销
            user_id = data.get('user_id')
            account_type = data.get('account_type')
            if user_id and account_type:
                user_token_key = f"user_token:{account_type}:{user_id}"
                # 先检查是否有旧令牌，如果有则撤销
                old_token = RedisUtil.get(user_token_key)
                if old_token:
                    log.info(f"撤销用户 {user_id} 的旧访问令牌")
                    self.revoke_token(old_token)
                # 保存新令牌
                RedisUtil.set(user_token_key, shot_token, timeout=expires_delta*24*60*60)

            return shot_token

        except Exception as e:
            log.error(f"Error creating token - user_id: {data.get('user_id')}")
            raise Exception(f"Token creation failed: {str(e)}")

    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """
        创建刷新令牌

        生成用于刷新访问令牌的长期令牌

        Args:
            data: 要包含在令牌中的用户数据

        Returns:
            str: 编码后的刷新令牌
        """
        try:
            to_encode = data.copy()
            expire = datetime.datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
            to_encode.update({"exp": expire})
            encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

            # 将refresh token存入Redis
            shot_len = 24 
            while True:
                shot_token = encoded_jwt[-shot_len:]
                token_key = f"refresh_token:{shot_token}"
                if not RedisUtil.has_key(token_key):
                    break
                shot_len += 2
                if shot_len > 64:
                    raise Exception(f"refresh Token creation failed: {str(e)}")
            RedisUtil.set(token_key, encoded_jwt, timeout=self.refresh_token_expire_days*24*60*60)

            user_id = data.get('user_id')
            account_type = data.get('account_type')
            if user_id and account_type:
                user_token_key = f"user_refresh_token:{account_type}:{user_id}"
                old_token = RedisUtil.get(user_token_key)
                if old_token:
                    log.debug(f"主动撤销用户 {user_id} 的旧刷新令牌")
                    self.revoke_refresh_token(old_token)
                RedisUtil.set(user_token_key, shot_token, timeout=self.refresh_token_expire_days*24*60*60)

            log.debug(f"Created refresh token for user: {data.get('user_id')}")
            return shot_token

        except Exception as e:
            log.error(f"Error creating refresh token - user_id: {data.get('user_id')}")
            raise Exception(f"Refresh token creation failed: {str(e)}")

    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """
        验证JWT令牌

        验证令牌的签名和过期时间，返回令牌中的用户数据

        Args:
            token: 要验证的JWT令牌
            token_type: 令牌类型，"access"表示访问令牌，"refresh"表示刷新令牌

        Returns:
            Dict[str, Any]: 令牌中的用户数据

        Raises:
            AuthenticationFailed: 当令牌无效或过期时抛出
        """
        try:
            # 检查token是否在Redis中（是否被撤销）
            token_key = f"{token_type}_token:{token}"
            if not RedisUtil.has_key(token_key):
                log.warning(f"{token_type.capitalize()} token has been revoked: {token[:10]}...")
                raise AuthenticationFailed(f"{token_type.capitalize()} token has been revoked")
            jwt_token = RedisUtil.get(token_key)

            # 解码并验证令牌
            payload = jwt.decode(
                jwt_token,
                self.secret_key,
                algorithms=[self.algorithm]
            )

            # 获取用户标识
            user_id = payload.get("user_id")
            if user_id is None:
                log.warning(f"Invalid {token_type} token payload: {token[:10]}...")
                raise AuthenticationFailed(f"Invalid {token_type} token payload")

            #log.debug(f"Verified {token_type} token for user: {user_id}")
            return payload

        except jwt.ExpiredSignatureError:
            # 处理令牌过期
            log.warning(f"{token_type.capitalize()} token has expired: {token[:10]}...")
            raise AuthenticationFailed(f"{token_type.capitalize()} token has expired")
        except jwt.PyJWTError as e:
            # 处理其他JWT错误
            log.warning(f"JWT verification failed - token_prefix: {token[:10]}, token_type: {token_type}")
            raise AuthenticationFailed(f"Invalid {token_type} token")
        except Exception as e:
            # 处理其他错误
            log.warning("Unexpected error during token verification")
            raise APIException("Token verification failed")

    def verify_refresh_token(self, token: str) -> Dict[str, Any]:
        """
        验证刷新令牌

        专用方法验证刷新令牌的有效性

        Args:
            token: 要验证的刷新令牌

        Returns:
            Dict[str, Any]: 令牌中的用户数据

        Raises:
            AuthenticationFailed: 当令牌无效或过期时抛出
        """
        return self.verify_token(token, token_type="refresh")

    def revoke_token(self, token: str) -> None:
        """
        撤销访问令牌

        从Redis中删除令牌，使其失效

        Args:
            token: 要撤销的令牌
        """
        try:
            token_key = f"access_token:{token}"
            #给多 5 分钟有效期
            RedisUtil.expire(token_key, 5*60)
            #RedisUtil.delete(token_key)
            log.info(f"Token revoked: {token[:10]} after 5 minutes...")
        except Exception as e:
            log.warning(f"Error revoking token - token_prefix: {token[:10]}")

    def revoke_refresh_token(self, token: str) -> None:
        """
        撤销刷新令牌

        从Redis中删除刷新令牌，使其失效

        Args:
            token: 要撤销的刷新令牌
        """
        try:
            token_key = f"refresh_token:{token}"
            RedisUtil.delete(token_key)
            log.info(f"Refresh token revoked: {token[:10]}...")
        except Exception as e:
            log.warning(f"Error revoking refresh token - token_prefix: {token[:10]}")

    def refresh_access_token(self, refresh_token: str) -> str:
        """
        刷新访问令牌

        使用有效的刷新令牌生成新的访问令牌，并自动撤销该用户的旧访问令牌

        Args:
            refresh_token: 有效的刷新令牌

        Returns:
            str: 新的访问令牌

        Raises:
            AuthenticationFailed: 当刷新令牌无效或过期时抛出
        """
        # 验证刷新令牌
        payload = self.verify_refresh_token(refresh_token)

        # 获取用户信息
        user_id = payload.get('user_id')
        account_type = payload.get('account_type')

        # 生成新的访问令牌（create_token方法会自动撤销旧令牌）
        return self.create_token({
            'user_id': user_id,
            'account_type': account_type
        })

    def generate_token(self, user_id, account_type):
        """
        生成访问令牌和刷新令牌

        为指定用户生成一对令牌

        Args:
            user_id: 用户ID
            account_type: 账号类型（'platform'或'system'）

        Returns:
            Dict[str, str]: 包含访问令牌和刷新令牌的字典
        """
        # 准备令牌数据
        token_data = {
            'user_id': user_id,
            'account_type': account_type
        }
        log.info(f"Generating tokens for user_id={user_id}, account_type={account_type}")

        # 生成访问令牌
        access_token = self.create_token(token_data)

        # 生成刷新令牌
        refresh_token = self.create_refresh_token(token_data)

        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }


jwt_manager = JWTManager()

class JWTUtil:
    """JWT工具类（向后兼容）"""

    @staticmethod
    def generate_token(user_id, account_type):
        """
        生成JWT令牌
        :param user_id: 用户ID
        :param account_type: 账号类型
        :return: 包含访问令牌和刷新令牌的字典
        """
        return jwt_manager.generate_token(user_id, account_type)

    @staticmethod
    def verify_token(token, token_type="access"):
        """
        验证JWT令牌
        :param token: JWT令牌
        :param token_type: 令牌类型，"access"表示访问令牌，"refresh"表示刷新令牌
        :return: 载荷信息
        """
        return jwt_manager.verify_token(token, token_type)

    @staticmethod
    def verify_refresh_token(token):
        """
        验证刷新令牌
        :param token: 刷新令牌
        :return: 载荷信息
        """
        return jwt_manager.verify_refresh_token(token)

    @staticmethod
    def refresh_access_token(refresh_token):
        """
        刷新访问令牌

        使用有效的刷新令牌生成新的访问令牌，并自动撤销该用户的旧访问令牌

        :param refresh_token: 刷新令牌
        :return: 新的访问令牌
        """
        return jwt_manager.refresh_access_token(refresh_token)

    @staticmethod
    def revoke_token(token):
        """
        撤销访问令牌
        :param token: JWT令牌
        """
        jwt_manager.revoke_token(token)

    @staticmethod
    def revoke_refresh_token(token):
        """
        撤销刷新令牌
        :param token: 刷新令牌
        """
        jwt_manager.revoke_refresh_token(token)
